#!/bin/bash

# 🚀 DÉMARRAGE RAPIDE SPRINT 16 - MENU INTERACTIF
# Interface simple pour exécuter les actions du Sprint 16
# Basé sur doc/audit-roadmap-sprints-finaux.md

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Banner principal
print_main_banner() {
    clear
    echo -e "${PURPLE}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════════════════════╗
║                        🚀 SPRINT 16 - DÉMARRAGE RAPIDE                      ║
║                         TESTS E2E & PERFORMANCE                             ║
║                                                                              ║
║  📅 Période: 11-24 Juin 2025 (2 semaines)                                 ║
║  🎯 Objectif: Tests E2E 100% + Performance <100ms P95                      ║
║  📊 Basé sur: doc/audit-roadmap-sprints-finaux.md                          ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# Menu principal
show_main_menu() {
    echo -e "${CYAN}📋 MENU PRINCIPAL - SPRINT 16${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} 🧪 Tests E2E - Configuration Playwright (Semaine 1)"
    echo -e "${GREEN}2.${NC} ⚡ Performance - Optimisations complètes (Semaine 2)"
    echo -e "${GREEN}3.${NC} 🚀 Exécution complète Sprint 16 (Semaines 1+2)"
    echo -e "${GREEN}4.${NC} ✅ Validation et rapport de progression"
    echo ""
    echo -e "${YELLOW}Actions individuelles:${NC}"
    echo -e "${BLUE}5.${NC} 🎭 Configuration Playwright uniquement"
    echo -e "${BLUE}6.${NC} 🧪 Exécution tests E2E uniquement"
    echo -e "${BLUE}7.${NC} ⚡ Optimisation performance uniquement"
    echo -e "${BLUE}8.${NC} 📊 Tests de performance uniquement"
    echo ""
    echo -e "${PURPLE}Utilitaires:${NC}"
    echo -e "${CYAN}9.${NC} 📊 Voir l'état actuel du projet"
    echo -e "${CYAN}10.${NC} 📚 Afficher la documentation Sprint 16"
    echo -e "${CYAN}0.${NC} ❌ Quitter"
    echo ""
    echo -e "${YELLOW}Choisissez une option (0-10):${NC} "
}

# Fonction pour afficher l'état du projet
show_project_status() {
    echo -e "${CYAN}📊 ÉTAT ACTUEL DU PROJET - SPRINT 16${NC}"
    echo "========================================"
    
    # Vérifier les scripts
    echo -e "${BLUE}Scripts Sprint 16 disponibles:${NC}"
    local scripts=(
        "start-sprint-16.sh"
        "setup-playwright.sh"
        "run-e2e-tests.sh"
        "optimize-performance.sh"
        "validate-sprint16-progress.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ -f "$PROJECT_ROOT/scripts/$script" ]]; then
            echo -e "  ✅ $script"
        else
            echo -e "  ❌ $script"
        fi
    done
    
    echo ""
    echo -e "${BLUE}Configuration E2E:${NC}"
    
    if [[ -f "$PROJECT_ROOT/e2e-tests/playwright.config.ts" ]]; then
        echo -e "  ✅ Configuration Playwright"
    else
        echo -e "  ❌ Configuration Playwright (à créer)"
    fi
    
    if [[ -d "$PROJECT_ROOT/e2e-tests/tests" ]]; then
        echo -e "  ✅ Tests E2E créés"
        local test_count=$(find "$PROJECT_ROOT/e2e-tests/tests" -name "*.spec.ts" 2>/dev/null | wc -l)
        echo -e "     📊 Nombre de tests: $test_count"
    else
        echo -e "  ❌ Tests E2E (à créer)"
    fi
    
    if [[ -f "$PROJECT_ROOT/.github/workflows/e2e-tests.yml" ]]; then
        echo -e "  ✅ CI/CD E2E configuré"
    else
        echo -e "  ❌ CI/CD E2E (à configurer)"
    fi
    
    echo ""
    echo -e "${BLUE}Optimisations Performance:${NC}"
    
    if [[ -f "$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main/vite.config.ts" ]]; then
        if grep -q "chunkSizeWarningLimit.*500" "$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main/vite.config.ts" 2>/dev/null; then
            echo -e "  ✅ Configuration Vite optimisée"
        else
            echo -e "  ⚠️  Configuration Vite (optimisation partielle)"
        fi
    else
        echo -e "  ❌ Configuration Vite (à optimiser)"
    fi
    
    if [[ -f "$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main/src/utils/lazyLoad.tsx" ]]; then
        echo -e "  ✅ Lazy loading implémenté"
    else
        echo -e "  ❌ Lazy loading (à implémenter)"
    fi
    
    if [[ -f "$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main/src/components/OptimizedImage.tsx" ]]; then
        echo -e "  ✅ Images optimisées"
    else
        echo -e "  ❌ Images optimisées (à créer)"
    fi
    
    if [[ -f "$PROJECT_ROOT/Projet-RB2/Backend-NestJS/src/cache/cache.service.ts" ]]; then
        echo -e "  ✅ Cache Redis configuré"
    else
        echo -e "  ❌ Cache Redis (à configurer)"
    fi
    
    echo ""
    echo -e "${BLUE}Prérequis Sprint 15:${NC}"
    
    if [[ -f "$PROJECT_ROOT/.env.vault" ]]; then
        echo -e "  ✅ Gestion des secrets (Sprint 15)"
    else
        echo -e "  ❌ Sprint 15 requis - Exécutez d'abord Sprint 15"
    fi
    
    if [[ -d "$PROJECT_ROOT/design-system" ]]; then
        echo -e "  ✅ Design System (Sprint 15)"
    else
        echo -e "  ❌ Sprint 15 requis - Design System manquant"
    fi
    
    echo ""
    read -p "Appuyez sur Entrée pour continuer..."
}

# Fonction pour afficher la documentation
show_documentation() {
    echo -e "${CYAN}📚 DOCUMENTATION SPRINT 16${NC}"
    echo "============================"
    echo ""
    echo -e "${GREEN}🎯 OBJECTIFS SPRINT 16:${NC}"
    echo "• Tests E2E 100% des user journeys critiques"
    echo "• Performance <100ms P95 + Lighthouse >95"
    echo "• Cross-browser testing (Chrome, Firefox, Safari, Mobile)"
    echo "• Bundle optimization <500KB (-40%)"
    echo ""
    echo -e "${GREEN}📋 SEMAINE 1 - TESTS E2E:${NC}"
    echo "• Configuration Playwright unifiée multi-browser"
    echo "• Tests d'authentification et réservation"
    echo "• Tests cross-browser (4 environnements)"
    echo "• Intégration CI/CD GitHub Actions"
    echo ""
    echo -e "${GREEN}📋 SEMAINE 2 - PERFORMANCE:${NC}"
    echo "• Lazy loading global (composants et pages)"
    echo "• Optimisation bundle size <500KB"
    echo "• Cache strategy avancée (Redis multi-niveau)"
    echo "• Database query optimization"
    echo ""
    echo -e "${GREEN}🏆 LIVRABLES FINAUX:${NC}"
    echo "• ✅ Tests E2E 100% coverage"
    echo "• ✅ Performance <100ms P95"
    echo "• ✅ Cross-browser compatibility"
    echo "• ✅ Bundle optimization -40%"
    echo ""
    echo -e "${YELLOW}📁 FICHIERS CLÉS:${NC}"
    echo "• e2e-tests/playwright.config.ts - Configuration Playwright"
    echo "• e2e-tests/tests/ - Tests E2E par catégorie"
    echo "• vite.config.ts - Configuration optimisée"
    echo "• src/utils/lazyLoad.tsx - Utilitaire lazy loading"
    echo ""
    echo -e "${YELLOW}🧪 TESTS CRÉÉS:${NC}"
    echo "• tests/auth/login.spec.ts - Tests d'authentification"
    echo "• tests/booking/booking-flow.spec.ts - Tests de réservation"
    echo "• tests/utils/auth-helper.ts - Utilitaires de test"
    echo ""
    echo -e "${YELLOW}⚡ OPTIMISATIONS:${NC}"
    echo "• Frontend: Vite + lazy loading + images WebP"
    echo "• Backend: Redis cache + compression + rate limiting"
    echo "• Monitoring: Métriques P50/P95/P99"
    echo ""
    read -p "Appuyez sur Entrée pour continuer..."
}

# Fonction pour exécuter une action avec confirmation
execute_action() {
    local action_name="$1"
    local script_path="$2"
    local description="$3"
    
    echo ""
    echo -e "${YELLOW}🚀 EXÉCUTION: $action_name${NC}"
    echo -e "${BLUE}Description:${NC} $description"
    echo -e "${BLUE}Script:${NC} $script_path"
    echo ""
    
    read -p "Voulez-vous continuer? (o/N): " confirm
    
    if [[ $confirm =~ ^[Oo]$ ]]; then
        echo ""
        echo -e "${GREEN}▶️  Démarrage de $action_name...${NC}"
        echo "================================"
        
        if [[ -f "$script_path" ]]; then
            chmod +x "$script_path"
            "$script_path"
            local exit_code=$?
            
            echo ""
            if [[ $exit_code -eq 0 ]]; then
                echo -e "${GREEN}✅ $action_name terminé avec succès!${NC}"
            else
                echo -e "${RED}❌ $action_name terminé avec des erreurs (code: $exit_code)${NC}"
            fi
        else
            echo -e "${RED}❌ Script non trouvé: $script_path${NC}"
        fi
        
        echo ""
        read -p "Appuyez sur Entrée pour continuer..."
    else
        echo -e "${YELLOW}⏸️  Action annulée${NC}"
        sleep 1
    fi
}

# Boucle principale du menu
main_loop() {
    while true; do
        print_main_banner
        show_main_menu
        
        read -r choice
        
        case $choice in
            1)
                execute_action \
                    "Tests E2E - Semaine 1" \
                    "$PROJECT_ROOT/scripts/start-sprint-16.sh e2e" \
                    "Configuration Playwright et exécution des tests E2E multi-browser"
                ;;
            2)
                execute_action \
                    "Performance - Semaine 2" \
                    "$PROJECT_ROOT/scripts/start-sprint-16.sh performance" \
                    "Optimisations complètes frontend/backend pour <100ms P95"
                ;;
            3)
                execute_action \
                    "Sprint 16 Complet" \
                    "$PROJECT_ROOT/scripts/start-sprint-16.sh" \
                    "Exécution complète: Tests E2E (Semaine 1) + Performance (Semaine 2)"
                ;;
            4)
                execute_action \
                    "Validation Sprint 16" \
                    "$PROJECT_ROOT/scripts/validate-sprint16-progress.sh" \
                    "Validation des livrables et génération du rapport de progression"
                ;;
            5)
                execute_action \
                    "Configuration Playwright" \
                    "$PROJECT_ROOT/scripts/setup-playwright.sh" \
                    "Configuration Playwright multi-browser avec tests de base"
                ;;
            6)
                execute_action \
                    "Exécution Tests E2E" \
                    "$PROJECT_ROOT/scripts/run-e2e-tests.sh" \
                    "Exécution complète des tests E2E sur tous les navigateurs"
                ;;
            7)
                execute_action \
                    "Optimisation Performance" \
                    "$PROJECT_ROOT/scripts/optimize-performance.sh" \
                    "Optimisations frontend/backend pour performance maximale"
                ;;
            8)
                execute_action \
                    "Tests Performance" \
                    "$PROJECT_ROOT/scripts/run-e2e-tests.sh performance" \
                    "Tests de performance avec métriques P95 et Lighthouse"
                ;;
            9)
                show_project_status
                ;;
            10)
                show_documentation
                ;;
            0)
                echo -e "${GREEN}👋 Au revoir! Sprint 16 en cours...${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ Option invalide. Choisissez entre 0 et 10.${NC}"
                sleep 2
                ;;
        esac
    done
}

# Point d'entrée principal
main() {
    # Vérifier que nous sommes dans le bon répertoire
    if [[ ! -f "$PROJECT_ROOT/doc/audit-roadmap-sprints-finaux.md" ]]; then
        echo -e "${RED}❌ Erreur: Roadmap Sprint 16 non trouvée${NC}"
        echo -e "${YELLOW}Assurez-vous d'être dans le répertoire racine du projet${NC}"
        exit 1
    fi
    
    # Vérifier que Sprint 15 est terminé
    if [[ ! -f "$PROJECT_ROOT/.env.vault" ]] || [[ ! -d "$PROJECT_ROOT/design-system" ]]; then
        echo -e "${RED}❌ Erreur: Sprint 15 non terminé${NC}"
        echo -e "${YELLOW}Veuillez d'abord exécuter Sprint 15 avant Sprint 16${NC}"
        echo -e "${BLUE}Commande: ./scripts/quick-start-sprint15.sh${NC}"
        exit 1
    fi
    
    # Démarrer la boucle principale
    main_loop
}

# Gestion des arguments en ligne de commande
if [[ $# -gt 0 ]]; then
    case "$1" in
        "status")
            show_project_status
            ;;
        "doc")
            show_documentation
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [status|doc|help]"
            echo ""
            echo "Options:"
            echo "  status  - Afficher l'état du projet Sprint 16"
            echo "  doc     - Afficher la documentation Sprint 16"
            echo "  help    - Afficher cette aide"
            echo ""
            echo "Sans argument: Lance le menu interactif"
            ;;
        *)
            echo -e "${RED}❌ Option inconnue: $1${NC}"
            echo "Utilisez '$0 help' pour voir les options disponibles"
            exit 1
            ;;
    esac
else
    # Lancer le menu interactif
    main
fi
