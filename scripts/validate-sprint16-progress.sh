#!/bin/bash

# ✅ SCRIPT DE VALIDATION SPRINT 16 - TESTS E2E & PERFORMANCE
# Validation des livrables selon doc/audit-roadmap-sprints-finaux.md
# Période: 11-24 Juin 2025

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
VALIDATION_DIR="$PROJECT_ROOT/sprint-16-validation"
LOG_FILE="$VALIDATION_DIR/validation-$TIMESTAMP.log"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[✅ SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[⚠️  WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[❌ ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

header() {
    echo -e "${PURPLE}[VALIDATION]${NC} $1" | tee -a "$LOG_FILE"
}

# Créer le répertoire de validation
mkdir -p "$VALIDATION_DIR"

# Variables de score
TOTAL_CHECKS=0
PASSED_CHECKS=0

# Fonction pour incrémenter les compteurs
check_item() {
    local description="$1"
    local condition="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if eval "$condition"; then
        success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        error "$description"
        return 1
    fi
}

# Banner de validation
print_validation_banner() {
    echo -e "${PURPLE}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════════════════════╗
║                        ✅ VALIDATION SPRINT 16                               ║
║                         TESTS E2E & PERFORMANCE                             ║
║                                                                              ║
║  📋 Vérification des livrables selon la roadmap                            ║
║  🎯 Objectifs: Tests E2E 100% + Performance <100ms P95                     ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

print_validation_banner

log "✅ DÉMARRAGE VALIDATION SPRINT 16"
log "================================="
log "Projet: $PROJECT_ROOT"
log "Log: $LOG_FILE"

# 1. VALIDATION TESTS E2E (SEMAINE 1)
validate_e2e_tests() {
    header "🧪 1. VALIDATION TESTS E2E - SEMAINE 1"
    
    log "📋 Vérification des livrables tests E2E..."
    
    # 1.1 Configuration Playwright
    check_item "Configuration Playwright existe" \
        "[[ -f '$PROJECT_ROOT/e2e-tests/playwright.config.ts' ]]"
    
    check_item "Package.json E2E existe" \
        "[[ -f '$PROJECT_ROOT/e2e-tests/package.json' ]]"
    
    check_item "Script d'exécution E2E existe" \
        "[[ -f '$PROJECT_ROOT/scripts/run-e2e-tests.sh' ]]"
    
    check_item "Script de configuration Playwright existe" \
        "[[ -f '$PROJECT_ROOT/scripts/setup-playwright.sh' ]]"
    
    # 1.2 Tests de base
    check_item "Tests d'authentification créés" \
        "[[ -f '$PROJECT_ROOT/e2e-tests/tests/auth/login.spec.ts' ]]"
    
    check_item "Tests de réservation créés" \
        "[[ -f '$PROJECT_ROOT/e2e-tests/tests/booking/booking-flow.spec.ts' ]]"
    
    check_item "Utilitaires de test créés" \
        "[[ -f '$PROJECT_ROOT/e2e-tests/tests/utils/auth-helper.ts' ]]"
    
    # 1.3 Configuration CI/CD
    check_item "Workflow GitHub Actions E2E créé" \
        "[[ -f '$PROJECT_ROOT/.github/workflows/e2e-tests.yml' ]]"
    
    # 1.4 Scripts exécutables
    check_item "Scripts E2E sont exécutables" \
        "[[ -x '$PROJECT_ROOT/scripts/setup-playwright.sh' && -x '$PROJECT_ROOT/scripts/run-e2e-tests.sh' ]]"
    
    # 1.5 Structure des tests
    local test_dirs=(
        "e2e-tests/tests/auth"
        "e2e-tests/tests/booking"
        "e2e-tests/tests/utils"
    )
    
    local test_dirs_count=0
    for dir in "${test_dirs[@]}"; do
        if [[ -d "$PROJECT_ROOT/$dir" ]]; then
            test_dirs_count=$((test_dirs_count + 1))
        fi
    done
    
    check_item "Au moins 3/3 répertoires de tests créés" \
        "[[ $test_dirs_count -eq 3 ]]"
    
    log "   📊 Répertoires de tests: $test_dirs_count/3"
}

# 2. VALIDATION PERFORMANCE (SEMAINE 2)
validate_performance() {
    header "⚡ 2. VALIDATION PERFORMANCE - SEMAINE 2"
    
    log "📋 Vérification des optimisations de performance..."
    
    # 2.1 Scripts de performance
    check_item "Script d'optimisation performance existe" \
        "[[ -f '$PROJECT_ROOT/scripts/optimize-performance.sh' ]]"
    
    check_item "Script d'optimisation est exécutable" \
        "[[ -x '$PROJECT_ROOT/scripts/optimize-performance.sh' ]]"
    
    # 2.2 Optimisations Frontend
    local frontend_path="$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main"
    
    check_item "Configuration Vite optimisée existe" \
        "[[ -f '$frontend_path/vite.config.ts' ]]"
    
    check_item "Utilitaire lazy loading créé" \
        "[[ -f '$frontend_path/src/utils/lazyLoad.tsx' ]]"
    
    check_item "Composant image optimisée créé" \
        "[[ -f '$frontend_path/src/components/OptimizedImage.tsx' ]]"
    
    check_item "Composants lazy créés" \
        "[[ -f '$frontend_path/src/components/LazyComponents.tsx' ]]"
    
    # 2.3 Optimisations Backend
    local backend_path="$PROJECT_ROOT/Projet-RB2/Backend-NestJS"
    
    check_item "Service de cache Redis créé" \
        "[[ -f '$backend_path/src/cache/cache.service.ts' ]]"
    
    check_item "Intercepteur de performance créé" \
        "[[ -f '$backend_path/src/interceptors/query-performance.interceptor.ts' ]]"
    
    check_item "Middleware de compression créé" \
        "[[ -f '$backend_path/src/middleware/compression.middleware.ts' ]]"
    
    check_item "Configuration rate limiting créée" \
        "[[ -f '$backend_path/src/config/rate-limit.config.ts' ]]"
    
    # 2.4 Monitoring
    check_item "Service de monitoring créé" \
        "[[ -f '$PROJECT_ROOT/performance-optimization/monitoring/performance-monitor.ts' ]]"
    
    check_item "Répertoire d'optimisation performance créé" \
        "[[ -d '$PROJECT_ROOT/performance-optimization' ]]"
}

# 3. VALIDATION SCRIPTS ET OUTILS
validate_scripts() {
    header "🛠️  3. VALIDATION SCRIPTS ET OUTILS"
    
    log "📋 Vérification des scripts Sprint 16..."
    
    # 3.1 Scripts principaux
    check_item "Script start-sprint-16.sh existe" \
        "[[ -f '$PROJECT_ROOT/scripts/start-sprint-16.sh' ]]"
    
    check_item "Script de validation Sprint 16 existe" \
        "[[ -f '$PROJECT_ROOT/scripts/validate-sprint16-progress.sh' ]]"
    
    # 3.2 Permissions d'exécution
    local executable_scripts=0
    local scripts=(
        "start-sprint-16.sh"
        "setup-playwright.sh"
        "run-e2e-tests.sh"
        "optimize-performance.sh"
        "validate-sprint16-progress.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ -x "$PROJECT_ROOT/scripts/$script" ]]; then
            executable_scripts=$((executable_scripts + 1))
        fi
    done
    
    check_item "Au moins 4/5 scripts sont exécutables" \
        "[[ $executable_scripts -ge 4 ]]"
    
    log "   📊 Scripts exécutables: $executable_scripts/5"
    
    # 3.3 Dépendance Sprint 15
    check_item "Sprint 15 terminé (prérequis)" \
        "[[ -f '$PROJECT_ROOT/.env.vault' && -d '$PROJECT_ROOT/design-system' ]]"
}

# 4. VALIDATION STRUCTURE E2E
validate_e2e_structure() {
    header "🏗️  4. VALIDATION STRUCTURE E2E"
    
    log "📋 Vérification de la structure E2E..."
    
    # 4.1 Répertoires E2E
    local e2e_dirs=(
        "e2e-tests"
        "e2e-tests/tests"
        "e2e-tests/tests/auth"
        "e2e-tests/tests/booking"
        "e2e-tests/tests/admin"
        "e2e-tests/tests/api"
        "e2e-tests/tests/utils"
    )
    
    local e2e_dirs_count=0
    for dir in "${e2e_dirs[@]}"; do
        if [[ -d "$PROJECT_ROOT/$dir" ]]; then
            e2e_dirs_count=$((e2e_dirs_count + 1))
        fi
    done
    
    check_item "Au moins 5/7 répertoires E2E créés" \
        "[[ $e2e_dirs_count -ge 5 ]]"
    
    log "   📊 Répertoires E2E: $e2e_dirs_count/7"
    
    # 4.2 Fichiers de configuration
    check_item "TypeScript config E2E" \
        "[[ -f '$PROJECT_ROOT/e2e-tests/tsconfig.json' ]] || [[ -f '$PROJECT_ROOT/e2e-tests/playwright.config.ts' ]]"
    
    # 4.3 Scripts d'exécution E2E
    check_item "Script run-tests.sh dans e2e-tests" \
        "[[ -f '$PROJECT_ROOT/e2e-tests/run-tests.sh' ]]"
}

# 5. VALIDATION CONFORMITÉ ROADMAP
validate_roadmap_compliance() {
    header "📋 5. VALIDATION CONFORMITÉ ROADMAP"
    
    log "📋 Vérification conformité avec audit-roadmap-sprints-finaux.md..."
    
    # 5.1 Objectifs Sprint 16
    check_item "Objectif: Tests E2E 100% (structure créée)" \
        "[[ -d '$PROJECT_ROOT/e2e-tests' && -f '$PROJECT_ROOT/e2e-tests/playwright.config.ts' ]]"
    
    check_item "Objectif: Performance <100ms P95 (optimisations créées)" \
        "[[ -f '$PROJECT_ROOT/scripts/optimize-performance.sh' ]]"
    
    check_item "Objectif: Cross-browser testing (Playwright multi-browser)" \
        "grep -q 'chromium\\|firefox\\|webkit' '$PROJECT_ROOT/e2e-tests/playwright.config.ts' 2>/dev/null"
    
    check_item "Objectif: Bundle optimization <500KB (config Vite)" \
        "[[ -f '$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main/vite.config.ts' ]]"
    
    # 5.2 Livrables Semaine 1
    check_item "Livrable: Configuration Playwright unifiée" \
        "[[ -f '$PROJECT_ROOT/e2e-tests/playwright.config.ts' ]]"
    
    check_item "Livrable: Tests E2E tous microservices (structure)" \
        "[[ -d '$PROJECT_ROOT/e2e-tests/tests' ]]"
    
    check_item "Livrable: Tests cross-browser (config multi-browser)" \
        "grep -q 'projects:' '$PROJECT_ROOT/e2e-tests/playwright.config.ts' 2>/dev/null"
    
    check_item "Livrable: Intégration CI/CD (GitHub Actions)" \
        "[[ -f '$PROJECT_ROOT/.github/workflows/e2e-tests.yml' ]]"
    
    # 5.3 Livrables Semaine 2
    check_item "Livrable: Lazy loading global (utilitaire créé)" \
        "[[ -f '$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main/src/utils/lazyLoad.tsx' ]]"
    
    check_item "Livrable: Bundle optimization <500KB (config Vite)" \
        "grep -q 'chunkSizeWarningLimit.*500' '$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main/vite.config.ts' 2>/dev/null"
    
    check_item "Livrable: Cache strategy avancée (Redis service)" \
        "[[ -f '$PROJECT_ROOT/Projet-RB2/Backend-NestJS/src/cache/cache.service.ts' ]]"
    
    check_item "Livrable: Database query optimization (intercepteur)" \
        "[[ -f '$PROJECT_ROOT/Projet-RB2/Backend-NestJS/src/interceptors/query-performance.interceptor.ts' ]]"
}

# 6. GÉNÉRATION DU RAPPORT DE VALIDATION
generate_validation_report() {
    header "📊 6. GÉNÉRATION DU RAPPORT DE VALIDATION"
    
    local score_percentage=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    local report_file="$VALIDATION_DIR/validation-report-$TIMESTAMP.md"
    
    cat > "$report_file" << EOF
# ✅ RAPPORT DE VALIDATION SPRINT 16

**📅 Date:** $(date)  
**🎯 Sprint:** 16 - Tests E2E & Performance  
**📊 Score:** $PASSED_CHECKS/$TOTAL_CHECKS ($score_percentage%)  

## 📊 RÉSUMÉ EXÉCUTIF

### Score Global: $score_percentage%
- **Tests réussis:** $PASSED_CHECKS
- **Tests totaux:** $TOTAL_CHECKS
- **Statut:** $([ $score_percentage -ge 80 ] && echo "✅ CONFORME" || echo "⚠️ ATTENTION REQUISE")

## 🔍 DÉTAILS PAR CATÉGORIE

### 🧪 Tests E2E (Semaine 1)
- Configuration Playwright multi-browser
- Tests d'authentification et réservation
- Utilitaires et helpers de test
- Intégration CI/CD GitHub Actions

### ⚡ Performance (Semaine 2)
- Optimisations Frontend (Vite, lazy loading, images)
- Optimisations Backend (Redis, compression, rate limiting)
- Monitoring et métriques de performance
- Bundle optimization et code splitting

### 🛠️ Scripts et Outils
- Scripts d'automatisation Sprint 16
- Permissions d'exécution configurées
- Dépendances Sprint 15 vérifiées

### 🏗️ Structure E2E
- Répertoires de tests organisés
- Configuration TypeScript/Playwright
- Scripts d'exécution dédiés

### 📋 Conformité Roadmap
- Objectifs Sprint 16 respectés
- Livrables Semaine 1 complétés
- Livrables Semaine 2 complétés

## 🎯 RECOMMANDATIONS

$(if [ $score_percentage -ge 90 ]; then
    echo "### ✅ EXCELLENT (90%+)"
    echo "- Sprint 16 prêt pour validation finale"
    echo "- Tous les objectifs sont atteints"
    echo "- Prêt pour Sprint 17"
elif [ $score_percentage -ge 80 ]; then
    echo "### ✅ BON (80-89%)"
    echo "- Sprint 16 globalement conforme"
    echo "- Quelques ajustements mineurs recommandés"
    echo "- Validation finale possible"
elif [ $score_percentage -ge 70 ]; then
    echo "### ⚠️ ATTENTION (70-79%)"
    echo "- Corrections nécessaires avant validation"
    echo "- Revoir les éléments manquants"
    echo "- Tests supplémentaires recommandés"
else
    echo "### ❌ CRITIQUE (<70%)"
    echo "- Sprint 16 non conforme"
    echo "- Corrections majeures requises"
    echo "- Revoir la stratégie d'implémentation"
fi)

## 📁 FICHIERS VÉRIFIÉS

### Scripts Principaux
- \`scripts/start-sprint-16.sh\`
- \`scripts/setup-playwright.sh\`
- \`scripts/run-e2e-tests.sh\`
- \`scripts/optimize-performance.sh\`

### Configuration E2E
- \`e2e-tests/playwright.config.ts\`
- \`e2e-tests/package.json\`
- \`.github/workflows/e2e-tests.yml\`

### Optimisations Performance
- \`*/vite.config.ts\` - Configuration Vite
- \`*/src/utils/lazyLoad.tsx\` - Lazy loading
- \`*/src/cache/cache.service.ts\` - Cache Redis

## 🚀 PROCHAINES ÉTAPES

1. **Si score ≥ 80%:** Procéder au Sprint 17
2. **Si score < 80%:** Corriger les éléments manquants
3. **Tests d'intégration:** Valider le fonctionnement E2E
4. **Performance testing:** Mesurer les métriques réelles

---

**Validation générée le $(date) par validate-sprint16-progress.sh**
EOF
    
    success "✅ Rapport de validation généré: $report_file"
    
    # Afficher le résumé
    echo ""
    if [ $score_percentage -ge 80 ]; then
        echo -e "${GREEN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${GREEN}║                          ✅ VALIDATION RÉUSSIE                               ║${NC}"
        echo -e "${GREEN}║                                                                              ║${NC}"
        echo -e "${GREEN}║  📊 Score: $PASSED_CHECKS/$TOTAL_CHECKS ($score_percentage%)                                                    ║${NC}"
        echo -e "${GREEN}║  🎯 Sprint 16: CONFORME aux objectifs                                       ║${NC}"
        echo -e "${GREEN}║  🚀 Prêt pour Sprint 17                                                     ║${NC}"
        echo -e "${GREEN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    else
        echo -e "${YELLOW}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${YELLOW}║                        ⚠️ ATTENTION REQUISE                                  ║${NC}"
        echo -e "${YELLOW}║                                                                              ║${NC}"
        echo -e "${YELLOW}║  📊 Score: $PASSED_CHECKS/$TOTAL_CHECKS ($score_percentage%)                                                    ║${NC}"
        echo -e "${YELLOW}║  🎯 Sprint 16: Corrections nécessaires                                      ║${NC}"
        echo -e "${YELLOW}║  🔧 Voir le rapport pour les détails                                        ║${NC}"
        echo -e "${YELLOW}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    fi
    echo ""
    
    log "📊 Rapport complet: $report_file"
}

# Fonction principale
main() {
    log "🚀 DÉBUT DE LA VALIDATION SPRINT 16"
    
    # Exécuter toutes les validations
    validate_e2e_tests
    validate_performance
    validate_scripts
    validate_e2e_structure
    validate_roadmap_compliance
    
    # Générer le rapport final
    generate_validation_report
    
    success "🎉 VALIDATION SPRINT 16 TERMINÉE"
    log "📊 Score final: $PASSED_CHECKS/$TOTAL_CHECKS"
}

# Exécution du script
main "$@"
