#!/bin/bash

# 🎭 SCRIPT DE CONFIGURATION PLAYWRIGHT - SPRINT 16
# Configuration unifiée pour tests E2E multi-browser
# Basé sur doc/audit-roadmap-sprints-finaux.md

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
E2E_DIR="$PROJECT_ROOT/e2e-tests"
LOG_FILE="$E2E_DIR/setup-playwright-$TIMESTAMP.log"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Créer le répertoire E2E
mkdir -p "$E2E_DIR"

log "🎭 DÉMARRAGE CONFIGURATION PLAYWRIGHT - SPRINT 16"
log "=================================================="
log "Projet: $PROJECT_ROOT"
log "Log: $LOG_FILE"

# 1. INSTALLATION PLAYWRIGHT
install_playwright() {
    log "📦 1. INSTALLATION PLAYWRIGHT"
    
    # Créer package.json pour E2E si nécessaire
    if [[ ! -f "$E2E_DIR/package.json" ]]; then
        log "   Création package.json E2E"
        cat > "$E2E_DIR/package.json" << 'EOF'
{
  "name": "@retreatandbe/e2e-tests",
  "version": "1.0.0",
  "description": "Tests E2E unifiés pour Retreat And Be - Sprint 16",
  "scripts": {
    "test": "playwright test",
    "test:headed": "playwright test --headed",
    "test:debug": "playwright test --debug",
    "test:ui": "playwright test --ui",
    "test:report": "playwright show-report",
    "test:chrome": "playwright test --project=chromium",
    "test:firefox": "playwright test --project=firefox",
    "test:safari": "playwright test --project=webkit",
    "test:mobile": "playwright test --project=mobile",
    "install": "playwright install"
  },
  "devDependencies": {
    "@playwright/test": "^1.40.0",
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0"
  },
  "dependencies": {
    "dotenv": "^16.0.0"
  }
}
EOF
        success "✅ Package.json E2E créé"
    fi
    
    # Installer Playwright
    cd "$E2E_DIR"
    log "   Installation des dépendances..."
    npm install
    
    log "   Installation des navigateurs..."
    npx playwright install
    
    success "✅ Playwright installé"
    cd "$PROJECT_ROOT"
}

# 2. CONFIGURATION PLAYWRIGHT
create_playwright_config() {
    log "⚙️  2. CRÉATION CONFIGURATION PLAYWRIGHT"
    
    cat > "$E2E_DIR/playwright.config.ts" << 'EOF'
import { defineConfig, devices } from '@playwright/test';
import * as dotenv from 'dotenv';

// Charger les variables d'environnement
dotenv.config();

/**
 * Configuration Playwright unifiée - Sprint 16
 * Tests E2E multi-browser pour Retreat And Be
 */
export default defineConfig({
  // Répertoire des tests
  testDir: './tests',
  
  // Timeout global
  timeout: 30000,
  
  // Timeout pour les assertions
  expect: {
    timeout: 5000,
  },
  
  // Nombre de tentatives en cas d'échec
  retries: process.env.CI ? 2 : 1,
  
  // Nombre de workers parallèles
  workers: process.env.CI ? 1 : undefined,
  
  // Reporter pour les résultats
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['json', { outputFile: 'test-results.json' }],
    ['junit', { outputFile: 'test-results.xml' }],
    ['list']
  ],
  
  // Configuration globale
  use: {
    // URL de base
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    
    // Timeout pour les actions
    actionTimeout: 10000,
    
    // Timeout pour la navigation
    navigationTimeout: 30000,
    
    // Capture d'écran en cas d'échec
    screenshot: 'only-on-failure',
    
    // Enregistrement vidéo
    video: 'retain-on-failure',
    
    // Trace en cas d'échec
    trace: 'retain-on-failure',
    
    // Headers par défaut
    extraHTTPHeaders: {
      'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
    },
  },

  // Projets de test (différents navigateurs)
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 }
      },
    },
    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        viewport: { width: 1920, height: 1080 }
      },
    },
    {
      name: 'webkit',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1920, height: 1080 }
      },
    },
    {
      name: 'mobile',
      use: { 
        ...devices['iPhone 13'],
      },
    },
    {
      name: 'tablet',
      use: { 
        ...devices['iPad Pro'],
      },
    },
  ],

  // Serveur de développement
  webServer: process.env.CI ? undefined : {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    cwd: '../Projet-RB2/Front-Audrey-V1-Main-main',
  },
});
EOF
    
    success "✅ Configuration Playwright créée"
}

# 3. CRÉATION DES TESTS DE BASE
create_base_tests() {
    log "🧪 3. CRÉATION DES TESTS DE BASE"
    
    # Créer la structure des tests
    mkdir -p "$E2E_DIR/tests"/{auth,booking,admin,api}
    mkdir -p "$E2E_DIR/tests/utils"
    
    # Test d'authentification
    cat > "$E2E_DIR/tests/auth/login.spec.ts" << 'EOF'
import { test, expect } from '@playwright/test';

test.describe('Authentification', () => {
  test('Login utilisateur valide', async ({ page }) => {
    await page.goto('/login');
    
    // Remplir le formulaire de connexion
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    
    // Cliquer sur le bouton de connexion
    await page.click('[data-testid="login-button"]');
    
    // Vérifier la redirection vers le dashboard
    await expect(page).toHaveURL('/dashboard');
    
    // Vérifier la présence du menu utilisateur
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });

  test('Login avec identifiants invalides', async ({ page }) => {
    await page.goto('/login');
    
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');
    
    // Vérifier le message d'erreur
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Identifiants invalides');
  });

  test('Logout utilisateur', async ({ page }) => {
    // Se connecter d'abord
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    
    // Se déconnecter
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-button"]');
    
    // Vérifier la redirection vers la page d'accueil
    await expect(page).toHaveURL('/');
  });
});
EOF
    
    # Test de réservation
    cat > "$E2E_DIR/tests/booking/booking-flow.spec.ts" << 'EOF'
import { test, expect } from '@playwright/test';

test.describe('Processus de réservation', () => {
  test.beforeEach(async ({ page }) => {
    // Se connecter avant chaque test
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
  });

  test('Réservation complète d\'une retraite', async ({ page }) => {
    // 1. Rechercher une retraite
    await page.goto('/retreats');
    await page.fill('[data-testid="search-input"]', 'Yoga');
    await page.click('[data-testid="search-button"]');
    
    // 2. Sélectionner une retraite
    await page.click('[data-testid="retreat-card"]:first-child');
    await expect(page.locator('[data-testid="retreat-details"]')).toBeVisible();
    
    // 3. Choisir les dates
    await page.click('[data-testid="book-now-button"]');
    await page.click('[data-testid="date-picker"]');
    await page.click('[data-testid="available-date"]:first-child');
    
    // 4. Sélectionner les options
    await page.click('[data-testid="room-type-select"]');
    await page.click('[data-testid="room-option"]:first-child');
    
    // 5. Procéder au paiement
    await page.click('[data-testid="proceed-payment"]');
    
    // 6. Remplir les informations de paiement (mode test)
    await page.fill('[data-testid="card-number"]', '****************');
    await page.fill('[data-testid="card-expiry"]', '12/25');
    await page.fill('[data-testid="card-cvc"]', '123');
    
    // 7. Confirmer la réservation
    await page.click('[data-testid="confirm-booking"]');
    
    // 8. Vérifier la confirmation
    await expect(page.locator('[data-testid="booking-confirmation"]')).toBeVisible();
    await expect(page.locator('[data-testid="booking-reference"]')).toBeVisible();
  });

  test('Annulation de réservation', async ({ page }) => {
    // Aller aux réservations
    await page.goto('/my-bookings');
    
    // Sélectionner une réservation
    await page.click('[data-testid="booking-item"]:first-child');
    
    // Annuler la réservation
    await page.click('[data-testid="cancel-booking"]');
    await page.click('[data-testid="confirm-cancellation"]');
    
    // Vérifier l'annulation
    await expect(page.locator('[data-testid="cancellation-confirmation"]')).toBeVisible();
  });
});
EOF
    
    # Utilitaires de test
    cat > "$E2E_DIR/tests/utils/auth-helper.ts" << 'EOF'
import { Page } from '@playwright/test';

export class AuthHelper {
  constructor(private page: Page) {}

  async login(email: string = '<EMAIL>', password: string = 'password123') {
    await this.page.goto('/login');
    await this.page.fill('[data-testid="email"]', email);
    await this.page.fill('[data-testid="password"]', password);
    await this.page.click('[data-testid="login-button"]');
    await this.page.waitForURL('/dashboard');
  }

  async logout() {
    await this.page.click('[data-testid="user-menu"]');
    await this.page.click('[data-testid="logout-button"]');
    await this.page.waitForURL('/');
  }

  async loginAsAdmin() {
    await this.login('<EMAIL>', 'admin123');
  }
}
EOF
    
    success "✅ Tests de base créés"
}

# 4. CONFIGURATION CI/CD
create_ci_config() {
    log "🔄 4. CONFIGURATION CI/CD"
    
    # GitHub Actions workflow
    mkdir -p "$PROJECT_ROOT/.github/workflows"
    
    cat > "$PROJECT_ROOT/.github/workflows/e2e-tests.yml" << 'EOF'
name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd e2e-tests
        npm ci
    
    - name: Install Playwright browsers
      run: |
        cd e2e-tests
        npx playwright install --with-deps ${{ matrix.browser }}
    
    - name: Run E2E tests
      run: |
        cd e2e-tests
        npx playwright test --project=${{ matrix.browser }}
      env:
        BASE_URL: ${{ secrets.BASE_URL || 'http://localhost:3000' }}
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report-${{ matrix.browser }}
        path: e2e-tests/playwright-report/
        retention-days: 30
EOF
    
    success "✅ Configuration CI/CD créée"
}

# 5. SCRIPTS D'EXÉCUTION
create_execution_scripts() {
    log "📜 5. CRÉATION SCRIPTS D'EXÉCUTION"
    
    # Script de lancement des tests
    cat > "$E2E_DIR/run-tests.sh" << 'EOF'
#!/bin/bash

# Script d'exécution des tests E2E - Sprint 16

set -e

# Configuration
E2E_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

cd "$E2E_DIR"

case "${1:-all}" in
    "chrome"|"chromium")
        log "🧪 Exécution tests Chrome"
        npx playwright test --project=chromium
        ;;
    "firefox")
        log "🧪 Exécution tests Firefox"
        npx playwright test --project=firefox
        ;;
    "safari"|"webkit")
        log "🧪 Exécution tests Safari"
        npx playwright test --project=webkit
        ;;
    "mobile")
        log "🧪 Exécution tests Mobile"
        npx playwright test --project=mobile
        ;;
    "headed")
        log "🧪 Exécution tests avec interface"
        npx playwright test --headed
        ;;
    "debug")
        log "🧪 Exécution tests en mode debug"
        npx playwright test --debug
        ;;
    "ui")
        log "🧪 Ouverture interface UI"
        npx playwright test --ui
        ;;
    "report")
        log "📊 Ouverture rapport"
        npx playwright show-report
        ;;
    "all"|*)
        log "🧪 Exécution tous les tests"
        npx playwright test
        ;;
esac

success "✅ Tests terminés"
EOF
    
    chmod +x "$E2E_DIR/run-tests.sh"
    
    success "✅ Scripts d'exécution créés"
}

# Fonction principale
main() {
    log "🚀 DÉBUT DE LA CONFIGURATION PLAYWRIGHT"
    
    # Exécuter toutes les configurations
    install_playwright
    create_playwright_config
    create_base_tests
    create_ci_config
    create_execution_scripts
    
    # Générer le rapport
    generate_setup_report
    
    success "🎉 CONFIGURATION PLAYWRIGHT TERMINÉE"
    log "📊 Rapport: $E2E_DIR/setup-report-$TIMESTAMP.md"
}

# Génération du rapport de configuration
generate_setup_report() {
    log "📊 GÉNÉRATION DU RAPPORT DE CONFIGURATION"
    
    local report_file="$E2E_DIR/setup-report-$TIMESTAMP.md"
    
    cat > "$report_file" << EOF
# 🎭 RAPPORT CONFIGURATION PLAYWRIGHT - SPRINT 16

**Date:** $(date)  
**Script:** setup-playwright.sh  
**Projet:** Agentic-Coding-Framework-RB2  

## 📊 RÉSUMÉ EXÉCUTIF

- **Playwright:** ✅ Installé et configuré
- **Navigateurs:** ✅ Chrome, Firefox, Safari, Mobile
- **Tests de base:** ✅ Auth, Booking, Admin
- **CI/CD:** ✅ GitHub Actions configuré
- **Scripts:** ✅ Exécution et utilitaires

## 🎯 CONFIGURATION RÉALISÉE

### 1. ✅ Installation Playwright
- Package.json E2E créé
- Dépendances installées
- Navigateurs téléchargés

### 2. ✅ Configuration Multi-Browser
- **Chrome:** Desktop 1920x1080
- **Firefox:** Desktop 1920x1080  
- **Safari:** Desktop 1920x1080
- **Mobile:** iPhone 13
- **Tablet:** iPad Pro

### 3. ✅ Tests de Base
- **Authentification:** Login, Logout, Erreurs
- **Réservation:** Flow complet, Annulation
- **Utilitaires:** AuthHelper, Helpers

### 4. ✅ CI/CD Integration
- GitHub Actions workflow
- Tests automatiques sur PR
- Rapports d'artefacts

## 🚀 UTILISATION

### Commandes Principales
\`\`\`bash
# Tous les tests
cd e2e-tests && npm test

# Tests par navigateur
./run-tests.sh chrome
./run-tests.sh firefox
./run-tests.sh safari
./run-tests.sh mobile

# Mode debug
./run-tests.sh debug

# Interface UI
./run-tests.sh ui

# Rapport
./run-tests.sh report
\`\`\`

### Structure Créée
\`\`\`
e2e-tests/
├── package.json
├── playwright.config.ts
├── tests/
│   ├── auth/
│   ├── booking/
│   ├── admin/
│   ├── api/
│   └── utils/
├── run-tests.sh
└── playwright-report/
\`\`\`

## 🎯 PROCHAINES ÉTAPES

1. **Exécuter les tests:** \`./run-tests.sh\`
2. **Ajouter tests spécifiques** selon besoins
3. **Intégrer dans pipeline CI/CD**
4. **Optimiser performance tests**

---

**✅ Configuration Playwright terminée - Prêt pour tests E2E!**
EOF
    
    success "✅ Rapport de configuration généré: $report_file"
}

# Exécution du script
main "$@"
