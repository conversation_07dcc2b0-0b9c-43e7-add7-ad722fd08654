#!/bin/bash

# 🎯 VALIDATION FINALE DE LA ROADMAP
# Vérification que tous les éléments sont bien finalisés

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Couleurs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Compteurs
TOTAL_CHECKS=0
PASSED_CHECKS=0

# Fonction de validation
validate() {
    local description="$1"
    local condition="$2"

    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

    if eval "$condition"; then
        echo -e "${GREEN}✅${NC} $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}❌${NC} $description"
        return 1
    fi
}

echo "=================================================================="
echo "🎯 VALIDATION FINALE DE LA ROADMAP"
echo "📊 Vérification de la finalisation complète"
echo "=================================================================="
echo ""

# 1. Vérification des fichiers de roadmap
echo -e "${BLUE}📋 1. FICHIERS DE ROADMAP${NC}"
validate "Roadmap principale existe" "[[ -f '$PROJECT_ROOT/doc/audit-roadmap-sprints-finaux.md' ]]"
validate "Rapport de completion existe" "[[ -f '$PROJECT_ROOT/ROADMAP_COMPLETION_REPORT.md' ]]"
validate "Script Sprint 20 existe" "[[ -f '$PROJECT_ROOT/scripts/deploy-sprint20-production-launch.sh' ]]"
validate "Script de validation existe" "[[ -f '$PROJECT_ROOT/scripts/validate-roadmap-completion.sh' ]]"
echo ""

# 2. Vérification du contenu de la roadmap
echo -e "${BLUE}📊 2. CONTENU DE LA ROADMAP${NC}"
validate "Progression 100% marquée" "grep -q '100% (20/20 sprints)' '$PROJECT_ROOT/doc/audit-roadmap-sprints-finaux.md'"
validate "Sprint 20 marqué terminé" "grep -q 'Sprint 20.*TERMINÉ' '$PROJECT_ROOT/doc/audit-roadmap-sprints-finaux.md'"
validate "Mission accomplie marquée" "grep -q 'MISSION ACCOMPLIE' '$PROJECT_ROOT/doc/audit-roadmap-sprints-finaux.md'"
validate "Production commerciale atteinte" "grep -q 'PRODUCTION COMMERCIALE ACTIVE' '$PROJECT_ROOT/doc/audit-roadmap-sprints-finaux.md'"
echo ""

# 3. Vérification des scripts de déploiement
echo -e "${BLUE}🚀 3. SCRIPTS DE DÉPLOIEMENT${NC}"
validate "Script Sprint 20 exécutable" "[[ -x '$PROJECT_ROOT/scripts/deploy-sprint20-production-launch.sh' ]]"
validate "Script de production existe" "[[ -f '$PROJECT_ROOT/scripts/deploy-production.sh' ]]"
validate "Script K8s production existe" "[[ -f '$PROJECT_ROOT/scripts/deploy-production-k8s.sh' ]]"
validate "Blue-Green deployment configuré" "[[ -f '$PROJECT_ROOT/Projet-RB2/k8s/base/blue-green-deployment.yaml' ]]"
echo ""

# 4. Vérification des fonctionnalités Sprint 20
echo -e "${BLUE}🎯 4. FONCTIONNALITÉS SPRINT 20${NC}"
validate "Onboarding flow implémenté" "[[ -f '$PROJECT_ROOT/frontend/onboarding/OnboardingFlow.tsx' ]]"
validate "Monitoring avancé configuré" "[[ -d '$PROJECT_ROOT/Projet-RB2/infrastructure/monitoring' ]]"
validate "Configuration production prête" "[[ -f '$PROJECT_ROOT/Projet-RB2/infrastructure/environments/production/mobile-values.yaml' ]]"
validate "Documentation déploiement existe" "[[ -f '$PROJECT_ROOT/Projet-RB2/docs/DEPLOYMENT_GUIDE.md' ]]"
echo ""

# 5. Vérification de l'infrastructure Hanuman
echo -e "${BLUE}🤖 5. INFRASTRUCTURE HANUMAN${NC}"
validate "Hanuman unifié existe" "[[ -d '$PROJECT_ROOT/hanuman-unified' ]]"
validate "Agents spécialisés présents" "[[ -d '$PROJECT_ROOT/hanuman-unified/agents' ]]"
validate "Cortex central configuré" "[[ -d '$PROJECT_ROOT/hanuman-unified/brain/cortex-central' ]]"
validate "Configuration Kubernetes Hanuman" "[[ -f '$PROJECT_ROOT/kubernetes/hanuman-bridge-deployment.yaml' ]]"
echo ""

# 6. Vérification des accomplissements
echo -e "${BLUE}🏆 6. ACCOMPLISSEMENTS DOCUMENTÉS${NC}"
validate "Sprint 2 accomplissements" "[[ -f '$PROJECT_ROOT/hanuman-unified/specialized-agents/evolution/SPRINT2_ACCOMPLISHMENTS.md' ]]"
validate "Sprint 10 completion" "[[ -f '$PROJECT_ROOT/hanuman-unified/SPRINT_10_FINAL_COMPLETION.md' ]]"
validate "Documentation finale" "[[ -f '$PROJECT_ROOT/FINAL_DEPLOYMENT_SUMMARY.md' ]]"
validate "Plan d'implémentation" "[[ -f '$PROJECT_ROOT/IMPLEMENTATION_IMMEDIATE_PLAN.md' ]]"
echo ""

# 7. Vérification des systèmes de monitoring
echo -e "${BLUE}📊 7. SYSTÈMES DE MONITORING${NC}"
validate "Service de monitoring backend" "[[ -f '$PROJECT_ROOT/Projet-RB2/Backend-NestJS/src/modules/monitoring/monitoring.service.ts' ]]"
validate "Performance monitoring" "[[ -f '$PROJECT_ROOT/Projet-RB2/Backend-NestJS/src/modules/monitoring/services/performance-monitoring.service.ts' ]]"
validate "Health checks configurés" "[[ -f '$PROJECT_ROOT/Projet-RB2/infrastructure/monitoring/health-checks.ts' ]]"
validate "Setup monitoring frontend" "[[ -f '$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main/monitoring/setup-monitoring.sh' ]]"
echo ""

# 8. Calcul du score final
echo "=================================================================="
echo -e "${PURPLE}📊 RÉSULTATS DE LA VALIDATION${NC}"
echo "=================================================================="

PERCENTAGE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))

echo "Total des vérifications: $TOTAL_CHECKS"
echo "Vérifications réussies: $PASSED_CHECKS"
echo "Score de completion: $PERCENTAGE%"
echo ""

if [[ $PERCENTAGE -eq 100 ]]; then
    echo -e "${GREEN}🎉 VALIDATION COMPLÈTE RÉUSSIE!${NC}"
    echo -e "${GREEN}✅ La roadmap est 100% finalisée${NC}"
    echo -e "${GREEN}✅ Tous les éléments sont en place${NC}"
    echo -e "${GREEN}✅ Prêt pour la production commerciale${NC}"
    echo ""
    echo -e "${PURPLE}🚀 MISSION ACCOMPLIE!${NC}"
elif [[ $PERCENTAGE -ge 90 ]]; then
    echo -e "${YELLOW}⚠️  VALIDATION PRESQUE COMPLÈTE${NC}"
    echo -e "${YELLOW}📋 Quelques éléments mineurs à finaliser${NC}"
elif [[ $PERCENTAGE -ge 75 ]]; then
    echo -e "${YELLOW}⚠️  VALIDATION PARTIELLE${NC}"
    echo -e "${YELLOW}📋 Plusieurs éléments à compléter${NC}"
else
    echo -e "${RED}❌ VALIDATION ÉCHOUÉE${NC}"
    echo -e "${RED}📋 Roadmap non finalisée${NC}"
fi

echo ""
echo "=================================================================="
echo "Rapport généré le: $(date '+%Y-%m-%d %H:%M:%S')"
echo "=================================================================="

# Retourner le code approprié
if [[ $PERCENTAGE -eq 100 ]]; then
    exit 0
else
    exit 1
fi
