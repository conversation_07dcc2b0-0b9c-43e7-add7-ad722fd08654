#!/bin/bash

# 🚀 DÉMARRAGE RAPIDE SPRINT 15 - MENU INTERACTIF
# Interface simple pour exécuter les actions du Sprint 15
# Basé sur doc/audit-roadmap-sprints-finaux.md

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Banner principal
print_main_banner() {
    clear
    echo -e "${PURPLE}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════════════════════╗
║                        🚀 SPRINT 15 - DÉMARRAGE RAPIDE                      ║
║                    SÉCURISATION & INTÉGRATION DESIGN SYSTEM                 ║
║                                                                              ║
║  📅 Période: 28 Mai - 10 Juin 2025 (4 semaines)                           ║
║  🎯 Objectif: 0 vulnérabilité critique + Design System dans 6 services     ║
║  📊 Basé sur: doc/audit-roadmap-sprints-finaux.md                          ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# Menu principal
show_main_menu() {
    echo -e "${CYAN}📋 MENU PRINCIPAL - SPRINT 15${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} 🔒 Sécurité - Correction vulnérabilités critiques (Semaine 1)"
    echo -e "${GREEN}2.${NC} 🎨 Design System - Intégration microservices (Semaine 2)"
    echo -e "${GREEN}3.${NC} 🚀 Exécution complète Sprint 15 (Semaines 1+2)"
    echo -e "${GREEN}4.${NC} ✅ Validation et rapport de progression"
    echo ""
    echo -e "${YELLOW}Actions individuelles:${NC}"
    echo -e "${BLUE}5.${NC} 🔐 Migration des secrets uniquement"
    echo -e "${BLUE}6.${NC} 🔍 Audit de sécurité uniquement"
    echo -e "${BLUE}7.${NC} 🎨 Création Design System uniquement"
    echo ""
    echo -e "${PURPLE}Utilitaires:${NC}"
    echo -e "${CYAN}8.${NC} 📊 Voir l'état actuel du projet"
    echo -e "${CYAN}9.${NC} 📚 Afficher la documentation Sprint 15"
    echo -e "${CYAN}0.${NC} ❌ Quitter"
    echo ""
    echo -e "${YELLOW}Choisissez une option (0-9):${NC} "
}

# Fonction pour afficher l'état du projet
show_project_status() {
    echo -e "${CYAN}📊 ÉTAT ACTUEL DU PROJET${NC}"
    echo "================================"
    
    # Vérifier les scripts
    echo -e "${BLUE}Scripts disponibles:${NC}"
    local scripts=(
        "security-fix-critical.sh"
        "migrate-secrets.sh"
        "integrate-design-system.sh"
        "start-sprint-15.sh"
        "validate-sprint15-progress.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ -f "$PROJECT_ROOT/scripts/$script" ]]; then
            echo -e "  ✅ $script"
        else
            echo -e "  ❌ $script"
        fi
    done
    
    echo ""
    echo -e "${BLUE}Services détectés:${NC}"
    local services=(
        "Projet-RB2/Backend-NestJS:Backend NestJS"
        "Projet-RB2/Front-Audrey-V1-Main-main:Frontend React"
        "Projet-RB2/Agent IA:Agent IA"
        "Projet-RB2/Security:Security Service"
        "Projet-RB2/Financial-Management:Financial Management"
        "Projet-RB2/Social:Social Platform"
    )
    
    for service_info in "${services[@]}"; do
        local service_path="${service_info%:*}"
        local service_name="${service_info#*:}"
        
        if [[ -d "$PROJECT_ROOT/$service_path" ]]; then
            echo -e "  ✅ $service_name"
        else
            echo -e "  ❌ $service_name"
        fi
    done
    
    echo ""
    echo -e "${BLUE}Configuration actuelle:${NC}"
    
    if [[ -f "$PROJECT_ROOT/.env.vault" ]]; then
        echo -e "  ✅ .env.vault (gestion des secrets)"
    else
        echo -e "  ❌ .env.vault (à créer)"
    fi
    
    if [[ -d "$PROJECT_ROOT/design-system" ]]; then
        echo -e "  ✅ Design System (package créé)"
    else
        echo -e "  ❌ Design System (à créer)"
    fi
    
    if [[ -d "$PROJECT_ROOT/security-reports" ]]; then
        echo -e "  ✅ Rapports de sécurité (répertoire créé)"
    else
        echo -e "  ❌ Rapports de sécurité (à créer)"
    fi
    
    echo ""
    read -p "Appuyez sur Entrée pour continuer..."
}

# Fonction pour afficher la documentation
show_documentation() {
    echo -e "${CYAN}📚 DOCUMENTATION SPRINT 15${NC}"
    echo "============================"
    echo ""
    echo -e "${GREEN}🎯 OBJECTIFS SPRINT 15:${NC}"
    echo "• 0 vulnérabilité critique/high"
    echo "• Design System dans 6 microservices prioritaires"
    echo "• Documentation migration sécurité"
    echo "• Pipeline CI/CD avec security gates"
    echo ""
    echo -e "${GREEN}📋 SEMAINE 1 - SÉCURITÉ:${NC}"
    echo "• Migration API Keys vers variables environnement"
    echo "• Correction SQL Injection"
    echo "• Mise à jour dépendances (lodash, axios, etc.)"
    echo "• Validation stricte des inputs"
    echo ""
    echo -e "${GREEN}📋 SEMAINE 2 - DESIGN SYSTEM:${NC}"
    echo "• Création package @retreatandbe/design-system"
    echo "• Intégration services Priority 1: Frontend, Agent-IA, Backend"
    echo "• Intégration services Priority 2: Security, Financial, Social"
    echo "• Documentation et exemples d'utilisation"
    echo ""
    echo -e "${GREEN}🏆 LIVRABLES FINAUX:${NC}"
    echo "• ✅ 0 vulnérabilité critique"
    echo "• ✅ Design System dans 6 microservices"
    echo "• ✅ Documentation migration sécurité"
    echo "• ✅ Pipeline CI/CD avec security gates"
    echo ""
    echo -e "${YELLOW}📁 FICHIERS CLÉS:${NC}"
    echo "• doc/audit-roadmap-sprints-finaux.md - Roadmap complète"
    echo "• scripts/start-sprint-15.sh - Script principal"
    echo "• scripts/validate-sprint15-progress.sh - Validation"
    echo ""
    read -p "Appuyez sur Entrée pour continuer..."
}

# Fonction pour exécuter une action avec confirmation
execute_action() {
    local action_name="$1"
    local script_path="$2"
    local description="$3"
    
    echo ""
    echo -e "${YELLOW}🚀 EXÉCUTION: $action_name${NC}"
    echo -e "${BLUE}Description:${NC} $description"
    echo -e "${BLUE}Script:${NC} $script_path"
    echo ""
    
    read -p "Voulez-vous continuer? (o/N): " confirm
    
    if [[ $confirm =~ ^[Oo]$ ]]; then
        echo ""
        echo -e "${GREEN}▶️  Démarrage de $action_name...${NC}"
        echo "================================"
        
        if [[ -f "$script_path" ]]; then
            chmod +x "$script_path"
            "$script_path"
            local exit_code=$?
            
            echo ""
            if [[ $exit_code -eq 0 ]]; then
                echo -e "${GREEN}✅ $action_name terminé avec succès!${NC}"
            else
                echo -e "${RED}❌ $action_name terminé avec des erreurs (code: $exit_code)${NC}"
            fi
        else
            echo -e "${RED}❌ Script non trouvé: $script_path${NC}"
        fi
        
        echo ""
        read -p "Appuyez sur Entrée pour continuer..."
    else
        echo -e "${YELLOW}⏸️  Action annulée${NC}"
        sleep 1
    fi
}

# Boucle principale du menu
main_loop() {
    while true; do
        print_main_banner
        show_main_menu
        
        read -r choice
        
        case $choice in
            1)
                execute_action \
                    "Sécurité - Semaine 1" \
                    "$PROJECT_ROOT/scripts/start-sprint-15.sh security" \
                    "Correction des vulnérabilités critiques, migration des secrets, mise à jour des dépendances"
                ;;
            2)
                execute_action \
                    "Design System - Semaine 2" \
                    "$PROJECT_ROOT/scripts/start-sprint-15.sh design" \
                    "Création et intégration du Design System dans les microservices prioritaires"
                ;;
            3)
                execute_action \
                    "Sprint 15 Complet" \
                    "$PROJECT_ROOT/scripts/start-sprint-15.sh" \
                    "Exécution complète: Sécurité (Semaine 1) + Design System (Semaine 2)"
                ;;
            4)
                execute_action \
                    "Validation Sprint 15" \
                    "$PROJECT_ROOT/scripts/validate-sprint15-progress.sh" \
                    "Validation des livrables et génération du rapport de progression"
                ;;
            5)
                execute_action \
                    "Migration des Secrets" \
                    "$PROJECT_ROOT/scripts/migrate-secrets.sh" \
                    "Migration des API Keys vers variables d'environnement pour tous les services"
                ;;
            6)
                execute_action \
                    "Audit de Sécurité" \
                    "$PROJECT_ROOT/scripts/start-sprint-15.sh audit" \
                    "Audit de sécurité complet avec npm audit et scan des vulnérabilités"
                ;;
            7)
                execute_action \
                    "Création Design System" \
                    "$PROJECT_ROOT/scripts/integrate-design-system.sh" \
                    "Création du package Design System et intégration dans les services"
                ;;
            8)
                show_project_status
                ;;
            9)
                show_documentation
                ;;
            0)
                echo -e "${GREEN}👋 Au revoir! Sprint 15 en cours...${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ Option invalide. Choisissez entre 0 et 9.${NC}"
                sleep 2
                ;;
        esac
    done
}

# Point d'entrée principal
main() {
    # Vérifier que nous sommes dans le bon répertoire
    if [[ ! -f "$PROJECT_ROOT/doc/audit-roadmap-sprints-finaux.md" ]]; then
        echo -e "${RED}❌ Erreur: Roadmap Sprint 15 non trouvée${NC}"
        echo -e "${YELLOW}Assurez-vous d'être dans le répertoire racine du projet${NC}"
        exit 1
    fi
    
    # Démarrer la boucle principale
    main_loop
}

# Gestion des arguments en ligne de commande
if [[ $# -gt 0 ]]; then
    case "$1" in
        "status")
            show_project_status
            ;;
        "doc")
            show_documentation
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [status|doc|help]"
            echo ""
            echo "Options:"
            echo "  status  - Afficher l'état du projet"
            echo "  doc     - Afficher la documentation"
            echo "  help    - Afficher cette aide"
            echo ""
            echo "Sans argument: Lance le menu interactif"
            ;;
        *)
            echo -e "${RED}❌ Option inconnue: $1${NC}"
            echo "Utilisez '$0 help' pour voir les options disponibles"
            exit 1
            ;;
    esac
else
    # Lancer le menu interactif
    main
fi
