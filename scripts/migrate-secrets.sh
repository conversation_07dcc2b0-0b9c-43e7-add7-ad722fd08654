#!/bin/bash

# 🔐 SCRIPT DE MIGRATION DES SECRETS - SPRINT 15
# Migration des API Keys vers variables d'environnement
# Basé sur doc/audit-roadmap-sprints-finaux.md

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
MIGRATION_DIR="$PROJECT_ROOT/security-reports/migration"
LOG_FILE="$MIGRATION_DIR/secrets-migration-$TIMESTAMP.log"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Créer le répertoire de migration
mkdir -p "$MIGRATION_DIR"

log "🔐 DÉMARRAGE MIGRATION DES SECRETS - SPRINT 15"
log "=============================================="
log "Projet: $PROJECT_ROOT"
log "Log: $LOG_FILE"

# 1. BACKUP DES FICHIERS AVANT MIGRATION
backup_files() {
    log "💾 1. BACKUP DES FICHIERS AVANT MIGRATION"
    
    local backup_dir="$MIGRATION_DIR/backup-$TIMESTAMP"
    mkdir -p "$backup_dir"
    
    # Services à sauvegarder
    local services=(
        "Projet-RB2/Backend-NestJS"
        "Projet-RB2/Front-Audrey-V1-Main-main"
        "Projet-RB2/Agent IA"
        "Projet-RB2/Security"
        "hanuman-unified"
        "vimana"
    )
    
    for service in "${services[@]}"; do
        local service_path="$PROJECT_ROOT/$service"
        
        if [[ -d "$service_path" ]]; then
            log "   Backup: $service"
            
            # Backup des fichiers de configuration
            find "$service_path" -name "*.env*" -o -name "config.*" -o -name "*.config.*" | while read -r file; do
                if [[ -f "$file" ]]; then
                    local rel_path="${file#$PROJECT_ROOT/}"
                    local backup_file="$backup_dir/$rel_path"
                    mkdir -p "$(dirname "$backup_file")"
                    cp "$file" "$backup_file"
                    log "     Sauvegardé: $rel_path"
                fi
            done
        fi
    done
    
    success "✅ Backup terminé: $backup_dir"
}

# 2. GÉNÉRATION DES FICHIERS .ENV POUR CHAQUE SERVICE
generate_env_files() {
    log "📝 2. GÉNÉRATION DES FICHIERS .ENV POUR CHAQUE SERVICE"
    
    # Backend NestJS
    generate_backend_env
    
    # Frontend
    generate_frontend_env
    
    # Agent IA
    generate_agent_ia_env
    
    # Security Service
    generate_security_env
    
    # Hanuman
    generate_hanuman_env
    
    # Vimana
    generate_vimana_env
}

generate_backend_env() {
    local service_path="$PROJECT_ROOT/Projet-RB2/Backend-NestJS"
    
    if [[ -d "$service_path" ]]; then
        log "   Génération .env pour Backend NestJS"
        
        cat > "$service_path/.env.example" << 'EOF'
# 🔐 BACKEND NESTJS - CONFIGURATION ENVIRONNEMENT
# Sprint 15 - Migration des secrets

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/retreatandbe_dev
MONGODB_URI=mongodb://localhost:27017/retreatandbe

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT & Authentication
JWT_SECRET=your-super-secret-jwt-key-minimum-32-chars
JWT_REFRESH_SECRET=your-super-secret-refresh-key-minimum-32-chars
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# API Keys
OPENAI_API_KEY=sk-your-openai-api-key-here
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key-here
SENDGRID_API_KEY=SG.your-sendgrid-api-key-here

# External Services
SENTRY_DSN=https://your-sentry-dsn-here
CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here
HASH_SALT=your-hash-salt-here
CORS_ORIGIN=http://localhost:3000,http://localhost:5173

# Server Configuration
PORT=3001
NODE_ENV=development
API_PREFIX=api/v1

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./uploads

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100
EOF
        
        success "✅ .env.example généré pour Backend NestJS"
    fi
}

generate_frontend_env() {
    local service_path="$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main"
    
    if [[ -d "$service_path" ]]; then
        log "   Génération .env pour Frontend"
        
        cat > "$service_path/.env.example" << 'EOF'
# 🔐 FRONTEND REACT - CONFIGURATION ENVIRONNEMENT
# Sprint 15 - Migration des secrets

# API Configuration
VITE_API_URL=http://localhost:3001/api/v1
VITE_WS_URL=ws://localhost:3001

# Public API Keys (safe for frontend)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key-here
VITE_GOOGLE_MAPS_API_KEY=your-google-maps-api-key-here

# Analytics
VITE_GOOGLE_ANALYTICS_ID=GA-your-analytics-id-here
VITE_HOTJAR_ID=your-hotjar-id-here

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_CHAT=true
VITE_ENABLE_NOTIFICATIONS=true

# Environment
VITE_NODE_ENV=development
VITE_APP_VERSION=1.0.0

# CDN & Assets
VITE_CDN_URL=https://your-cdn-url-here
VITE_ASSETS_URL=https://your-assets-url-here

# Social Login
VITE_GOOGLE_CLIENT_ID=your-google-client-id-here
VITE_FACEBOOK_APP_ID=your-facebook-app-id-here

# Monitoring
VITE_SENTRY_DSN=https://your-frontend-sentry-dsn-here
EOF
        
        success "✅ .env.example généré pour Frontend"
    fi
}

generate_agent_ia_env() {
    local service_path="$PROJECT_ROOT/Projet-RB2/Agent IA"
    
    if [[ -d "$service_path" ]]; then
        log "   Génération .env pour Agent IA"
        
        cat > "$service_path/.env.example" << 'EOF'
# 🔐 AGENT IA - CONFIGURATION ENVIRONNEMENT
# Sprint 15 - Migration des secrets

# AI Services
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here
HUGGINGFACE_API_KEY=your-huggingface-api-key-here

# Vector Database
PINECONE_API_KEY=your-pinecone-api-key-here
PINECONE_ENVIRONMENT=your-pinecone-environment
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=your-weaviate-api-key-here

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/agent_ia_dev
REDIS_URL=redis://localhost:6379

# API Configuration
PORT=3002
API_PREFIX=api/v1
NODE_ENV=development

# Security
JWT_SECRET=your-agent-ia-jwt-secret-here
ENCRYPTION_KEY=your-agent-ia-encryption-key-here

# Model Configuration
DEFAULT_MODEL=gpt-4
MAX_TOKENS=4000
TEMPERATURE=0.7

# Monitoring
SENTRY_DSN=https://your-agent-ia-sentry-dsn-here
LOG_LEVEL=info
EOF
        
        success "✅ .env.example généré pour Agent IA"
    fi
}

generate_security_env() {
    local service_path="$PROJECT_ROOT/Projet-RB2/Security"
    
    if [[ -d "$service_path" ]]; then
        log "   Génération .env pour Security Service"
        
        cat > "$service_path/.env.example" << 'EOF'
# 🔐 SECURITY SERVICE - CONFIGURATION ENVIRONNEMENT
# Sprint 15 - Migration des secrets

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/security_dev
REDIS_URL=redis://localhost:6379

# Security Keys
MASTER_KEY=your-master-security-key-minimum-64-chars
ENCRYPTION_KEY=your-encryption-key-minimum-32-chars
SIGNING_KEY=your-signing-key-minimum-32-chars

# JWT Configuration
JWT_SECRET=your-security-jwt-secret-here
JWT_ALGORITHM=HS256
JWT_EXPIRES_IN=1h

# API Configuration
PORT=3003
NODE_ENV=development
API_PREFIX=api/v1/security

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Monitoring & Alerts
SENTRY_DSN=https://your-security-sentry-dsn-here
SLACK_WEBHOOK_URL=https://hooks.slack.com/your-webhook-url
EMAIL_ALERTS=<EMAIL>

# Audit
AUDIT_LOG_RETENTION=90
AUDIT_LOG_LEVEL=info

# External Security Services
VIRUSTOTAL_API_KEY=your-virustotal-api-key-here
SHODAN_API_KEY=your-shodan-api-key-here
EOF
        
        success "✅ .env.example généré pour Security Service"
    fi
}

generate_hanuman_env() {
    local service_path="$PROJECT_ROOT/hanuman-unified"
    
    if [[ -d "$service_path" ]]; then
        log "   Génération .env pour Hanuman"
        
        cat > "$service_path/.env.example" << 'EOF'
# 🔐 HANUMAN UNIFIED - CONFIGURATION ENVIRONNEMENT
# Sprint 15 - Migration des secrets

# Core Configuration
NODE_ENV=development
PORT=3004
API_PREFIX=api/v1/hanuman

# AI Services
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/hanuman_dev
MONGODB_URI=mongodb://localhost:27017/hanuman
REDIS_URL=redis://localhost:6379

# Vector Database
WEAVIATE_URL=http://localhost:8080
PINECONE_API_KEY=your-pinecone-api-key-here

# Security
JWT_SECRET=your-hanuman-jwt-secret-here
ENCRYPTION_KEY=your-hanuman-encryption-key-here

# Microservices Communication
BACKEND_URL=http://localhost:3001
FRONTEND_URL=http://localhost:3000
AGENT_IA_URL=http://localhost:3002
SECURITY_URL=http://localhost:3003

# Monitoring
SENTRY_DSN=https://your-hanuman-sentry-dsn-here
LOG_LEVEL=debug

# Hanuman Specific
CORTEX_MODE=development
MEMORY_RETENTION=30
LEARNING_RATE=0.01
EOF
        
        success "✅ .env.example généré pour Hanuman"
    fi
}

generate_vimana_env() {
    local service_path="$PROJECT_ROOT/vimana"
    
    if [[ -d "$service_path" ]]; then
        log "   Génération .env pour Vimana"
        
        cat > "$service_path/.env.example" << 'EOF'
# 🔐 VIMANA FRAMEWORK - CONFIGURATION ENVIRONNEMENT
# Sprint 15 - Migration des secrets

# Core Configuration
NODE_ENV=development
PORT=3005
API_PREFIX=api/v1/vimana

# AI Services
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/vimana_dev
REDIS_URL=redis://localhost:6379

# Security
JWT_SECRET=your-vimana-jwt-secret-here
ENCRYPTION_KEY=your-vimana-encryption-key-here

# Vimana Specific
SPIRITUAL_MODE=true
BRAHMA_CREATOR_ENABLED=true
VISHNU_PRESERVER_ENABLED=true
SHIVA_TRANSFORMER_ENABLED=true

# Integration
HANUMAN_URL=http://localhost:3004
IDE_INTEGRATION=true

# Monitoring
SENTRY_DSN=https://your-vimana-sentry-dsn-here
LOG_LEVEL=info
EOF
        
        success "✅ .env.example généré pour Vimana"
    fi
}

# 3. CRÉATION DU SCRIPT DE VALIDATION
create_validation_script() {
    log "✅ 3. CRÉATION DU SCRIPT DE VALIDATION"
    
    cat > "$MIGRATION_DIR/validate-env.sh" << 'EOF'
#!/bin/bash

# Script de validation des variables d'environnement
# Vérifie que tous les services ont leurs variables configurées

validate_service() {
    local service_name="$1"
    local env_file="$2"
    
    echo "🔍 Validation: $service_name"
    
    if [[ -f "$env_file" ]]; then
        echo "  ✅ Fichier .env trouvé"
        
        # Vérifier les variables critiques
        local critical_vars=("DATABASE_URL" "JWT_SECRET" "NODE_ENV")
        
        for var in "${critical_vars[@]}"; do
            if grep -q "^$var=" "$env_file"; then
                echo "  ✅ $var configuré"
            else
                echo "  ❌ $var manquant"
            fi
        done
    else
        echo "  ❌ Fichier .env manquant: $env_file"
    fi
    
    echo ""
}

# Valider tous les services
validate_service "Backend NestJS" "Projet-RB2/Backend-NestJS/.env"
validate_service "Frontend" "Projet-RB2/Front-Audrey-V1-Main-main/.env"
validate_service "Agent IA" "Projet-RB2/Agent IA/.env"
validate_service "Security" "Projet-RB2/Security/.env"
validate_service "Hanuman" "hanuman-unified/.env"
validate_service "Vimana" "vimana/.env"
EOF
    
    chmod +x "$MIGRATION_DIR/validate-env.sh"
    success "✅ Script de validation créé: $MIGRATION_DIR/validate-env.sh"
}

# 4. GÉNÉRATION DU RAPPORT DE MIGRATION
generate_migration_report() {
    log "📊 4. GÉNÉRATION DU RAPPORT DE MIGRATION"
    
    local report_file="$MIGRATION_DIR/migration-report-$TIMESTAMP.md"
    
    cat > "$report_file" << EOF
# 🔐 RAPPORT DE MIGRATION DES SECRETS - SPRINT 15

**Date:** $(date)  
**Script:** migrate-secrets.sh  
**Projet:** Agentic-Coding-Framework-RB2  

## 📊 RÉSUMÉ EXÉCUTIF

- **Services migrés:** 6 microservices
- **Fichiers .env.example créés:** ✅ 6 fichiers
- **Backup effectué:** ✅ $MIGRATION_DIR/backup-$TIMESTAMP
- **Script de validation:** ✅ Créé

## 🔍 SERVICES TRAITÉS

### 1. ✅ Backend NestJS
- **Port:** 3001
- **Variables:** Database, JWT, API Keys, Security
- **Fichier:** Projet-RB2/Backend-NestJS/.env.example

### 2. ✅ Frontend React
- **Port:** 3000
- **Variables:** API URLs, Public Keys, Analytics
- **Fichier:** Projet-RB2/Front-Audrey-V1-Main-main/.env.example

### 3. ✅ Agent IA
- **Port:** 3002
- **Variables:** AI Services, Vector DB, Models
- **Fichier:** Projet-RB2/Agent IA/.env.example

### 4. ✅ Security Service
- **Port:** 3003
- **Variables:** Security Keys, Monitoring, Audit
- **Fichier:** Projet-RB2/Security/.env.example

### 5. ✅ Hanuman Unified
- **Port:** 3004
- **Variables:** AI, Microservices, Cortex
- **Fichier:** hanuman-unified/.env.example

### 6. ✅ Vimana Framework
- **Port:** 3005
- **Variables:** Spiritual Mode, Integration
- **Fichier:** vimana/.env.example

## 🎯 PROCHAINES ÉTAPES

1. **Copier** les fichiers .env.example vers .env
2. **Remplir** les vraies valeurs des secrets
3. **Exécuter** le script de validation
4. **Tester** chaque service individuellement
5. **Déployer** avec les nouvelles configurations

## 📁 FICHIERS GÉNÉRÉS

- Backup: \`$MIGRATION_DIR/backup-$TIMESTAMP/\`
- Validation: \`$MIGRATION_DIR/validate-env.sh\`
- Ce rapport: \`$report_file\`

## 🔒 SÉCURITÉ

⚠️ **IMPORTANT:** Les fichiers .env contiennent des secrets sensibles
- Ne jamais commiter les fichiers .env
- Utiliser .env.example pour les exemples
- Configurer .gitignore approprié

---

**✅ Sprint 15 - Migration des Secrets: TERMINÉ**
EOF
    
    success "✅ Rapport de migration généré: $report_file"
}

# Fonction principale
main() {
    log "🚀 DÉBUT DE LA MIGRATION DES SECRETS"
    
    # Exécuter la migration
    backup_files
    generate_env_files
    create_validation_script
    generate_migration_report
    
    success "🎉 MIGRATION DES SECRETS TERMINÉE"
    log "📊 Rapport complet: $MIGRATION_DIR/migration-report-$TIMESTAMP.md"
    log "🔍 Validation: $MIGRATION_DIR/validate-env.sh"
}

# Exécution du script
main "$@"
