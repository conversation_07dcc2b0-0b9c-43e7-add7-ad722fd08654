#!/bin/bash

# 🔒 SCRIPT DE CORRECTION DES VULNÉRABILITÉS CRITIQUES - SPRINT 15
# Basé sur doc/audit-roadmap-sprints-finaux.md
# Période: 28 Mai - 10 Juin 2025

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
SECURITY_DIR="$PROJECT_ROOT/security-reports"
LOG_FILE="$SECURITY_DIR/security-fix-critical-$TIMESTAMP.log"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Créer le répertoire de sécurité
mkdir -p "$SECURITY_DIR"

log "🔒 DÉMARRAGE CORRECTION VULNÉRABILITÉS CRITIQUES - SPRINT 15"
log "=============================================================="
log "Projet: $PROJECT_ROOT"
log "Log: $LOG_FILE"

# 1. IDENTIFICATION DES API KEYS HARDCODÉES
identify_hardcoded_secrets() {
    log "🔍 1. IDENTIFICATION DES API KEYS HARDCODÉES"
    
    local secrets_file="$SECURITY_DIR/hardcoded-secrets-$TIMESTAMP.txt"
    
    # Patterns de recherche pour les secrets
    local patterns=(
        "api[_-]?key\s*[=:]\s*['\"][^'\"]{10,}['\"]"
        "secret[_-]?key\s*[=:]\s*['\"][^'\"]{10,}['\"]"
        "password\s*[=:]\s*['\"][^'\"]{5,}['\"]"
        "token\s*[=:]\s*['\"][^'\"]{10,}['\"]"
        "private[_-]?key\s*[=:]\s*['\"][^'\"]{10,}['\"]"
        "database[_-]?url\s*[=:]\s*['\"][^'\"]{10,}['\"]"
        "mongodb[_-]?uri\s*[=:]\s*['\"][^'\"]{10,}['\"]"
        "jwt[_-]?secret\s*[=:]\s*['\"][^'\"]{10,}['\"]"
    )
    
    echo "# SECRETS HARDCODÉS DÉTECTÉS - $(date)" > "$secrets_file"
    echo "# Généré par: security-fix-critical.sh" >> "$secrets_file"
    echo "" >> "$secrets_file"
    
    local secrets_found=0
    
    for pattern in "${patterns[@]}"; do
        log "   Recherche pattern: $pattern"
        
        # Rechercher dans les fichiers source
        local matches=$(grep -r -E "$pattern" \
            --include="*.ts" --include="*.js" --include="*.tsx" --include="*.jsx" \
            --include="*.json" --include="*.env*" \
            --exclude-dir=node_modules --exclude-dir=.git --exclude-dir=dist \
            "$PROJECT_ROOT" 2>/dev/null || true)
        
        if [[ -n "$matches" ]]; then
            echo "## Pattern: $pattern" >> "$secrets_file"
            echo "$matches" >> "$secrets_file"
            echo "" >> "$secrets_file"
            secrets_found=$((secrets_found + 1))
        fi
    done
    
    if [[ $secrets_found -gt 0 ]]; then
        warning "⚠️  $secrets_found types de secrets détectés"
        warning "📄 Détails dans: $secrets_file"
    else
        success "✅ Aucun secret hardcodé détecté"
    fi
    
    echo "$secrets_found" > "$SECURITY_DIR/secrets-count.txt"
}

# 2. CRÉATION DU SYSTÈME DE GESTION DES SECRETS
create_secrets_management() {
    log "🔐 2. CRÉATION DU SYSTÈME DE GESTION DES SECRETS"
    
    # Créer .env.vault pour la gestion sécurisée
    local vault_file="$PROJECT_ROOT/.env.vault"
    
    if [[ ! -f "$vault_file" ]]; then
        log "   Création de .env.vault"
        cat > "$vault_file" << 'EOF'
# 🔐 VAULT DE SECRETS - SPRINT 15
# Ce fichier contient les références aux secrets, pas les valeurs réelles
# Les valeurs réelles sont stockées dans le gestionnaire de secrets

# API Keys
API_KEY_OPENAI=vault:secret/openai/api_key
API_KEY_STRIPE=vault:secret/stripe/api_key
API_KEY_SENDGRID=vault:secret/sendgrid/api_key

# Database
DATABASE_URL=vault:secret/database/url
MONGODB_URI=vault:secret/mongodb/uri
REDIS_URL=vault:secret/redis/url

# JWT & Auth
JWT_SECRET=vault:secret/auth/jwt_secret
JWT_REFRESH_SECRET=vault:secret/auth/jwt_refresh_secret
SESSION_SECRET=vault:secret/auth/session_secret

# External Services
SENTRY_DSN=vault:secret/monitoring/sentry_dsn
CLOUDINARY_URL=vault:secret/storage/cloudinary_url

# Encryption
ENCRYPTION_KEY=vault:secret/security/encryption_key
HASH_SALT=vault:secret/security/hash_salt
EOF
        success "✅ .env.vault créé"
    else
        log "   .env.vault existe déjà"
    fi
    
    # Créer .env.example avec des exemples
    local example_file="$PROJECT_ROOT/.env.example"
    
    if [[ ! -f "$example_file" ]]; then
        log "   Création de .env.example"
        cat > "$example_file" << 'EOF'
# 📝 EXEMPLE DE CONFIGURATION - SPRINT 15
# Copiez ce fichier vers .env et remplissez avec vos vraies valeurs

# API Keys (remplacez par vos vraies clés)
API_KEY_OPENAI=sk-your-openai-key-here
API_KEY_STRIPE=sk_test_your-stripe-key-here
API_KEY_SENDGRID=SG.your-sendgrid-key-here

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/retreatandbe
MONGODB_URI=mongodb://localhost:27017/retreatandbe
REDIS_URL=redis://localhost:6379

# JWT & Auth (générez des secrets forts)
JWT_SECRET=your-super-secret-jwt-key-here
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here
SESSION_SECRET=your-super-secret-session-key-here

# External Services
SENTRY_DSN=https://your-sentry-dsn-here
CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name

# Encryption (générez des clés fortes)
ENCRYPTION_KEY=your-32-char-encryption-key-here
HASH_SALT=your-hash-salt-here

# Environment
NODE_ENV=development
PORT=3000
EOF
        success "✅ .env.example créé"
    else
        log "   .env.example existe déjà"
    fi
}

# 3. CORRECTION SQL INJECTION
fix_sql_injection() {
    log "🛡️  3. CORRECTION DES VULNÉRABILITÉS SQL INJECTION"
    
    local backend_path="$PROJECT_ROOT/Projet-RB2/Backend-NestJS"
    
    if [[ -d "$backend_path" ]]; then
        log "   Analyse du Backend NestJS"
        
        # Rechercher les requêtes SQL potentiellement vulnérables
        local sql_files=$(find "$backend_path/src" -name "*.ts" -exec grep -l "query\|execute\|raw" {} \; 2>/dev/null || true)
        
        if [[ -n "$sql_files" ]]; then
            log "   Fichiers avec requêtes SQL détectés:"
            echo "$sql_files" | while read -r file; do
                log "     - $file"
            done
            
            # Créer un rapport des requêtes à vérifier
            local sql_report="$SECURITY_DIR/sql-injection-review-$TIMESTAMP.md"
            echo "# RÉVISION SQL INJECTION - $(date)" > "$sql_report"
            echo "" >> "$sql_report"
            echo "## Fichiers à réviser:" >> "$sql_report"
            echo "" >> "$sql_report"
            
            echo "$sql_files" | while read -r file; do
                echo "### $file" >> "$sql_report"
                echo "" >> "$sql_report"
                echo "\`\`\`typescript" >> "$sql_report"
                grep -n -A 3 -B 1 "query\|execute\|raw" "$file" 2>/dev/null || true >> "$sql_report"
                echo "\`\`\`" >> "$sql_report"
                echo "" >> "$sql_report"
            done
            
            warning "⚠️  Requêtes SQL détectées - Révision manuelle requise"
            warning "📄 Rapport: $sql_report"
        else
            success "✅ Aucune requête SQL brute détectée"
        fi
    else
        warning "⚠️  Backend NestJS non trouvé: $backend_path"
    fi
}

# 4. MISE À JOUR DES DÉPENDANCES VULNÉRABLES
update_vulnerable_dependencies() {
    log "📦 4. MISE À JOUR DES DÉPENDANCES VULNÉRABLES"
    
    # Services à mettre à jour
    local services=(
        "Projet-RB2/Backend-NestJS"
        "Projet-RB2/Front-Audrey-V1-Main-main"
        "Projet-RB2/Agent IA"
        "Projet-RB2/Security"
        "hanuman-unified"
        "vimana"
    )
    
    for service in "${services[@]}"; do
        local service_path="$PROJECT_ROOT/$service"
        
        if [[ -d "$service_path" && -f "$service_path/package.json" ]]; then
            log "   Mise à jour: $service"
            
            cd "$service_path"
            
            # Audit de sécurité
            log "     Audit npm..."
            npm audit --audit-level=high > "$SECURITY_DIR/audit-$service-$TIMESTAMP.txt" 2>&1 || true
            
            # Tentative de correction automatique
            log "     Correction automatique..."
            npm audit fix --force > "$SECURITY_DIR/fix-$service-$TIMESTAMP.txt" 2>&1 || true
            
            success "✅ Service $service mis à jour"
        else
            warning "⚠️  Service non trouvé ou sans package.json: $service"
        fi
    done
    
    cd "$PROJECT_ROOT"
}

# Fonction principale
main() {
    log "🚀 DÉBUT DU PROCESSUS DE CORRECTION"
    
    # Exécuter les corrections
    identify_hardcoded_secrets
    create_secrets_management
    fix_sql_injection
    update_vulnerable_dependencies
    
    success "🎉 CORRECTION DES VULNÉRABILITÉS CRITIQUES TERMINÉE"
    log "📊 Consultez les rapports dans: $SECURITY_DIR"
}

# Exécution du script
main "$@"
