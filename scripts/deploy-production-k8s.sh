#!/bin/bash

# Deploy Production Kubernetes - Sprint 18
# Date: 9 Juillet 2025
# Objectif: Déploiement production avec monitoring 24/7

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
K8S_DIR="$PROJECT_ROOT/k8s"

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "🔍 Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check helm
    if ! command -v helm &> /dev/null; then
        log_error "helm is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check if we're in the right context
    CURRENT_CONTEXT=$(kubectl config current-context)
    log "Current Kubernetes context: $CURRENT_CONTEXT"
    
    if [[ "$CURRENT_CONTEXT" != *"production"* ]]; then
        log_warning "You're not in a production context. Continue? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_error "Deployment cancelled"
            exit 1
        fi
    fi
    
    log_success "Prerequisites check passed"
}

# Deploy namespaces and RBAC
deploy_namespaces() {
    log "🏗️ Deploying namespaces and RBAC..."
    
    kubectl apply -f "$K8S_DIR/production/namespace.yaml"
    
    # Wait for namespaces to be ready
    kubectl wait --for=condition=Active namespace/production --timeout=60s
    kubectl wait --for=condition=Active namespace/monitoring --timeout=60s
    kubectl wait --for=condition=Active namespace/ingress-system --timeout=60s
    kubectl wait --for=condition=Active namespace/backup-system --timeout=60s
    kubectl wait --for=condition=Active namespace/security-system --timeout=60s
    
    log_success "Namespaces deployed successfully"
}

# Deploy storage
deploy_storage() {
    log "💾 Deploying storage configuration..."
    
    kubectl apply -f "$K8S_DIR/production/storage.yaml"
    
    # Wait for storage classes
    kubectl wait --for=condition=Ready storageclass/fast-ssd --timeout=60s
    kubectl wait --for=condition=Ready storageclass/standard-ssd --timeout=60s
    kubectl wait --for=condition=Ready storageclass/backup-storage --timeout=60s
    
    log_success "Storage configuration deployed"
}

# Deploy ingress controller
deploy_ingress() {
    log "🚪 Deploying NGINX Ingress Controller..."
    
    # Install cert-manager first
    kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.12.0/cert-manager.yaml
    
    # Wait for cert-manager
    kubectl wait --for=condition=Available deployment/cert-manager -n cert-manager --timeout=300s
    kubectl wait --for=condition=Available deployment/cert-manager-webhook -n cert-manager --timeout=300s
    kubectl wait --for=condition=Available deployment/cert-manager-cainjector -n cert-manager --timeout=300s
    
    # Deploy NGINX Ingress
    kubectl apply -f "$K8S_DIR/ingress/nginx-ingress.yaml"
    
    # Wait for ingress controller
    kubectl wait --for=condition=Available deployment/nginx-ingress-controller -n ingress-system --timeout=300s
    
    log_success "Ingress controller deployed"
}

# Deploy monitoring stack
deploy_monitoring() {
    log "📊 Deploying monitoring stack..."
    
    # Deploy Prometheus
    kubectl apply -f "$K8S_DIR/monitoring/prometheus.yaml"
    
    # Wait for Prometheus
    kubectl wait --for=condition=Available deployment/prometheus -n monitoring --timeout=300s
    
    # Deploy Grafana
    kubectl apply -f "$K8S_DIR/monitoring/grafana.yaml"
    
    # Wait for Grafana
    kubectl wait --for=condition=Available deployment/grafana -n monitoring --timeout=300s
    
    # Deploy Alertmanager
    kubectl apply -f "$K8S_DIR/monitoring/alertmanager.yaml"
    
    # Wait for Alertmanager
    kubectl wait --for=condition=Available deployment/alertmanager -n monitoring --timeout=300s
    
    log_success "Monitoring stack deployed"
}

# Deploy backup system
deploy_backup() {
    log "💾 Deploying backup and disaster recovery..."
    
    # Install Velero
    helm repo add vmware-tanzu https://vmware-tanzu.github.io/helm-charts
    helm repo update
    
    helm upgrade --install velero vmware-tanzu/velero \
        --namespace velero \
        --create-namespace \
        --set-file credentials.secretContents.cloud=/tmp/velero-credentials \
        --set configuration.provider=aws \
        --set configuration.backupStorageLocation.name=aws-s3-backup \
        --set configuration.backupStorageLocation.bucket=retreat-and-be-backups \
        --set configuration.backupStorageLocation.config.region=us-east-1 \
        --set configuration.volumeSnapshotLocation.name=aws-ebs-snapshots \
        --set configuration.volumeSnapshotLocation.config.region=us-east-1 \
        --set initContainers[0].name=velero-plugin-for-aws \
        --set initContainers[0].image=velero/velero-plugin-for-aws:v1.7.0 \
        --set initContainers[0].volumeMounts[0].name=plugins \
        --set initContainers[0].volumeMounts[0].mountPath=/target \
        --wait
    
    # Apply backup schedules
    kubectl apply -f "$K8S_DIR/backup/velero-backup.yaml"
    
    log_success "Backup system deployed"
}

# Deploy application services
deploy_applications() {
    log "🚀 Deploying application services..."
    
    # Convert Docker Compose to Kubernetes manifests
    if command -v kompose &> /dev/null; then
        log "Converting Docker Compose to Kubernetes manifests..."
        cd "$PROJECT_ROOT"
        kompose convert -f docker-compose.unified.yml -o k8s/production/generated/
        
        # Apply generated manifests
        kubectl apply -f k8s/production/generated/ -n production
    else
        log_warning "Kompose not found. Deploying manually..."
        
        # Deploy core services manually
        deploy_core_services
    fi
    
    log_success "Application services deployed"
}

# Deploy core services manually
deploy_core_services() {
    log "🔧 Deploying core services manually..."
    
    # PostgreSQL
    kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: retreat_and_be
        - name: POSTGRES_USER
          value: rb_user
        - name: POSTGRES_PASSWORD
          value: rb_secure_password_2025
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-primary-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: production
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
EOF

    # Redis
    kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        command: ["redis-server", "--requirepass", "rb_redis_password_2025"]
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-storage
          mountPath: /data
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-master-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: production
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
EOF

    # Kong Gateway
    kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kong-gateway
  namespace: production
spec:
  replicas: 5
  selector:
    matchLabels:
      app: kong-gateway
  template:
    metadata:
      labels:
        app: kong-gateway
    spec:
      containers:
      - name: kong
        image: kong:3.4-alpine
        env:
        - name: KONG_DATABASE
          value: "off"
        - name: KONG_DECLARATIVE_CONFIG
          value: /kong/declarative/kong.yml
        - name: KONG_PROXY_LISTEN
          value: "0.0.0.0:8000"
        - name: KONG_ADMIN_LISTEN
          value: "0.0.0.0:8001"
        ports:
        - containerPort: 8000
        - containerPort: 8001
        volumeMounts:
        - name: kong-config
          mountPath: /kong/declarative
      volumes:
      - name: kong-config
        configMap:
          name: kong-config
---
apiVersion: v1
kind: Service
metadata:
  name: kong-gateway
  namespace: production
spec:
  selector:
    app: kong-gateway
  ports:
  - name: proxy
    port: 8000
    targetPort: 8000
  - name: admin
    port: 8001
    targetPort: 8001
  type: LoadBalancer
EOF
}

# Validate deployment
validate_deployment() {
    log "✅ Validating deployment..."
    
    # Check all pods are running
    log "Checking pod status..."
    kubectl get pods --all-namespaces | grep -E "(Pending|Error|CrashLoopBackOff)" && {
        log_warning "Some pods are not in Running state"
    } || {
        log_success "All pods are running"
    }
    
    # Check services
    log "Checking services..."
    kubectl get services --all-namespaces
    
    # Check ingress
    log "Checking ingress..."
    kubectl get ingress --all-namespaces
    
    # Test monitoring endpoints
    log "Testing monitoring endpoints..."
    
    # Port forward to test locally
    kubectl port-forward -n monitoring svc/prometheus 9090:9090 &
    PROMETHEUS_PID=$!
    sleep 5
    
    if curl -f -s http://localhost:9090/-/healthy > /dev/null; then
        log_success "Prometheus is healthy"
    else
        log_warning "Prometheus health check failed"
    fi
    
    kill $PROMETHEUS_PID 2>/dev/null || true
    
    log_success "Deployment validation completed"
}

# Display access information
display_access_info() {
    log "🌐 Access Information:"
    echo ""
    echo "📊 Monitoring URLs:"
    echo "  • Prometheus: https://monitoring.retreatandbe.com/prometheus"
    echo "  • Grafana: https://monitoring.retreatandbe.com/grafana (admin/admin_password_2025)"
    echo "  • Alertmanager: https://monitoring.retreatandbe.com/alertmanager"
    echo ""
    echo "🚪 Application URLs:"
    echo "  • API Gateway: https://api.retreatandbe.com"
    echo "  • Frontend: https://app.retreatandbe.com"
    echo "  • Hanuman: https://hanuman.retreatandbe.com"
    echo ""
    echo "🔧 Management Commands:"
    echo "  • View pods: kubectl get pods -n production"
    echo "  • View services: kubectl get services -n production"
    echo "  • View logs: kubectl logs -f deployment/kong-gateway -n production"
    echo "  • Scale service: kubectl scale deployment kong-gateway --replicas=10 -n production"
    echo ""
}

# Cleanup function
cleanup() {
    log "🧹 Cleaning up..."
    # Kill any background processes
    jobs -p | xargs -r kill 2>/dev/null || true
}

# Main deployment function
main() {
    log "🚀 Starting Sprint 18 - Production Kubernetes Deployment"
    echo ""
    
    # Set trap for cleanup
    trap cleanup EXIT
    
    check_prerequisites
    deploy_namespaces
    deploy_storage
    deploy_ingress
    deploy_monitoring
    deploy_backup
    deploy_applications
    
    validate_deployment
    display_access_info
    
    log_success "🎉 Sprint 18 production deployment completed successfully!"
    log "📋 Next steps:"
    echo "  1. Configure DNS records for domains"
    echo "  2. Set up SSL certificates"
    echo "  3. Configure monitoring alerts"
    echo "  4. Run load tests"
    echo "  5. Test disaster recovery procedures"
}

# Handle script interruption
trap 'log_error "Deployment interrupted"; cleanup; exit 1' INT TERM

# Run main function
main "$@"
