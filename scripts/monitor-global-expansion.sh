#!/bin/bash

# 🌍 MONITORING EXPANSION MONDIALE
# Dashboard temps réel de la conquête globale

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m'

# Fonction de simulation des métriques globales
simulate_global_metrics() {
    local base_date=$(date +%s)
    local global_growth=$((RANDOM % 40 + 60))  # 60-100% growth
    
    # Métriques par région
    local france_mrr=$((60000 + global_growth * 100))
    local belgium_mrr=$((global_growth * 80))
    local switzerland_mrr=$((global_growth * 120))
    local uk_mrr=$((global_growth * 200))
    local canada_mrr=$((global_growth * 150))
    local australia_mrr=$((global_growth * 100))
    local usa_mrr=$((global_growth * 500))
    
    local total_mrr=$((france_mrr + belgium_mrr + switzerland_mrr + uk_mrr + canada_mrr + australia_mrr + usa_mrr))
    
    # Utilisateurs par région
    local france_users=$((1500 + global_growth * 20))
    local belgium_users=$((global_growth * 8))
    local switzerland_users=$((global_growth * 5))
    local uk_users=$((global_growth * 25))
    local canada_users=$((global_growth * 18))
    local australia_users=$((global_growth * 12))
    local usa_users=$((global_growth * 60))
    
    local total_users=$((france_users + belgium_users + switzerland_users + uk_users + canada_users + australia_users + usa_users))
    
    echo "$total_mrr,$france_mrr,$belgium_mrr,$switzerland_mrr,$uk_mrr,$canada_mrr,$australia_mrr,$usa_mrr,$total_users,$france_users,$belgium_users,$switzerland_users,$uk_users,$canada_users,$australia_users,$usa_users,$global_growth"
}

# Fonction d'affichage du dashboard global
display_global_dashboard() {
    clear
    echo "=================================================================="
    echo -e "${BOLD}${CYAN}🌍 DASHBOARD EXPANSION MONDIALE${NC}"
    echo -e "${BOLD}${PURPLE}🚀 Hanuman - Conquête Globale en Temps Réel${NC}"
    echo "📅 $(date '+%Y-%m-%d %H:%M:%S')"
    echo "=================================================================="
    echo ""
    
    # Récupérer les métriques
    local metrics=$(simulate_global_metrics)
    IFS=',' read -r total_mrr france_mrr belgium_mrr switzerland_mrr uk_mrr canada_mrr australia_mrr usa_mrr total_users france_users belgium_users switzerland_users uk_users canada_users australia_users usa_users growth <<< "$metrics"
    
    # Vue d'ensemble globale
    echo -e "${PURPLE}🎯 VUE D'ENSEMBLE GLOBALE${NC}"
    echo "┌─────────────────────────────────────────────────────────────────┐"
    printf "│ %-25s │ %-35s │\n" "Revenue Total MRR" "€${total_mrr} (+${growth}%)"
    printf "│ %-25s │ %-35s │\n" "Utilisateurs Total" "${total_users} (+${growth}%)"
    printf "│ %-25s │ %-35s │\n" "Marchés Actifs" "7 pays (3 continents)"
    printf "│ %-25s │ %-35s │\n" "Croissance Globale" "+${growth}% (EXPLOSIVE!)"
    printf "│ %-25s │ %-35s │\n" "Valorisation" "€$((total_mrr * 12 * 25 / 1000000))M - €$((total_mrr * 12 * 40 / 1000000))M"
    echo "└─────────────────────────────────────────────────────────────────┘"
    echo ""
    
    # Performance par région
    echo -e "${PURPLE}🌍 PERFORMANCE PAR RÉGION${NC}"
    echo "┌─────────────────────────────────────────────────────────────────┐"
    printf "│ %-15s │ %-12s │ %-12s │ %-12s │ %-10s │\n" "Région" "MRR (€)" "Users" "Market" "Statut"
    echo "├─────────────────────────────────────────────────────────────────┤"
    
    # Europe
    local europe_mrr=$((france_mrr + belgium_mrr + switzerland_mrr))
    local europe_users=$((france_users + belgium_users + switzerland_users))
    printf "│ %-15s │ %-12s │ %-12s │ %-12s │ ${GREEN}%-10s${NC} │\n" "🇪🇺 Europe" "€${europe_mrr}" "${europe_users}" "Mature" "✅ LEADER"
    
    # UK
    if [[ $uk_mrr -ge 500000 ]]; then
        printf "│ %-15s │ %-12s │ %-12s │ %-12s │ ${GREEN}%-10s${NC} │\n" "🇬🇧 UK" "€${uk_mrr}" "${uk_users}" "Growth" "✅ SUCCESS"
    elif [[ $uk_mrr -ge 100000 ]]; then
        printf "│ %-15s │ %-12s │ %-12s │ %-12s │ ${YELLOW}%-10s${NC} │\n" "🇬🇧 UK" "€${uk_mrr}" "${uk_users}" "Launch" "🚀 SCALING"
    else
        printf "│ %-15s │ %-12s │ %-12s │ %-12s │ ${BLUE}%-10s${NC} │\n" "🇬🇧 UK" "€${uk_mrr}" "${uk_users}" "Entry" "🌱 STARTING"
    fi
    
    # North America
    local na_mrr=$((canada_mrr + usa_mrr))
    local na_users=$((canada_users + usa_users))
    if [[ $na_mrr -ge 2000000 ]]; then
        printf "│ %-15s │ %-12s │ %-12s │ %-12s │ ${GREEN}%-10s${NC} │\n" "🇺🇸🇨🇦 N.America" "€${na_mrr}" "${na_users}" "Expansion" "✅ DOMINATE"
    elif [[ $na_mrr -ge 500000 ]]; then
        printf "│ %-15s │ %-12s │ %-12s │ %-12s │ ${YELLOW}%-10s${NC} │\n" "🇺🇸🇨🇦 N.America" "€${na_mrr}" "${na_users}" "Growth" "🚀 GROWING"
    else
        printf "│ %-15s │ %-12s │ %-12s │ %-12s │ ${BLUE}%-10s${NC} │\n" "🇺🇸🇨🇦 N.America" "€${na_mrr}" "${na_users}" "Prep" "🎯 PLANNING"
    fi
    
    # Asia-Pacific
    if [[ $australia_mrr -ge 300000 ]]; then
        printf "│ %-15s │ %-12s │ %-12s │ %-12s │ ${GREEN}%-10s${NC} │\n" "🇦🇺 Asia-Pacific" "€${australia_mrr}" "${australia_users}" "Active" "✅ STRONG"
    elif [[ $australia_mrr -ge 50000 ]]; then
        printf "│ %-15s │ %-12s │ %-12s │ %-12s │ ${YELLOW}%-10s${NC} │\n" "🇦🇺 Asia-Pacific" "€${australia_mrr}" "${australia_users}" "Launch" "🚀 LAUNCH"
    else
        printf "│ %-15s │ %-12s │ %-12s │ %-12s │ ${BLUE}%-10s${NC} │\n" "🇦🇺 Asia-Pacific" "€${australia_mrr}" "${australia_users}" "Prep" "🎯 PREP"
    fi
    
    echo "└─────────────────────────────────────────────────────────────────┘"
    echo ""
    
    # Objectifs de domination mondiale
    echo -e "${PURPLE}🎯 OBJECTIFS DOMINATION MONDIALE${NC}"
    echo "┌─────────────────────────────────────────────────────────────────┐"
    printf "│ %-25s │ %-15s │ %-15s │\n" "Objectif" "Actuel" "Cible 2026"
    echo "├─────────────────────────────────────────────────────────────────┤"
    
    local arr_current=$((total_mrr * 12))
    local arr_progress=$((arr_current * 100 / 150000000))  # Objectif €150M ARR
    
    printf "│ %-25s │ %-15s │ %-15s │\n" "Revenue ARR" "€${arr_current} (${arr_progress}%)" "€150M"
    printf "│ %-25s │ %-15s │ %-15s │\n" "Marchés Actifs" "7 pays" "15+ pays"
    printf "│ %-25s │ %-15s │ %-15s │\n" "Utilisateurs" "${total_users}" "1M+"
    printf "│ %-25s │ %-15s │ %-15s │\n" "Valorisation" "€$((arr_current * 25 / 1000000))M" "€2B+"
    echo "└─────────────────────────────────────────────────────────────────┘"
    echo ""
    
    # Innovation Hanuman Global
    echo -e "${PURPLE}🤖 HANUMAN - INTELLIGENCE GLOBALE${NC}"
    echo "┌─────────────────────────────────────────────────────────────────┐"
    printf "│ %-30s │ %-30s │\n" "Composant" "Statut Global"
    echo "├─────────────────────────────────────────────────────────────────┤"
    printf "│ %-30s │ ${GREEN}%-30s${NC} │\n" "🧠 Cortex Central" "✅ 7 régions actives"
    printf "│ %-30s │ ${GREEN}%-30s${NC} │\n" "🌐 Agents Distribués" "✅ 119 agents (17×7 régions)"
    printf "│ %-30s │ ${YELLOW}%-30s${NC} │\n" "🔮 Hanuman v2.0 Quantum" "🔄 80% développement"
    printf "│ %-30s │ ${GREEN}%-30s${NC} │\n" "📊 Monitoring Prédictif" "✅ Alertes multi-régions"
    printf "│ %-30s │ ${YELLOW}%-30s${NC} │\n" "🏪 Agent Marketplace" "🔄 Beta 5 pays"
    echo "└─────────────────────────────────────────────────────────────────┘"
    echo ""
    
    # Alertes et recommandations globales
    echo -e "${PURPLE}🚨 ALERTES & RECOMMANDATIONS GLOBALES${NC}"
    
    if [[ $total_mrr -ge 500000 ]]; then
        echo -e "${GREEN}🎉 EXCELLENT! Revenue dépasse €500K MRR${NC}"
        echo -e "${GREEN}🚀 Prêt pour Series B (€100-200M)${NC}"
        echo -e "${GREEN}🌟 Trajectoire IPO €10B+ confirmée${NC}"
    elif [[ $total_mrr -ge 300000 ]]; then
        echo -e "${YELLOW}⚡ Très fort! Approche €500K MRR${NC}"
        echo -e "${YELLOW}💰 Préparer Series B agressive${NC}"
    else
        echo -e "${BLUE}📈 Croissance solide - Accélérer expansion${NC}"
    fi
    
    if [[ $growth -ge 80 ]]; then
        echo -e "${GREEN}🚀 CROISSANCE HYPERSONIQUE (+${growth}%)${NC}"
        echo -e "${GREEN}🌍 Domination mondiale en vue!${NC}"
    elif [[ $growth -ge 60 ]]; then
        echo -e "${YELLOW}📈 Forte croissance (+${growth}%)${NC}"
        echo -e "${YELLOW}🎯 Maintenir le momentum global${NC}"
    fi
    
    # Prochaines expansions
    if [[ $total_mrr -ge 400000 ]]; then
        echo -e "${CYAN}🌏 Prochaines expansions: Allemagne, Japon, Singapour${NC}"
    fi
    
    echo ""
    echo "=================================================================="
    echo -e "${CYAN}📊 Mise à jour toutes les 30 secondes${NC}"
    echo -e "${BLUE}💡 Menu: [M] | Régions: [R] | Hanuman: [H] | Quit: [Q]${NC}"
    echo "=================================================================="
}

# Menu d'actions globales
show_global_menu() {
    echo ""
    echo -e "${PURPLE}🎯 ACTIONS EXPANSION GLOBALE${NC}"
    echo "┌─────────────────────────────────────────────────────────────────┐"
    echo "│ 1. 🇪🇺 Dashboard Europe (France, BE, CH)                       │"
    echo "│ 2. 🇬🇧 Status UK expansion                                     │"
    echo "│ 3. 🇺🇸🇨🇦 North America progress                               │"
    echo "│ 4. 🇦🇺 Asia-Pacific development                               │"
    echo "│ 5. 🤖 Hanuman global intelligence                              │"
    echo "│ 6. 💰 Projections Series B                                     │"
    echo "│ 7. 🌍 Plan expansion prochains pays                           │"
    echo "│ 8. 🎊 Célébrer la conquête mondiale!                          │"
    echo "│ 0. ❌ Retour dashboard principal                               │"
    echo "└─────────────────────────────────────────────────────────────────┘"
    echo ""
    echo -n -e "${CYAN}Choisissez une action (0-8): ${NC}"
}

# Fonction de monitoring principal
monitor_global_expansion() {
    echo -e "${GREEN}🚀 Démarrage du monitoring global...${NC}"
    echo -e "${BLUE}🌍 Dashboard expansion mondiale activé${NC}"
    sleep 2
    
    while true; do
        display_global_dashboard
        
        # Lecture non-bloquante avec timeout
        read -t 30 -n 1 key 2>/dev/null || key=""
        
        case $key in
            [Mm])
                show_global_menu
                read -r choice
                case $choice in
                    1) echo -e "${BLUE}🇪🇺 Dashboard Europe...${NC}"; sleep 3 ;;
                    2) echo -e "${BLUE}🇬🇧 UK expansion status...${NC}"; sleep 3 ;;
                    3) echo -e "${BLUE}🇺🇸🇨🇦 North America progress...${NC}"; sleep 3 ;;
                    4) echo -e "${BLUE}🇦🇺 Asia-Pacific development...${NC}"; sleep 3 ;;
                    5) echo -e "${BLUE}🤖 Hanuman global intelligence...${NC}"; sleep 3 ;;
                    6) echo -e "${BLUE}💰 Projections Series B...${NC}"; sleep 3 ;;
                    7) echo -e "${BLUE}🌍 Plan expansion...${NC}"; sleep 3 ;;
                    8) echo -e "${GREEN}🎉 FÉLICITATIONS CONQUÊTE MONDIALE!${NC}"; sleep 5 ;;
                    0) continue ;;
                esac
                ;;
            [Rr])
                echo -e "${YELLOW}🌍 Analyse régionale...${NC}"
                sleep 2
                ;;
            [Hh])
                echo -e "${YELLOW}🤖 Hanuman global status...${NC}"
                sleep 2
                ;;
            [Qq])
                break
                ;;
        esac
    done
}

# Gestion de l'interruption
cleanup() {
    echo ""
    echo -e "${YELLOW}📊 Arrêt du monitoring global${NC}"
    echo -e "${GREEN}✅ Session expansion mondiale terminée${NC}"
    echo -e "${PURPLE}🌍 Continuez la conquête globale!${NC}"
    exit 0
}

trap cleanup INT TERM

# Vérifier les statuts
check_global_status() {
    local missing=0
    
    if [[ ! -f "$PROJECT_ROOT/.roadmap_status" ]]; then
        echo -e "${RED}❌ Roadmap non finalisée${NC}"
        missing=1
    fi
    
    if [[ ! -f "$PROJECT_ROOT/.commercial_status" ]]; then
        echo -e "${RED}❌ Phase commerciale non activée${NC}"
        missing=1
    fi
    
    if [[ ! -f "$PROJECT_ROOT/.expansion_status" ]]; then
        echo -e "${YELLOW}⚠️ Expansion européenne recommandée${NC}"
    fi
    
    if [[ $missing -eq 1 ]]; then
        echo -e "${YELLOW}💡 Exécutez d'abord les phases précédentes${NC}"
        exit 1
    fi
}

# Démarrer le monitoring global
check_global_status
monitor_global_expansion
