# Velero Backup & Disaster Recovery - Sprint 18
# Date: 9 Juillet 2025
# Objectif: Backup automatisé et disaster recovery

# Velero Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: velero
  labels:
    name: velero
    environment: production

---
# Velero ServiceAccount
apiVersion: v1
kind: ServiceAccount
metadata:
  name: velero
  namespace: velero

---
# Velero ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: velero
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: velero
  namespace: velero

---
# Velero BackupStorageLocation
apiVersion: velero.io/v1
kind: BackupStorageLocation
metadata:
  name: aws-s3-backup
  namespace: velero
spec:
  provider: aws
  objectStorage:
    bucket: retreat-and-be-backups
    prefix: velero
  config:
    region: us-east-1
    s3ForcePathStyle: "false"
    s3Url: https://s3.us-east-1.amazonaws.com
    kmsKeyId: arn:aws:kms:us-east-1:********9012:key/********-1234-1234-1234-********9012

---
# Velero VolumeSnapshotLocation
apiVersion: velero.io/v1
kind: VolumeSnapshotLocation
metadata:
  name: aws-ebs-snapshots
  namespace: velero
spec:
  provider: aws
  config:
    region: us-east-1

---
# Velero Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: velero
  namespace: velero
  labels:
    app: velero
spec:
  replicas: 1
  selector:
    matchLabels:
      app: velero
  template:
    metadata:
      labels:
        app: velero
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8085"
    spec:
      serviceAccountName: velero
      priorityClassName: high-priority
      containers:
      - name: velero
        image: velero/velero:v1.11.0
        command:
        - /velero
        args:
        - server
        - --log-level=info
        - --log-format=json
        - --metrics-address=0.0.0.0:8085
        - --profiler-address=0.0.0.0:6060
        - --default-backup-storage-location=aws-s3-backup
        - --default-volume-snapshot-locations=aws-ebs-snapshots
        - --backup-sync-period=60m
        - --fs-backup-timeout=240m
        - --default-backup-ttl=720h
        - --restore-resource-priorities=namespaces,storageclasses,volumesnapshotclass.snapshot.storage.k8s.io,volumesnapshotcontents.snapshot.storage.k8s.io,volumesnapshots.snapshot.storage.k8s.io,persistentvolumes,persistentvolumeclaims,secrets,configmaps,serviceaccounts,limitranges,pods
        ports:
        - containerPort: 8085
          name: metrics
        - containerPort: 6060
          name: profiler
        env:
        - name: VELERO_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: AWS_SHARED_CREDENTIALS_FILE
          value: /credentials/cloud
        volumeMounts:
        - name: cloud-credentials
          mountPath: /credentials
          readOnly: true
        - name: plugins
          mountPath: /plugins
        - name: scratch
          mountPath: /scratch
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /metrics
            port: 8085
          initialDelaySeconds: 30
          timeoutSeconds: 30
        readinessProbe:
          httpGet:
            path: /metrics
            port: 8085
          initialDelaySeconds: 30
          timeoutSeconds: 30
      volumes:
      - name: cloud-credentials
        secret:
          secretName: cloud-credentials
      - name: plugins
        emptyDir: {}
      - name: scratch
        emptyDir: {}
      initContainers:
      - name: velero-plugin-for-aws
        image: velero/velero-plugin-for-aws:v1.7.0
        volumeMounts:
        - name: plugins
          mountPath: /target

---
# Cloud Credentials Secret
apiVersion: v1
kind: Secret
metadata:
  name: cloud-credentials
  namespace: velero
type: Opaque
data:
  cloud: |
    W2RlZmF1bHRdCmF3c19hY2Nlc3Nfa2V5X2lkID0gQUtJQVlPVVJBQ0NFU1NLRVkKYXdzX3NlY3JldF9hY2Nlc3Nfa2V5ID0gWU9VUlNFQ1JFVEFDQ0VTU0tFWQ==

---
# Daily Production Backup Schedule
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: daily-production-backup
  namespace: velero
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM UTC
  template:
    metadata:
      labels:
        backup-type: daily
        environment: production
    spec:
      includedNamespaces:
      - production
      - monitoring
      - ingress-system
      excludedResources:
      - events
      - events.events.k8s.io
      - backups.velero.io
      - restores.velero.io
      - resticrepositories.velero.io
      snapshotVolumes: true
      ttl: 720h  # 30 days
      storageLocation: aws-s3-backup
      volumeSnapshotLocations:
      - aws-ebs-snapshots
      hooks:
        resources:
        - name: postgres-backup-hook
          includedNamespaces:
          - production
          labelSelector:
            matchLabels:
              app: postgres
          pre:
          - exec:
              command:
              - /bin/bash
              - -c
              - "pg_dump -h localhost -U postgres -d retreat_and_be > /tmp/backup.sql"
              container: postgres
          post:
          - exec:
              command:
              - /bin/bash
              - -c
              - "rm -f /tmp/backup.sql"
              container: postgres

---
# Weekly Full Backup Schedule
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: weekly-full-backup
  namespace: velero
spec:
  schedule: "0 1 * * 0"  # Weekly on Sunday at 1 AM UTC
  template:
    metadata:
      labels:
        backup-type: weekly
        environment: production
    spec:
      includedNamespaces:
      - "*"
      excludedResources:
      - events
      - events.events.k8s.io
      snapshotVolumes: true
      ttl: 2160h  # 90 days
      storageLocation: aws-s3-backup
      volumeSnapshotLocations:
      - aws-ebs-snapshots

---
# Critical Services Backup (Every 6 hours)
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: critical-services-backup
  namespace: velero
spec:
  schedule: "0 */6 * * *"  # Every 6 hours
  template:
    metadata:
      labels:
        backup-type: critical
        environment: production
    spec:
      includedNamespaces:
      - production
      labelSelector:
        matchLabels:
          backup-priority: critical
      excludedResources:
      - events
      - events.events.k8s.io
      snapshotVolumes: true
      ttl: 168h  # 7 days
      storageLocation: aws-s3-backup
      volumeSnapshotLocations:
      - aws-ebs-snapshots

---
# Backup Monitoring ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-monitoring
  namespace: monitoring
data:
  backup-alerts.yaml: |
    groups:
    - name: backup.rules
      rules:
      - alert: VeleroBackupFailed
        expr: velero_backup_failure_total > 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Velero backup failed"
          description: "Velero backup {{ $labels.schedule }} has failed"

      - alert: VeleroBackupPartiallyFailed
        expr: velero_backup_partial_failure_total > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Velero backup partially failed"
          description: "Velero backup {{ $labels.schedule }} has partially failed"

      - alert: VeleroBackupTooOld
        expr: time() - velero_backup_last_successful_timestamp > 86400
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Velero backup is too old"
          description: "Last successful backup was more than 24 hours ago"

---
# Disaster Recovery Restore Template
apiVersion: v1
kind: ConfigMap
metadata:
  name: disaster-recovery-procedures
  namespace: velero
data:
  restore-production.yaml: |
    # Emergency Production Restore Procedure
    # 1. Create restore from latest backup
    apiVersion: velero.io/v1
    kind: Restore
    metadata:
      name: production-disaster-recovery
      namespace: velero
    spec:
      backupName: daily-production-backup-20250709020000
      includedNamespaces:
      - production
      - monitoring
      restorePVs: true
      preserveNodePorts: false
      includeClusterResources: true

  restore-critical-services.yaml: |
    # Critical Services Only Restore
    apiVersion: velero.io/v1
    kind: Restore
    metadata:
      name: critical-services-restore
      namespace: velero
    spec:
      backupName: critical-services-backup-latest
      includedNamespaces:
      - production
      labelSelector:
        matchLabels:
          backup-priority: critical
      restorePVs: true

  cross-region-restore.yaml: |
    # Cross-Region Disaster Recovery
    apiVersion: velero.io/v1
    kind: Restore
    metadata:
      name: cross-region-dr
      namespace: velero
    spec:
      backupName: weekly-full-backup-latest
      includedNamespaces:
      - "*"
      restorePVs: true
      preserveNodePorts: false
      includeClusterResources: true

---
# Backup Validation CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: backup-validation
  namespace: velero
spec:
  schedule: "0 6 * * *"  # Daily at 6 AM UTC
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: velero
          containers:
          - name: backup-validator
            image: velero/velero:v1.11.0
            command:
            - /bin/bash
            - -c
            - |
              # Validate latest backups
              velero backup get --output json | jq -r '.items[] | select(.status.phase == "Completed") | .metadata.name' | head -5 | while read backup; do
                echo "Validating backup: $backup"
                velero backup describe $backup --details
              done
              
              # Check for failed backups
              FAILED_BACKUPS=$(velero backup get --output json | jq -r '.items[] | select(.status.phase == "Failed") | .metadata.name')
              if [ ! -z "$FAILED_BACKUPS" ]; then
                echo "ALERT: Failed backups detected: $FAILED_BACKUPS"
                exit 1
              fi
              
              echo "All backups validated successfully"
            env:
            - name: VELERO_NAMESPACE
              value: velero
          restartPolicy: OnFailure

---
# Velero Service for Monitoring
apiVersion: v1
kind: Service
metadata:
  name: velero-metrics
  namespace: velero
  labels:
    app: velero
spec:
  type: ClusterIP
  ports:
  - port: 8085
    targetPort: 8085
    name: metrics
  selector:
    app: velero
