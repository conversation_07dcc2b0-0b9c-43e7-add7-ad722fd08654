# Alertmanager Production Stack - Sprint 18
# Date: 9 Juillet 2025
# Objectif: Alerting 24/7 multi-canal avec escalation

# Alertmanager ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'smtp.gmail.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: '$SMTP_PASSWORD'
      slack_api_url: '$SLACK_WEBHOOK_URL'
      pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'

    templates:
    - '/etc/alertmanager/templates/*.tmpl'

    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default'
      routes:
      # Critical alerts - immediate escalation
      - match:
          severity: critical
        receiver: 'critical-alerts'
        group_wait: 0s
        group_interval: 5s
        repeat_interval: 15m
        routes:
        - match:
            alertname: ServiceDown
          receiver: 'service-down-alerts'
          group_wait: 0s
          repeat_interval: 5m
        - match:
            alertname: HighErrorRate
          receiver: 'error-rate-alerts'
          group_wait: 30s
          repeat_interval: 10m

      # Warning alerts - standard escalation
      - match:
          severity: warning
        receiver: 'warning-alerts'
        group_wait: 30s
        group_interval: 5m
        repeat_interval: 30m

      # Info alerts - low priority
      - match:
          severity: info
        receiver: 'info-alerts'
        group_wait: 5m
        group_interval: 10m
        repeat_interval: 2h

    inhibit_rules:
    - source_match:
        severity: 'critical'
      target_match:
        severity: 'warning'
      equal: ['alertname', 'cluster', 'service']

    receivers:
    # Default receiver
    - name: 'default'
      slack_configs:
      - api_url: '$SLACK_WEBHOOK_URL'
        channel: '#alerts-general'
        title: 'Retreat And Be Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        color: 'warning'

    # Critical alerts - all channels
    - name: 'critical-alerts'
      slack_configs:
      - api_url: '$SLACK_WEBHOOK_URL'
        channel: '#alerts-critical'
        title: '🚨 CRITICAL ALERT - Retreat And Be'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.service }}
          *Instance:* {{ .Labels.instance }}
          {{ end }}
        color: 'danger'
        actions:
        - type: button
          text: 'View in Grafana'
          url: 'https://monitoring.retreatandbe.com/grafana'
        - type: button
          text: 'View in Prometheus'
          url: 'https://monitoring.retreatandbe.com/prometheus'

      email_configs:
      - to: '<EMAIL>'
        subject: '🚨 CRITICAL: {{ .GroupLabels.alertname }} - Retreat And Be'
        body: |
          Critical alert detected in Retreat And Be production environment.
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Started: {{ .StartsAt }}
          {{ end }}
          
          Please investigate immediately.
          
          Monitoring Dashboard: https://monitoring.retreatandbe.com/grafana
          Prometheus: https://monitoring.retreatandbe.com/prometheus

      pagerduty_configs:
      - routing_key: '$PAGERDUTY_INTEGRATION_KEY'
        description: '{{ .GroupLabels.alertname }} - {{ .CommonAnnotations.summary }}'
        severity: 'critical'
        details:
          firing: '{{ .Alerts.Firing | len }}'
          resolved: '{{ .Alerts.Resolved | len }}'
          service: '{{ .GroupLabels.service }}'
          instance: '{{ .GroupLabels.instance }}'

    # Service down alerts - immediate response
    - name: 'service-down-alerts'
      slack_configs:
      - api_url: '$SLACK_WEBHOOK_URL'
        channel: '#alerts-service-down'
        title: '🔴 SERVICE DOWN - Immediate Action Required'
        text: |
          {{ range .Alerts }}
          *Service Down:* {{ .Labels.service }}
          *Instance:* {{ .Labels.instance }}
          *Duration:* {{ .StartsAt }}
          *Runbook:* https://runbooks.retreatandbe.com/service-down
          {{ end }}
        color: 'danger'

      webhook_configs:
      - url: 'https://api.retreatandbe.com/webhooks/service-down'
        send_resolved: true

    # Error rate alerts
    - name: 'error-rate-alerts'
      slack_configs:
      - api_url: '$SLACK_WEBHOOK_URL'
        channel: '#alerts-errors'
        title: '⚠️ HIGH ERROR RATE DETECTED'
        text: |
          {{ range .Alerts }}
          *Service:* {{ .Labels.service }}
          *Error Rate:* {{ .Annotations.description }}
          *Instance:* {{ .Labels.instance }}
          {{ end }}
        color: 'warning'

    # Warning alerts
    - name: 'warning-alerts'
      slack_configs:
      - api_url: '$SLACK_WEBHOOK_URL'
        channel: '#alerts-warnings'
        title: '⚠️ Warning Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        color: 'warning'

      email_configs:
      - to: '<EMAIL>'
        subject: 'Warning: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          {{ .Annotations.summary }}
          {{ .Annotations.description }}
          {{ end }}

    # Info alerts
    - name: 'info-alerts'
      slack_configs:
      - api_url: '$SLACK_WEBHOOK_URL'
        channel: '#alerts-info'
        title: 'ℹ️ Info Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        color: 'good'

---
# Alertmanager Templates
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-templates
  namespace: monitoring
data:
  default.tmpl: |
    {{ define "slack.default.title" }}
    {{ if eq .Status "firing" }}🔥{{ else }}✅{{ end }} {{ .GroupLabels.alertname }}
    {{ end }}

    {{ define "slack.default.text" }}
    {{ range .Alerts }}
    {{ if eq .Status "firing" }}🔥{{ else }}✅{{ end }} {{ .Annotations.summary }}
    {{ if .Annotations.description }}
    {{ .Annotations.description }}
    {{ end }}
    {{ end }}
    {{ end }}

    {{ define "email.default.subject" }}
    {{ if eq .Status "firing" }}[FIRING]{{ else }}[RESOLVED]{{ end }} {{ .GroupLabels.alertname }}
    {{ end }}

    {{ define "email.default.body" }}
    {{ if eq .Status "firing" }}
    Alert is FIRING:
    {{ else }}
    Alert is RESOLVED:
    {{ end }}

    {{ range .Alerts }}
    Alert: {{ .Annotations.summary }}
    {{ if .Annotations.description }}Description: {{ .Annotations.description }}{{ end }}
    Labels:
    {{ range .Labels.SortedPairs }}  {{ .Name }}: {{ .Value }}
    {{ end }}
    {{ end }}
    {{ end }}

---
# Alertmanager Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: monitoring
  labels:
    app: alertmanager
spec:
  replicas: 3
  selector:
    matchLabels:
      app: alertmanager
  template:
    metadata:
      labels:
        app: alertmanager
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9093"
    spec:
      serviceAccountName: alertmanager
      priorityClassName: high-priority
      securityContext:
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
      containers:
      - name: alertmanager
        image: prom/alertmanager:v0.25.0
        args:
          - '--config.file=/etc/alertmanager/alertmanager.yml'
          - '--storage.path=/alertmanager'
          - '--data.retention=120h'
          - '--web.listen-address=:9093'
          - '--web.external-url=https://monitoring.retreatandbe.com/alertmanager'
          - '--cluster.listen-address=0.0.0.0:9094'
          - '--cluster.peer=alertmanager-0.alertmanager:9094'
          - '--cluster.peer=alertmanager-1.alertmanager:9094'
          - '--cluster.peer=alertmanager-2.alertmanager:9094'
        ports:
        - containerPort: 9093
          name: web
        - containerPort: 9094
          name: cluster
        env:
        - name: SMTP_PASSWORD
          valueFrom:
            secretKeyRef:
              name: alertmanager-secrets
              key: smtp-password
        - name: SLACK_WEBHOOK_URL
          valueFrom:
            secretKeyRef:
              name: alertmanager-secrets
              key: slack-webhook-url
        - name: PAGERDUTY_INTEGRATION_KEY
          valueFrom:
            secretKeyRef:
              name: alertmanager-secrets
              key: pagerduty-integration-key
        volumeMounts:
        - name: alertmanager-config
          mountPath: /etc/alertmanager
        - name: alertmanager-templates
          mountPath: /etc/alertmanager/templates
        - name: alertmanager-storage
          mountPath: /alertmanager
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1
            memory: 2Gi
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9093
          initialDelaySeconds: 30
          timeoutSeconds: 30
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9093
          initialDelaySeconds: 30
          timeoutSeconds: 30
      volumes:
      - name: alertmanager-config
        configMap:
          name: alertmanager-config
      - name: alertmanager-templates
        configMap:
          name: alertmanager-templates
      - name: alertmanager-storage
        emptyDir: {}

---
# Alertmanager Service
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: monitoring
  labels:
    app: alertmanager
spec:
  type: ClusterIP
  ports:
  - port: 9093
    targetPort: 9093
    name: web
  - port: 9094
    targetPort: 9094
    name: cluster
  selector:
    app: alertmanager

---
# Alertmanager ServiceAccount
apiVersion: v1
kind: ServiceAccount
metadata:
  name: alertmanager
  namespace: monitoring

---
# Alertmanager Secrets
apiVersion: v1
kind: Secret
metadata:
  name: alertmanager-secrets
  namespace: monitoring
type: Opaque
data:
  smtp-password: c210cF9wYXNzd29yZF8yMDI1 # smtp_password_2025
  slack-webhook-url: aHR0cHM6Ly9ob29rcy5zbGFjay5jb20vc2VydmljZXMvVDAwMDAwMDAwL0IwMDAwMDAwMC9YWFhYWFhYWFhYWFhYWFhYWFhYWA== # placeholder
  pagerduty-integration-key: cGFnZXJkdXR5X2ludGVncmF0aW9uX2tleV8yMDI1 # pagerduty_integration_key_2025

---
# Alertmanager HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: alertmanager-hpa
  namespace: monitoring
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: alertmanager
  minReplicas: 3
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
