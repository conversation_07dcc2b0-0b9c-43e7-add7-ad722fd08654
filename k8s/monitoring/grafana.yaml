# Grafana Production Stack - Sprint 18
# Date: 9 Juillet 2025
# Objectif: Dashboards et visualisation 24/7

# Grafana ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: monitoring
data:
  grafana.ini: |
    [analytics]
    check_for_updates = false
    reporting_enabled = false

    [security]
    admin_user = admin
    admin_password = $__env{GF_SECURITY_ADMIN_PASSWORD}
    secret_key = $__env{GF_SECURITY_SECRET_KEY}
    disable_gravatar = true

    [users]
    allow_sign_up = false
    allow_org_create = false
    auto_assign_org = true
    auto_assign_org_role = Viewer

    [auth]
    disable_login_form = false
    disable_signout_menu = false

    [auth.anonymous]
    enabled = false

    [log]
    mode = console
    level = info

    [paths]
    data = /var/lib/grafana
    logs = /var/log/grafana
    plugins = /var/lib/grafana/plugins
    provisioning = /etc/grafana/provisioning

    [server]
    protocol = http
    http_port = 3000
    domain = monitoring.retreatandbe.com
    root_url = https://monitoring.retreatandbe.com/grafana/
    serve_from_sub_path = true

    [database]
    type = postgres
    host = postgres:5432
    name = grafana
    user = grafana
    password = $__env{GF_DATABASE_PASSWORD}
    ssl_mode = require

    [session]
    provider = redis
    provider_config = addr=redis:6379,pool_size=100,db=grafana

    [alerting]
    enabled = true
    execute_alerts = true

    [metrics]
    enabled = true
    interval_seconds = 10

    [tracing.jaeger]
    address = jaeger:14268
    always_included_tag = cluster:production

---
# Grafana Datasources
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: monitoring
data:
  datasources.yaml: |
    apiVersion: 1
    datasources:
    - name: Prometheus
      type: prometheus
      access: proxy
      url: http://prometheus:9090
      isDefault: true
      editable: false
      jsonData:
        timeInterval: "15s"
        queryTimeout: "60s"
        httpMethod: "POST"
        manageAlerts: true
        prometheusType: "Prometheus"
        prometheusVersion: "2.45.0"
        cacheLevel: "High"
        incrementalQuerying: true
        incrementalQueryOverlapWindow: "10m"

    - name: Jaeger
      type: jaeger
      access: proxy
      url: http://jaeger-query:16686
      editable: false
      jsonData:
        tracesToLogs:
          datasourceUid: "loki"
          tags: ["job", "instance", "pod", "namespace"]
          mappedTags: [{"key": "service.name", "value": "service"}]
          mapTagNamesEnabled: true
          spanStartTimeShift: "1h"
          spanEndTimeShift: "1h"

    - name: Loki
      type: loki
      access: proxy
      url: http://loki:3100
      editable: false
      jsonData:
        maxLines: 1000
        derivedFields:
          - datasourceUid: "jaeger"
            matcherRegex: "traceID=(\\w+)"
            name: "TraceID"
            url: "$${__value.raw}"

---
# Grafana Dashboards ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards-config
  namespace: monitoring
data:
  dashboards.yaml: |
    apiVersion: 1
    providers:
    - name: 'default'
      orgId: 1
      folder: ''
      type: file
      disableDeletion: false
      updateIntervalSeconds: 10
      allowUiUpdates: true
      options:
        path: /var/lib/grafana/dashboards

---
# Grafana Production Dashboard
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-production-dashboard
  namespace: monitoring
data:
  production-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Production Overview - Retreat And Be",
        "tags": ["production", "overview"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Service Availability",
            "type": "stat",
            "targets": [
              {
                "expr": "avg(up)",
                "legendFormat": "Overall Availability"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "percentunit",
                "min": 0,
                "max": 1,
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "yellow", "value": 0.95},
                    {"color": "green", "value": 0.99}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total[5m]))",
                "legendFormat": "Total RPS"
              }
            ],
            "gridPos": {"h": 8, "w": 18, "x": 6, "y": 0}
          },
          {
            "id": 3,
            "title": "Response Time P95",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))",
                "legendFormat": "P95 Response Time"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "Error Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) / sum(rate(http_requests_total[5m]))",
                "legendFormat": "Error Rate"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ],
        "time": {"from": "now-1h", "to": "now"},
        "refresh": "30s"
      }
    }

---
# Grafana Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
  labels:
    app: grafana
spec:
  replicas: 2
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
    spec:
      serviceAccountName: grafana
      priorityClassName: high-priority
      securityContext:
        runAsUser: 472
        runAsGroup: 472
        fsGroup: 472
      containers:
      - name: grafana
        image: grafana/grafana:10.0.0
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: admin-password
        - name: GF_SECURITY_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: secret-key
        - name: GF_DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: database-password
        - name: GF_INSTALL_PLUGINS
          value: "grafana-clock-panel,grafana-simple-json-datasource,grafana-worldmap-panel,grafana-piechart-panel"
        volumeMounts:
        - name: grafana-config
          mountPath: /etc/grafana
        - name: grafana-storage
          mountPath: /var/lib/grafana
        - name: grafana-datasources
          mountPath: /etc/grafana/provisioning/datasources
        - name: grafana-dashboards-config
          mountPath: /etc/grafana/provisioning/dashboards
        - name: grafana-dashboards
          mountPath: /var/lib/grafana/dashboards
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          timeoutSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          timeoutSeconds: 30
      volumes:
      - name: grafana-config
        configMap:
          name: grafana-config
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-storage-pvc
      - name: grafana-datasources
        configMap:
          name: grafana-datasources
      - name: grafana-dashboards-config
        configMap:
          name: grafana-dashboards-config
      - name: grafana-dashboards
        configMap:
          name: grafana-production-dashboard

---
# Grafana Service
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: monitoring
  labels:
    app: grafana
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    name: http
  selector:
    app: grafana

---
# Grafana ServiceAccount
apiVersion: v1
kind: ServiceAccount
metadata:
  name: grafana
  namespace: monitoring

---
# Grafana Secrets
apiVersion: v1
kind: Secret
metadata:
  name: grafana-secrets
  namespace: monitoring
type: Opaque
data:
  admin-password: YWRtaW5fcGFzc3dvcmRfMjAyNQ== # admin_password_2025
  secret-key: Z3JhZmFuYV9zZWNyZXRfa2V5XzIwMjU= # grafana_secret_key_2025
  database-password: Z3JhZmFuYV9kYl9wYXNzd29yZF8yMDI1 # grafana_db_password_2025

---
# Grafana HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: grafana-hpa
  namespace: monitoring
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: grafana
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
