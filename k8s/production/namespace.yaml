# Kubernetes Namespaces - Sprint 18 Production
# Date: 9 Juillet 2025
# Objectif: Isolation des environnements production

apiVersion: v1
kind: Namespace
metadata:
  name: production
  labels:
    name: production
    environment: prod
    project: retreat-and-be
    managed-by: hanuman
  annotations:
    description: "Production environment for Retreat And Be platform"
    contact: "<EMAIL>"
    cost-center: "production"

---
apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
  labels:
    name: monitoring
    environment: prod
    project: retreat-and-be
    managed-by: hanuman
  annotations:
    description: "Monitoring and observability stack"
    contact: "<EMAIL>"

---
apiVersion: v1
kind: Namespace
metadata:
  name: ingress-system
  labels:
    name: ingress-system
    environment: prod
    project: retreat-and-be
    managed-by: hanuman
  annotations:
    description: "Ingress controllers and load balancers"
    contact: "<EMAIL>"

---
apiVersion: v1
kind: Namespace
metadata:
  name: backup-system
  labels:
    name: backup-system
    environment: prod
    project: retreat-and-be
    managed-by: hanuman
  annotations:
    description: "Backup and disaster recovery systems"
    contact: "<EMAIL>"

---
apiVersion: v1
kind: Namespace
metadata:
  name: security-system
  labels:
    name: security-system
    environment: prod
    project: retreat-and-be
    managed-by: hanuman
  annotations:
    description: "Security tools and compliance"
    contact: "<EMAIL>"

---
# Network Policies for Production Namespace
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: production-network-policy
  namespace: production
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-system
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector:
        matchLabels:
          app: kong-gateway
  - from:
    - podSelector: {}
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 5432
    - protocol: TCP
      port: 6379
    - protocol: TCP
      port: 9092
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  - to:
    - podSelector: {}

---
# Resource Quotas for Production
apiVersion: v1
kind: ResourceQuota
metadata:
  name: production-quota
  namespace: production
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "20"
    services: "50"
    secrets: "100"
    configmaps: "100"
    pods: "100"

---
# Limit Ranges for Production
apiVersion: v1
kind: LimitRange
metadata:
  name: production-limits
  namespace: production
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "100m"
      memory: "256Mi"
    type: Container
  - max:
      cpu: "4"
      memory: "8Gi"
    min:
      cpu: "50m"
      memory: "128Mi"
    type: Container

---
# Service Account for Production Services
apiVersion: v1
kind: ServiceAccount
metadata:
  name: production-service-account
  namespace: production
  labels:
    app: retreat-and-be
    environment: production
automountServiceAccountToken: true

---
# RBAC for Production Services
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: production
  name: production-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: production-role-binding
  namespace: production
subjects:
- kind: ServiceAccount
  name: production-service-account
  namespace: production
roleRef:
  kind: Role
  name: production-role
  apiGroup: rbac.authorization.k8s.io

---
# Priority Classes
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: high-priority
value: 1000
globalDefault: false
description: "High priority class for critical production services"

---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: medium-priority
value: 500
globalDefault: true
description: "Medium priority class for standard production services"

---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: low-priority
value: 100
globalDefault: false
description: "Low priority class for background services"

---
# Pod Disruption Budgets
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: hanuman-cortex-pdb
  namespace: production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: hanuman-cortex

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: rb2-backend-pdb
  namespace: production
spec:
  minAvailable: 3
  selector:
    matchLabels:
      app: rb2-backend

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: kong-gateway-pdb
  namespace: production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: kong-gateway
