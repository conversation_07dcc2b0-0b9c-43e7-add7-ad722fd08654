# Kubernetes Storage Configuration - Sprint 18 Production
# Date: 9 Juillet 2025
# Objectif: Storage classes et volumes persistants pour production

# Storage Classes
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fast-ssd
  labels:
    environment: production
    performance: high
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp3
  iops: "3000"
  throughput: "125"
  encrypted: "true"
allowVolumeExpansion: true
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer

---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: standard-ssd
  labels:
    environment: production
    performance: medium
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp3
  iops: "1000"
  throughput: "125"
  encrypted: "true"
allowVolumeExpansion: true
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer

---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: backup-storage
  labels:
    environment: production
    performance: low
    purpose: backup
provisioner: kubernetes.io/aws-ebs
parameters:
  type: sc1
  encrypted: "true"
allowVolumeExpansion: true
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer

---
# PostgreSQL Persistent Volumes
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-primary-pvc
  namespace: production
  labels:
    app: postgres
    role: primary
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 100Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-replica-pvc
  namespace: production
  labels:
    app: postgres
    role: replica
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 100Gi

---
# MongoDB Persistent Volumes
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mongodb-primary-pvc
  namespace: production
  labels:
    app: mongodb
    role: primary
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 200Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mongodb-secondary-pvc
  namespace: production
  labels:
    app: mongodb
    role: secondary
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard-ssd
  resources:
    requests:
      storage: 200Gi

---
# Redis Persistent Volumes
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-master-pvc
  namespace: production
  labels:
    app: redis
    role: master
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 50Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-replica-pvc
  namespace: production
  labels:
    app: redis
    role: replica
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard-ssd
  resources:
    requests:
      storage: 50Gi

---
# Kafka Persistent Volumes
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kafka-broker-0-pvc
  namespace: production
  labels:
    app: kafka
    broker-id: "0"
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 500Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kafka-broker-1-pvc
  namespace: production
  labels:
    app: kafka
    broker-id: "1"
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 500Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kafka-broker-2-pvc
  namespace: production
  labels:
    app: kafka
    broker-id: "2"
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 500Gi

---
# Weaviate Vector Database
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: weaviate-data-pvc
  namespace: production
  labels:
    app: weaviate
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 1Ti

---
# Hanuman Cortex Memory
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: hanuman-cortex-memory-pvc
  namespace: production
  labels:
    app: hanuman-cortex
    component: memory
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 100Gi

---
# Shared Workspace for Agents
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: hanuman-shared-workspace-pvc
  namespace: production
  labels:
    app: hanuman
    component: shared-workspace
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: standard-ssd
  resources:
    requests:
      storage: 200Gi

---
# Backup Storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backup-storage-pvc
  namespace: backup-system
  labels:
    app: backup
    purpose: disaster-recovery
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: backup-storage
  resources:
    requests:
      storage: 2Ti

---
# Monitoring Storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-storage-pvc
  namespace: monitoring
  labels:
    app: prometheus
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 500Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-storage-pvc
  namespace: monitoring
  labels:
    app: grafana
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard-ssd
  resources:
    requests:
      storage: 50Gi

---
# Volume Snapshot Classes
apiVersion: snapshot.storage.k8s.io/v1
kind: VolumeSnapshotClass
metadata:
  name: production-snapshot-class
  labels:
    environment: production
driver: ebs.csi.aws.com
deletionPolicy: Retain
parameters:
  encrypted: "true"

---
# Scheduled Volume Snapshots
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-schedule
  namespace: backup-system
data:
  postgres-backup.yaml: |
    apiVersion: snapshot.storage.k8s.io/v1
    kind: VolumeSnapshot
    metadata:
      name: postgres-snapshot-$(date +%Y%m%d-%H%M%S)
      namespace: production
    spec:
      volumeSnapshotClassName: production-snapshot-class
      source:
        persistentVolumeClaimName: postgres-primary-pvc
  
  mongodb-backup.yaml: |
    apiVersion: snapshot.storage.k8s.io/v1
    kind: VolumeSnapshot
    metadata:
      name: mongodb-snapshot-$(date +%Y%m%d-%H%M%S)
      namespace: production
    spec:
      volumeSnapshotClassName: production-snapshot-class
      source:
        persistentVolumeClaimName: mongodb-primary-pvc

---
# Storage Monitoring
apiVersion: v1
kind: ConfigMap
metadata:
  name: storage-monitoring
  namespace: monitoring
data:
  storage-alerts.yaml: |
    groups:
    - name: storage.rules
      rules:
      - alert: PersistentVolumeUsageHigh
        expr: kubelet_volume_stats_used_bytes / kubelet_volume_stats_capacity_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PV usage high ({{ $value }}%)"
          description: "PV {{ $labels.persistentvolumeclaim }} usage is above 80%"
      
      - alert: PersistentVolumeUsageCritical
        expr: kubelet_volume_stats_used_bytes / kubelet_volume_stats_capacity_bytes > 0.9
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "PV usage critical ({{ $value }}%)"
          description: "PV {{ $labels.persistentvolumeclaim }} usage is above 90%"
