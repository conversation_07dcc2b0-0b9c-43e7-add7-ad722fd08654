{"name": "@retreatandbe/design-system", "version": "1.0.0", "description": "Design System unifié pour Retreat And Be - Sprint 15", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "@storybook/react": "^7.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "rollup": "^3.0.0", "typescript": "^5.0.0"}, "dependencies": {"clsx": "^2.0.0", "tailwindcss": "^3.4.0"}}