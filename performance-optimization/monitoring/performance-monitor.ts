/**
 * 📊 SERVICE DE MONITORING PERFORMANCE
 * 
 * Collecte et analyse les métriques de performance en temps réel
 * Fait partie des optimisations Sprint 16 - Performance
 */

export interface PerformanceMetric {
  timestamp: number;
  type: 'request' | 'database' | 'cache' | 'external_api';
  duration: number;
  endpoint?: string;
  method?: string;
  statusCode?: number;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface PerformanceReport {
  period: {
    start: Date;
    end: Date;
    duration: number;
  };
  metrics: {
    totalRequests: number;
    averageDuration: number;
    p50: number;
    p95: number;
    p99: number;
    errorRate: number;
  };
  breakdown: {
    byEndpoint: Record<string, PerformanceMetric[]>;
    byMethod: Record<string, PerformanceMetric[]>;
    byStatusCode: Record<string, number>;
  };
  alerts: Array<{
    type: 'slow_request' | 'high_error_rate' | 'performance_degradation';
    message: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    timestamp: Date;
  }>;
}

/**
 * 📈 MONITEUR DE PERFORMANCE PRINCIPAL
 */
export class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private readonly maxMetrics = 10000; // Limite pour éviter la surcharge mémoire
  private readonly alertThresholds = {
    slowRequest: 100, // ms
    verySlowRequest: 1000, // ms
    highErrorRate: 5, // %
    performanceDegradation: 20, // % d'augmentation
  };

  /**
   * Enregistre une métrique de performance
   */
  recordMetric(metric: PerformanceMetric): void {
    metric.timestamp = Date.now();
    this.metrics.push(metric);

    // Nettoyer les anciennes métriques si nécessaire
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Vérifier les alertes
    this.checkAlerts(metric);
  }

  /**
   * Enregistre une requête HTTP
   */
  recordRequest(
    endpoint: string,
    method: string,
    duration: number,
    statusCode: number,
    userId?: string
  ): void {
    this.recordMetric({
      timestamp: Date.now(),
      type: 'request',
      duration,
      endpoint,
      method,
      statusCode,
      userId,
    });
  }

  /**
   * Enregistre une requête de base de données
   */
  recordDatabaseQuery(
    query: string,
    duration: number,
    metadata?: Record<string, any>
  ): void {
    this.recordMetric({
      timestamp: Date.now(),
      type: 'database',
      duration,
      endpoint: query,
      metadata,
    });
  }

  /**
   * Enregistre une opération de cache
   */
  recordCacheOperation(
    operation: 'hit' | 'miss' | 'set' | 'delete',
    key: string,
    duration: number
  ): void {
    this.recordMetric({
      timestamp: Date.now(),
      type: 'cache',
      duration,
      endpoint: key,
      method: operation,
    });
  }

  /**
   * Calcule les percentiles
   */
  private calculatePercentiles(durations: number[]): {
    p50: number;
    p95: number;
    p99: number;
  } {
    if (durations.length === 0) {
      return { p50: 0, p95: 0, p99: 0 };
    }

    const sorted = durations.sort((a, b) => a - b);
    const length = sorted.length;

    return {
      p50: sorted[Math.floor(length * 0.5)] || 0,
      p95: sorted[Math.floor(length * 0.95)] || 0,
      p99: sorted[Math.floor(length * 0.99)] || 0,
    };
  }

  /**
   * Génère un rapport de performance
   */
  generateReport(
    startTime?: Date,
    endTime?: Date
  ): PerformanceReport {
    const start = startTime || new Date(Date.now() - 3600000); // 1 heure par défaut
    const end = endTime || new Date();

    // Filtrer les métriques par période
    const filteredMetrics = this.metrics.filter(
      m => m.timestamp >= start.getTime() && m.timestamp <= end.getTime()
    );

    // Calculer les métriques globales
    const durations = filteredMetrics.map(m => m.duration);
    const requestMetrics = filteredMetrics.filter(m => m.type === 'request');
    const errorCount = requestMetrics.filter(m => 
      m.statusCode && m.statusCode >= 400
    ).length;

    const percentiles = this.calculatePercentiles(durations);

    // Grouper par endpoint
    const byEndpoint: Record<string, PerformanceMetric[]> = {};
    const byMethod: Record<string, PerformanceMetric[]> = {};
    const byStatusCode: Record<string, number> = {};

    filteredMetrics.forEach(metric => {
      if (metric.endpoint) {
        if (!byEndpoint[metric.endpoint]) {
          byEndpoint[metric.endpoint] = [];
        }
        byEndpoint[metric.endpoint].push(metric);
      }

      if (metric.method) {
        if (!byMethod[metric.method]) {
          byMethod[metric.method] = [];
        }
        byMethod[metric.method].push(metric);
      }

      if (metric.statusCode) {
        const code = metric.statusCode.toString();
        byStatusCode[code] = (byStatusCode[code] || 0) + 1;
      }
    });

    // Générer les alertes
    const alerts = this.generateAlerts(filteredMetrics);

    return {
      period: {
        start,
        end,
        duration: end.getTime() - start.getTime(),
      },
      metrics: {
        totalRequests: filteredMetrics.length,
        averageDuration: durations.length > 0 
          ? durations.reduce((a, b) => a + b, 0) / durations.length 
          : 0,
        ...percentiles,
        errorRate: requestMetrics.length > 0 
          ? (errorCount / requestMetrics.length) * 100 
          : 0,
      },
      breakdown: {
        byEndpoint,
        byMethod,
        byStatusCode,
      },
      alerts,
    };
  }

  /**
   * Vérifie et génère les alertes
   */
  private checkAlerts(metric: PerformanceMetric): void {
    // Alerte pour requête lente
    if (metric.duration > this.alertThresholds.verySlowRequest) {
      console.warn(`🚨 REQUÊTE TRÈS LENTE: ${metric.endpoint} - ${metric.duration}ms`);
    } else if (metric.duration > this.alertThresholds.slowRequest) {
      console.warn(`⚠️ REQUÊTE LENTE: ${metric.endpoint} - ${metric.duration}ms`);
    }
  }

  /**
   * Génère les alertes pour un rapport
   */
  private generateAlerts(metrics: PerformanceMetric[]): Array<{
    type: 'slow_request' | 'high_error_rate' | 'performance_degradation';
    message: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    timestamp: Date;
  }> {
    const alerts: any[] = [];

    // Vérifier les requêtes lentes
    const slowRequests = metrics.filter(m => 
      m.duration > this.alertThresholds.slowRequest
    );

    if (slowRequests.length > 0) {
      const percentage = (slowRequests.length / metrics.length) * 100;
      alerts.push({
        type: 'slow_request',
        message: `${slowRequests.length} requêtes lentes détectées (${percentage.toFixed(1)}%)`,
        severity: percentage > 10 ? 'high' : 'medium',
        timestamp: new Date(),
      });
    }

    // Vérifier le taux d'erreur
    const errorRequests = metrics.filter(m => 
      m.statusCode && m.statusCode >= 400
    );

    if (errorRequests.length > 0) {
      const errorRate = (errorRequests.length / metrics.length) * 100;
      if (errorRate > this.alertThresholds.highErrorRate) {
        alerts.push({
          type: 'high_error_rate',
          message: `Taux d'erreur élevé: ${errorRate.toFixed(1)}%`,
          severity: errorRate > 15 ? 'critical' : 'high',
          timestamp: new Date(),
        });
      }
    }

    return alerts;
  }

  /**
   * Obtient les métriques en temps réel
   */
  getRealTimeMetrics(): {
    currentRequests: number;
    averageResponseTime: number;
    requestsPerSecond: number;
    errorRate: number;
  } {
    const now = Date.now();
    const lastMinute = now - 60000; // 1 minute

    const recentMetrics = this.metrics.filter(m => 
      m.timestamp >= lastMinute && m.type === 'request'
    );

    const durations = recentMetrics.map(m => m.duration);
    const errors = recentMetrics.filter(m => 
      m.statusCode && m.statusCode >= 400
    );

    return {
      currentRequests: recentMetrics.length,
      averageResponseTime: durations.length > 0 
        ? durations.reduce((a, b) => a + b, 0) / durations.length 
        : 0,
      requestsPerSecond: recentMetrics.length / 60,
      errorRate: recentMetrics.length > 0 
        ? (errors.length / recentMetrics.length) * 100 
        : 0,
    };
  }

  /**
   * Nettoie les anciennes métriques
   */
  cleanup(olderThan: Date = new Date(Date.now() - 86400000)): number {
    const initialLength = this.metrics.length;
    this.metrics = this.metrics.filter(m => m.timestamp >= olderThan.getTime());
    return initialLength - this.metrics.length;
  }

  /**
   * Exporte les métriques au format JSON
   */
  exportMetrics(startTime?: Date, endTime?: Date): string {
    const report = this.generateReport(startTime, endTime);
    return JSON.stringify(report, null, 2);
  }

  /**
   * Réinitialise toutes les métriques
   */
  reset(): void {
    this.metrics = [];
  }

  /**
   * Obtient le nombre total de métriques
   */
  getMetricsCount(): number {
    return this.metrics.length;
  }
}

/**
 * 🌍 INSTANCE GLOBALE DU MONITEUR
 */
export const performanceMonitor = new PerformanceMonitor();

/**
 * 🔧 UTILITAIRES DE MONITORING
 */
export class MonitoringUtils {
  /**
   * Formate la durée en format lisible
   */
  static formatDuration(ms: number): string {
    if (ms < 1) return `${(ms * 1000).toFixed(0)}μs`;
    if (ms < 1000) return `${ms.toFixed(1)}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  }

  /**
   * Détermine la couleur selon la performance
   */
  static getPerformanceColor(duration: number): string {
    if (duration <= 50) return 'green';
    if (duration <= 100) return 'yellow';
    if (duration <= 500) return 'orange';
    return 'red';
  }

  /**
   * Génère un résumé textuel des métriques
   */
  static generateSummary(report: PerformanceReport): string {
    const { metrics } = report;
    
    return `
📊 RÉSUMÉ PERFORMANCE
====================
• Requêtes totales: ${metrics.totalRequests}
• Temps moyen: ${this.formatDuration(metrics.averageDuration)}
• P95: ${this.formatDuration(metrics.p95)}
• Taux d'erreur: ${metrics.errorRate.toFixed(1)}%
• Alertes: ${report.alerts.length}
    `.trim();
  }
}

/**
 * 📈 DÉCORATEUR POUR MONITORING AUTOMATIQUE
 */
export function MonitorPerformance(type: string = 'function') {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      
      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;
        
        performanceMonitor.recordMetric({
          timestamp: startTime,
          type: 'external_api',
          duration,
          endpoint: `${target.constructor.name}.${propertyName}`,
          method: type,
          metadata: { args: args.length },
        });
        
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        
        performanceMonitor.recordMetric({
          timestamp: startTime,
          type: 'external_api',
          duration,
          endpoint: `${target.constructor.name}.${propertyName}`,
          method: type,
          statusCode: 500,
          metadata: { error: error.message },
        });
        
        throw error;
      }
    };
  };
}
