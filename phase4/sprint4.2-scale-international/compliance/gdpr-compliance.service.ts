/**
 * 🔒 Service de Compliance GDPR
 * Phase 4 Sprint 4.2 : Conformité GDPR automatique et gestion des droits
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../../prisma/prisma.service';

interface PersonalData {
  id: string;
  userId: string;
  dataType: 'profile' | 'behavioral' | 'transactional' | 'technical';
  category: string;
  data: Record<string, any>;
  purpose: string[];
  legalBasis: 'consent' | 'contract' | 'legal_obligation' | 'vital_interests' | 'public_task' | 'legitimate_interests';
  retention: {
    period: number; // en jours
    reason: string;
  };
  consent: {
    given: boolean;
    timestamp: Date;
    version: string;
    withdrawable: boolean;
  };
  processing: {
    automated: boolean;
    profiling: boolean;
    thirdParty: boolean;
  };
  location: string; // région de stockage
  encrypted: boolean;
  anonymized: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface ConsentRecord {
  id: string;
  userId: string;
  purpose: string;
  granted: boolean;
  timestamp: Date;
  version: string;
  method: 'explicit' | 'implicit' | 'opt_in' | 'opt_out';
  ipAddress: string;
  userAgent: string;
  withdrawn?: Date;
  withdrawnReason?: string;
}

interface DataSubjectRequest {
  id: string;
  userId: string;
  type: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction' | 'objection';
  status: 'pending' | 'processing' | 'completed' | 'rejected';
  requestDate: Date;
  completionDate?: Date;
  details: Record<string, any>;
  verificationMethod: string;
  responseData?: any;
}

@Injectable()
export class GDPRComplianceService {
  private readonly logger = new Logger(GDPRComplianceService.name);
  private dataInventory: Map<string, PersonalData[]> = new Map();
  private consentRecords: Map<string, ConsentRecord[]> = new Map();
  private activeRequests: Map<string, DataSubjectRequest> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeGDPRCompliance();
  }

  /**
   * 🚀 Initialiser le système de compliance GDPR
   */
  private async initializeGDPRCompliance() {
    try {
      this.logger.log('🚀 Initialisation du système GDPR...');

      // Charger l'inventaire des données existantes
      await this.loadDataInventory();

      // Vérifier les consentements
      await this.validateConsents();

      // Configurer les politiques de rétention
      await this.setupRetentionPolicies();

      this.logger.log('✅ Système GDPR initialisé');

    } catch (error) {
      this.logger.error('❌ Erreur initialisation GDPR:', error);
    }
  }

  /**
   * 📊 Enregistrer des données personnelles
   */
  async registerPersonalData(data: Omit<PersonalData, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const dataRecord: PersonalData = {
        id: `pd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Ajouter à l'inventaire
      const userInventory = this.dataInventory.get(data.userId) || [];
      userInventory.push(dataRecord);
      this.dataInventory.set(data.userId, userInventory);

      // Vérifier le consentement
      if (data.legalBasis === 'consent') {
        await this.verifyConsent(data.userId, data.purpose);
      }

      // Émettre un événement
      this.eventEmitter.emit('gdpr.data.registered', {
        userId: data.userId,
        dataType: data.dataType,
        purpose: data.purpose,
        timestamp: new Date()
      });

      this.logger.debug(`📊 Données personnelles enregistrées: ${dataRecord.id}`);
      return dataRecord.id;

    } catch (error) {
      this.logger.error('❌ Erreur enregistrement données:', error);
      throw error;
    }
  }

  /**
   * ✅ Enregistrer un consentement
   */
  async recordConsent(consent: Omit<ConsentRecord, 'id'>): Promise<string> {
    try {
      const consentRecord: ConsentRecord = {
        id: `consent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...consent
      };

      // Ajouter aux enregistrements
      const userConsents = this.consentRecords.get(consent.userId) || [];
      userConsents.push(consentRecord);
      this.consentRecords.set(consent.userId, userConsents);

      // Mettre à jour les données associées
      await this.updateDataConsent(consent.userId, consent.purpose, consent.granted);

      // Émettre un événement
      this.eventEmitter.emit('gdpr.consent.recorded', {
        userId: consent.userId,
        purpose: consent.purpose,
        granted: consent.granted,
        timestamp: new Date()
      });

      this.logger.log(`✅ Consentement enregistré: ${consent.userId} -> ${consent.purpose} (${consent.granted ? 'accordé' : 'refusé'})`);
      return consentRecord.id;

    } catch (error) {
      this.logger.error('❌ Erreur enregistrement consentement:', error);
      throw error;
    }
  }

  /**
   * 🔄 Retirer un consentement
   */
  async withdrawConsent(userId: string, purpose: string, reason?: string): Promise<boolean> {
    try {
      const userConsents = this.consentRecords.get(userId) || [];
      const consentIndex = userConsents.findIndex(c => c.purpose === purpose && c.granted && !c.withdrawn);

      if (consentIndex === -1) {
        throw new Error(`Consentement actif non trouvé pour ${purpose}`);
      }

      // Marquer comme retiré
      userConsents[consentIndex].withdrawn = new Date();
      userConsents[consentIndex].withdrawnReason = reason;

      // Mettre à jour les données
      await this.updateDataConsent(userId, purpose, false);

      // Déclencher les actions de retrait
      await this.handleConsentWithdrawal(userId, purpose);

      this.logger.log(`🔄 Consentement retiré: ${userId} -> ${purpose}`);
      return true;

    } catch (error) {
      this.logger.error('❌ Erreur retrait consentement:', error);
      throw error;
    }
  }

  /**
   * 📋 Traiter une demande de droit GDPR
   */
  async processDataSubjectRequest(request: Omit<DataSubjectRequest, 'id' | 'status' | 'requestDate'>): Promise<string> {
    try {
      const requestRecord: DataSubjectRequest = {
        id: `dsr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        status: 'pending',
        requestDate: new Date(),
        ...request
      };

      this.activeRequests.set(requestRecord.id, requestRecord);

      // Traitement selon le type de demande
      switch (request.type) {
        case 'access':
          await this.processAccessRequest(requestRecord);
          break;
        case 'rectification':
          await this.processRectificationRequest(requestRecord);
          break;
        case 'erasure':
          await this.processErasureRequest(requestRecord);
          break;
        case 'portability':
          await this.processPortabilityRequest(requestRecord);
          break;
        case 'restriction':
          await this.processRestrictionRequest(requestRecord);
          break;
        case 'objection':
          await this.processObjectionRequest(requestRecord);
          break;
      }

      this.logger.log(`📋 Demande GDPR traitée: ${request.type} pour ${request.userId}`);
      return requestRecord.id;

    } catch (error) {
      this.logger.error('❌ Erreur traitement demande GDPR:', error);
      throw error;
    }
  }

  /**
   * 👁️ Traiter une demande d'accès (Article 15)
   */
  private async processAccessRequest(request: DataSubjectRequest): Promise<void> {
    try {
      request.status = 'processing';

      // Collecter toutes les données de l'utilisateur
      const userData = this.dataInventory.get(request.userId) || [];
      const userConsents = this.consentRecords.get(request.userId) || [];

      const accessData = {
        personalData: userData.map(data => ({
          type: data.dataType,
          category: data.category,
          purpose: data.purpose,
          legalBasis: data.legalBasis,
          retention: data.retention,
          processing: data.processing,
          location: data.location,
          createdAt: data.createdAt
        })),
        consents: userConsents.map(consent => ({
          purpose: consent.purpose,
          granted: consent.granted,
          timestamp: consent.timestamp,
          method: consent.method,
          withdrawn: consent.withdrawn
        })),
        dataProcessing: {
          automated: userData.some(d => d.processing.automated),
          profiling: userData.some(d => d.processing.profiling),
          thirdParty: userData.some(d => d.processing.thirdParty)
        },
        rights: {
          rectification: true,
          erasure: true,
          portability: true,
          restriction: true,
          objection: true
        }
      };

      request.responseData = accessData;
      request.status = 'completed';
      request.completionDate = new Date();

      this.activeRequests.set(request.id, request);

    } catch (error) {
      request.status = 'rejected';
      this.logger.error('❌ Erreur demande d\'accès:', error);
    }
  }

  /**
   * ✏️ Traiter une demande de rectification (Article 16)
   */
  private async processRectificationRequest(request: DataSubjectRequest): Promise<void> {
    try {
      request.status = 'processing';

      const { dataId, corrections } = request.details;
      const userData = this.dataInventory.get(request.userId) || [];
      const dataIndex = userData.findIndex(d => d.id === dataId);

      if (dataIndex === -1) {
        throw new Error('Données non trouvées');
      }

      // Appliquer les corrections
      const originalData = { ...userData[dataIndex].data };
      userData[dataIndex].data = { ...userData[dataIndex].data, ...corrections };
      userData[dataIndex].updatedAt = new Date();

      this.dataInventory.set(request.userId, userData);

      // Log de l'audit
      this.eventEmitter.emit('gdpr.data.rectified', {
        userId: request.userId,
        dataId,
        originalData,
        newData: userData[dataIndex].data,
        timestamp: new Date()
      });

      request.status = 'completed';
      request.completionDate = new Date();

    } catch (error) {
      request.status = 'rejected';
      this.logger.error('❌ Erreur rectification:', error);
    }
  }

  /**
   * 🗑️ Traiter une demande d'effacement (Article 17)
   */
  private async processErasureRequest(request: DataSubjectRequest): Promise<void> {
    try {
      request.status = 'processing';

      // Vérifier les conditions d'effacement
      const canErase = await this.verifyErasureConditions(request.userId);
      if (!canErase) {
        request.status = 'rejected';
        return;
      }

      // Effacer les données
      await this.eraseUserData(request.userId);

      // Notifier les tiers
      await this.notifyThirdParties(request.userId, 'erasure');

      request.status = 'completed';
      request.completionDate = new Date();

      this.logger.log(`🗑️ Données effacées pour l'utilisateur: ${request.userId}`);

    } catch (error) {
      request.status = 'rejected';
      this.logger.error('❌ Erreur effacement:', error);
    }
  }

  /**
   * 📦 Traiter une demande de portabilité (Article 20)
   */
  private async processPortabilityRequest(request: DataSubjectRequest): Promise<void> {
    try {
      request.status = 'processing';

      const userData = this.dataInventory.get(request.userId) || [];
      
      // Filtrer les données portables (consentement ou contrat)
      const portableData = userData.filter(data => 
        data.legalBasis === 'consent' || data.legalBasis === 'contract'
      );

      // Formater en JSON structuré
      const exportData = {
        userId: request.userId,
        exportDate: new Date().toISOString(),
        data: portableData.map(data => ({
          type: data.dataType,
          category: data.category,
          data: data.data,
          createdAt: data.createdAt
        }))
      };

      request.responseData = exportData;
      request.status = 'completed';
      request.completionDate = new Date();

    } catch (error) {
      request.status = 'rejected';
      this.logger.error('❌ Erreur portabilité:', error);
    }
  }

  /**
   * ⏸️ Traiter une demande de limitation (Article 18)
   */
  private async processRestrictionRequest(request: DataSubjectRequest): Promise<void> {
    try {
      request.status = 'processing';

      const userData = this.dataInventory.get(request.userId) || [];
      
      // Marquer les données comme restreintes
      userData.forEach(data => {
        data.processing = {
          ...data.processing,
          restricted: true,
          restrictionReason: request.details.reason
        };
      });

      this.dataInventory.set(request.userId, userData);

      request.status = 'completed';
      request.completionDate = new Date();

    } catch (error) {
      request.status = 'rejected';
      this.logger.error('❌ Erreur limitation:', error);
    }
  }

  /**
   * ❌ Traiter une objection (Article 21)
   */
  private async processObjectionRequest(request: DataSubjectRequest): Promise<void> {
    try {
      request.status = 'processing';

      const { purpose } = request.details;
      
      // Arrêter le traitement pour ce purpose
      await this.stopProcessingForPurpose(request.userId, purpose);

      request.status = 'completed';
      request.completionDate = new Date();

    } catch (error) {
      request.status = 'rejected';
      this.logger.error('❌ Erreur objection:', error);
    }
  }

  /**
   * 🔄 Tâche périodique de nettoyage des données
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async performDataRetentionCleanup() {
    try {
      this.logger.log('🔄 Démarrage du nettoyage de rétention...');

      let cleanedCount = 0;

      for (const [userId, userData] of this.dataInventory.entries()) {
        const filteredData = userData.filter(data => {
          const retentionExpired = this.isRetentionExpired(data);
          if (retentionExpired) {
            cleanedCount++;
            this.logger.debug(`🗑️ Données expirées supprimées: ${data.id}`);
          }
          return !retentionExpired;
        });

        if (filteredData.length !== userData.length) {
          this.dataInventory.set(userId, filteredData);
        }
      }

      this.logger.log(`✅ Nettoyage terminé: ${cleanedCount} enregistrements supprimés`);

    } catch (error) {
      this.logger.error('❌ Erreur nettoyage rétention:', error);
    }
  }

  /**
   * 📊 Obtenir le statut de compliance GDPR
   */
  getComplianceStatus(): {
    totalUsers: number;
    totalDataRecords: number;
    activeConsents: number;
    pendingRequests: number;
    complianceScore: number;
  } {
    const totalUsers = this.dataInventory.size;
    const totalDataRecords = Array.from(this.dataInventory.values())
      .reduce((sum, userData) => sum + userData.length, 0);
    
    const activeConsents = Array.from(this.consentRecords.values())
      .reduce((sum, consents) => sum + consents.filter(c => c.granted && !c.withdrawn).length, 0);
    
    const pendingRequests = Array.from(this.activeRequests.values())
      .filter(r => r.status === 'pending').length;

    // Score de compliance basé sur plusieurs facteurs
    const complianceScore = this.calculateComplianceScore();

    return {
      totalUsers,
      totalDataRecords,
      activeConsents,
      pendingRequests,
      complianceScore
    };
  }

  // Méthodes utilitaires privées
  private async loadDataInventory(): Promise<void> {
    // Charger depuis la base de données
    this.logger.log('📚 Chargement de l\'inventaire des données...');
  }

  private async validateConsents(): Promise<void> {
    // Valider les consentements existants
    this.logger.log('✅ Validation des consentements...');
  }

  private async setupRetentionPolicies(): Promise<void> {
    // Configurer les politiques de rétention
    this.logger.log('⏰ Configuration des politiques de rétention...');
  }

  private async verifyConsent(userId: string, purposes: string[]): Promise<boolean> {
    const userConsents = this.consentRecords.get(userId) || [];
    return purposes.every(purpose => 
      userConsents.some(c => c.purpose === purpose && c.granted && !c.withdrawn)
    );
  }

  private async updateDataConsent(userId: string, purpose: string, granted: boolean): Promise<void> {
    const userData = this.dataInventory.get(userId) || [];
    userData.forEach(data => {
      if (data.purpose.includes(purpose)) {
        data.consent.given = granted;
        data.consent.timestamp = new Date();
      }
    });
  }

  private async handleConsentWithdrawal(userId: string, purpose: string): Promise<void> {
    // Arrêter le traitement et potentiellement supprimer les données
    await this.stopProcessingForPurpose(userId, purpose);
  }

  private async verifyErasureConditions(userId: string): Promise<boolean> {
    // Vérifier si l'effacement est légalement possible
    return true; // Simplification
  }

  private async eraseUserData(userId: string): Promise<void> {
    this.dataInventory.delete(userId);
    this.consentRecords.delete(userId);
  }

  private async notifyThirdParties(userId: string, action: string): Promise<void> {
    // Notifier les partenaires de l'action
    this.logger.log(`📧 Notification tiers: ${action} pour ${userId}`);
  }

  private async stopProcessingForPurpose(userId: string, purpose: string): Promise<void> {
    const userData = this.dataInventory.get(userId) || [];
    userData.forEach(data => {
      if (data.purpose.includes(purpose)) {
        data.processing = { ...data.processing, stopped: true };
      }
    });
  }

  private isRetentionExpired(data: PersonalData): boolean {
    const expirationDate = new Date(data.createdAt);
    expirationDate.setDate(expirationDate.getDate() + data.retention.period);
    return new Date() > expirationDate;
  }

  private calculateComplianceScore(): number {
    // Calcul simplifié du score de compliance
    return 85; // Simulation
  }

  /**
   * 🧹 Nettoyage des ressources
   */
  async cleanup(): Promise<void> {
    this.dataInventory.clear();
    this.consentRecords.clear();
    this.activeRequests.clear();
    this.logger.log('🧹 Service GDPR nettoyé');
  }
}
