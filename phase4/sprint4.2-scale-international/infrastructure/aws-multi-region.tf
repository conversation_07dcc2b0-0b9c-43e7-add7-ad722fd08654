# 🌍 AWS Multi-Region Infrastructure
# Phase 4 Sprint 4.2 : Configuration Terraform pour déploiement global

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Variables globales
variable "project_name" {
  description = "Nom du projet"
  type        = string
  default     = "hanuman-global"
}

variable "environment" {
  description = "Environnement de déploiement"
  type        = string
  default     = "production"
}

variable "regions" {
  description = "Régions de déploiement"
  type = map(object({
    name        = string
    is_primary  = bool
    cidr_block  = string
  }))
  default = {
    us_east_1 = {
      name       = "us-east-1"
      is_primary = true
      cidr_block = "10.0.0.0/16"
    }
    eu_west_1 = {
      name       = "eu-west-1"
      is_primary = false
      cidr_block = "********/16"
    }
    ap_northeast_1 = {
      name       = "ap-northeast-1"
      is_primary = false
      cidr_block = "********/16"
    }
  }
}

# Providers pour chaque région
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
  
  default_tags {
    tags = {
      Project     = var.project_name
      Environment = var.environment
      Region      = "us-east-1"
      ManagedBy   = "terraform"
    }
  }
}

provider "aws" {
  alias  = "eu_west_1"
  region = "eu-west-1"
  
  default_tags {
    tags = {
      Project     = var.project_name
      Environment = var.environment
      Region      = "eu-west-1"
      ManagedBy   = "terraform"
    }
  }
}

provider "aws" {
  alias  = "ap_northeast_1"
  region = "ap-northeast-1"
  
  default_tags {
    tags = {
      Project     = var.project_name
      Environment = var.environment
      Region      = "ap-northeast-1"
      ManagedBy   = "terraform"
    }
  }
}

# VPC pour chaque région
resource "aws_vpc" "main" {
  for_each = var.regions
  
  provider             = aws.${replace(each.key, "_", "")}
  cidr_block           = each.value.cidr_block
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = {
    Name = "${var.project_name}-vpc-${each.value.name}"
    Type = each.value.is_primary ? "primary" : "secondary"
  }
}

# Subnets publics
resource "aws_subnet" "public" {
  for_each = {
    for region_key, region in var.regions : region_key => {
      for az_index in range(2) : "${region_key}-${az_index}" => {
        region_key = region_key
        region     = region
        az_index   = az_index
        cidr_block = cidrsubnet(region.cidr_block, 8, az_index)
      }
    }
  }
  
  provider                = aws.${replace(each.value.region_key, "_", "")}
  vpc_id                  = aws_vpc.main[each.value.region_key].id
  cidr_block              = each.value.cidr_block
  availability_zone       = data.aws_availability_zones.available[each.value.region_key].names[each.value.az_index]
  map_public_ip_on_launch = true
  
  tags = {
    Name = "${var.project_name}-public-${each.value.region.name}-${each.value.az_index + 1}"
    Type = "public"
  }
}

# Subnets privés
resource "aws_subnet" "private" {
  for_each = {
    for region_key, region in var.regions : region_key => {
      for az_index in range(2) : "${region_key}-${az_index}" => {
        region_key = region_key
        region     = region
        az_index   = az_index
        cidr_block = cidrsubnet(region.cidr_block, 8, az_index + 10)
      }
    }
  }
  
  provider          = aws.${replace(each.value.region_key, "_", "")}
  vpc_id            = aws_vpc.main[each.value.region_key].id
  cidr_block        = each.value.cidr_block
  availability_zone = data.aws_availability_zones.available[each.value.region_key].names[each.value.az_index]
  
  tags = {
    Name = "${var.project_name}-private-${each.value.region.name}-${each.value.az_index + 1}"
    Type = "private"
  }
}

# Internet Gateways
resource "aws_internet_gateway" "main" {
  for_each = var.regions
  
  provider = aws.${replace(each.key, "_", "")}
  vpc_id   = aws_vpc.main[each.key].id
  
  tags = {
    Name = "${var.project_name}-igw-${each.value.name}"
  }
}

# NAT Gateways
resource "aws_eip" "nat" {
  for_each = var.regions
  
  provider = aws.${replace(each.key, "_", "")}
  domain   = "vpc"
  
  tags = {
    Name = "${var.project_name}-nat-eip-${each.value.name}"
  }
}

resource "aws_nat_gateway" "main" {
  for_each = var.regions
  
  provider      = aws.${replace(each.key, "_", "")}
  allocation_id = aws_eip.nat[each.key].id
  subnet_id     = aws_subnet.public["${each.key}-0"].id
  
  tags = {
    Name = "${var.project_name}-nat-${each.value.name}"
  }
  
  depends_on = [aws_internet_gateway.main]
}

# Route Tables
resource "aws_route_table" "public" {
  for_each = var.regions
  
  provider = aws.${replace(each.key, "_", "")}
  vpc_id   = aws_vpc.main[each.key].id
  
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main[each.key].id
  }
  
  tags = {
    Name = "${var.project_name}-rt-public-${each.value.name}"
  }
}

resource "aws_route_table" "private" {
  for_each = var.regions
  
  provider = aws.${replace(each.key, "_", "")}
  vpc_id   = aws_vpc.main[each.key].id
  
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.main[each.key].id
  }
  
  tags = {
    Name = "${var.project_name}-rt-private-${each.value.name}"
  }
}

# EKS Clusters
resource "aws_eks_cluster" "main" {
  for_each = var.regions
  
  provider = aws.${replace(each.key, "_", "")}
  name     = "${var.project_name}-eks-${each.value.name}"
  role_arn = aws_iam_role.eks_cluster[each.key].arn
  version  = "1.28"
  
  vpc_config {
    subnet_ids = [
      aws_subnet.public["${each.key}-0"].id,
      aws_subnet.public["${each.key}-1"].id,
      aws_subnet.private["${each.key}-0"].id,
      aws_subnet.private["${each.key}-1"].id,
    ]
    
    endpoint_private_access = true
    endpoint_public_access  = true
    public_access_cidrs     = ["0.0.0.0/0"]
  }
  
  enabled_cluster_log_types = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
  
  tags = {
    Name = "${var.project_name}-eks-${each.value.name}"
    Type = each.value.is_primary ? "primary" : "secondary"
  }
  
  depends_on = [
    aws_iam_role_policy_attachment.eks_cluster_policy,
    aws_iam_role_policy_attachment.eks_vpc_resource_controller,
  ]
}

# RDS Global Database
resource "aws_rds_global_cluster" "main" {
  provider = aws.us_east_1
  
  global_cluster_identifier = "${var.project_name}-global-db"
  engine                    = "aurora-postgresql"
  engine_version            = "15.4"
  database_name             = "hanuman"
  master_username           = "hanuman_admin"
  manage_master_user_password = true
  
  deletion_protection = true
  
  tags = {
    Name = "${var.project_name}-global-db"
  }
}

# RDS Clusters par région
resource "aws_rds_cluster" "primary" {
  provider = aws.us_east_1
  
  cluster_identifier        = "${var.project_name}-db-us-east-1"
  global_cluster_identifier = aws_rds_global_cluster.main.id
  engine                    = aws_rds_global_cluster.main.engine
  engine_version            = aws_rds_global_cluster.main.engine_version
  database_name             = aws_rds_global_cluster.main.database_name
  
  db_subnet_group_name   = aws_db_subnet_group.main["us_east_1"].name
  vpc_security_group_ids = [aws_security_group.rds["us_east_1"].id]
  
  backup_retention_period = 7
  preferred_backup_window = "03:00-04:00"
  
  skip_final_snapshot = false
  final_snapshot_identifier = "${var.project_name}-db-final-snapshot-${formatdate("YYYY-MM-DD-hhmm", timestamp())}"
  
  tags = {
    Name = "${var.project_name}-db-primary"
    Type = "primary"
  }
}

resource "aws_rds_cluster" "secondary" {
  for_each = {
    for k, v in var.regions : k => v if !v.is_primary
  }
  
  provider = aws.${replace(each.key, "_", "")}
  
  cluster_identifier         = "${var.project_name}-db-${each.value.name}"
  global_cluster_identifier  = aws_rds_global_cluster.main.id
  engine                     = aws_rds_global_cluster.main.engine
  engine_version             = aws_rds_global_cluster.main.engine_version
  
  db_subnet_group_name   = aws_db_subnet_group.main[each.key].name
  vpc_security_group_ids = [aws_security_group.rds[each.key].id]
  
  depends_on = [aws_rds_cluster.primary]
  
  tags = {
    Name = "${var.project_name}-db-${each.value.name}"
    Type = "secondary"
  }
}

# CloudFront Distribution
resource "aws_cloudfront_distribution" "main" {
  provider = aws.us_east_1
  
  origin {
    domain_name = aws_lb.main["us_east_1"].dns_name
    origin_id   = "primary-alb"
    
    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }
  }
  
  # Origins secondaires pour failover
  dynamic "origin" {
    for_each = {
      for k, v in var.regions : k => v if !v.is_primary
    }
    
    content {
      domain_name = aws_lb.main[origin.key].dns_name
      origin_id   = "${origin.value.name}-alb"
      
      custom_origin_config {
        http_port              = 80
        https_port             = 443
        origin_protocol_policy = "https-only"
        origin_ssl_protocols   = ["TLSv1.2"]
      }
    }
  }
  
  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"
  
  # Cache behaviors
  default_cache_behavior {
    allowed_methods        = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = "primary-alb"
    compress               = true
    viewer_protocol_policy = "redirect-to-https"
    
    forwarded_values {
      query_string = true
      headers      = ["Authorization", "CloudFront-Viewer-Country"]
      
      cookies {
        forward = "all"
      }
    }
    
    min_ttl     = 0
    default_ttl = 3600
    max_ttl     = 86400
  }
  
  # Cache behavior pour les assets statiques
  ordered_cache_behavior {
    path_pattern           = "/static/*"
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = "primary-alb"
    compress               = true
    viewer_protocol_policy = "redirect-to-https"
    
    forwarded_values {
      query_string = false
      
      cookies {
        forward = "none"
      }
    }
    
    min_ttl     = 86400
    default_ttl = 86400
    max_ttl     = 31536000
  }
  
  # Restrictions géographiques
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  
  # Certificat SSL
  viewer_certificate {
    cloudfront_default_certificate = true
  }
  
  # Configuration des erreurs personnalisées
  custom_error_response {
    error_code         = 404
    response_code      = 200
    response_page_path = "/index.html"
  }
  
  tags = {
    Name = "${var.project_name}-cdn"
  }
}

# Outputs
output "vpc_ids" {
  description = "IDs des VPCs par région"
  value = {
    for k, v in aws_vpc.main : k => v.id
  }
}

output "eks_cluster_endpoints" {
  description = "Endpoints des clusters EKS"
  value = {
    for k, v in aws_eks_cluster.main : k => v.endpoint
  }
}

output "rds_cluster_endpoints" {
  description = "Endpoints des clusters RDS"
  value = {
    primary = aws_rds_cluster.primary.endpoint
    secondary = {
      for k, v in aws_rds_cluster.secondary : k => v.endpoint
    }
  }
}

output "cloudfront_domain_name" {
  description = "Nom de domaine CloudFront"
  value       = aws_cloudfront_distribution.main.domain_name
}

output "cloudfront_distribution_id" {
  description = "ID de la distribution CloudFront"
  value       = aws_cloudfront_distribution.main.id
}
