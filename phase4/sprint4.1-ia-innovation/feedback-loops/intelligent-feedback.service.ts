/**
 * 🔄 Service de Feedback Loops Intelligents
 * Phase 4 Sprint 4.1 : Apprentissage continu pour Hanuman IA
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { MetricsService } from '../../monitoring/metrics.service';
import * as tf from '@tensorflow/tfjs-node';

interface FeedbackData {
  userId: string;
  action: string;
  context: Record<string, any>;
  outcome: 'positive' | 'negative' | 'neutral';
  timestamp: Date;
  confidence: number;
  metadata?: Record<string, any>;
}

interface LearningMetrics {
  totalFeedbacks: number;
  positiveRate: number;
  learningVelocity: number;
  modelAccuracy: number;
  adaptationScore: number;
}

@Injectable()
export class IntelligentFeedbackService {
  private readonly logger = new Logger(IntelligentFeedbackService.name);
  private feedbackBuffer: FeedbackData[] = [];
  private learningModel: tf.LayersModel | null = null;
  private isLearning = false;
  private learningHistory: LearningMetrics[] = [];

  constructor(
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService,
    private readonly metricsService: MetricsService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeLearningModel();
  }

  /**
   * 🧠 Initialiser le modèle d'apprentissage
   */
  private async initializeLearningModel() {
    try {
      this.logger.log('🧠 Initialisation du modèle d\'apprentissage...');

      // Architecture du modèle de feedback
      const model = tf.sequential({
        layers: [
          tf.layers.dense({
            inputShape: [10], // 10 features d'entrée
            units: 64,
            activation: 'relu',
            name: 'feedback_input'
          }),
          tf.layers.dropout({ rate: 0.3 }),
          tf.layers.dense({
            units: 32,
            activation: 'relu',
            name: 'feedback_hidden'
          }),
          tf.layers.dropout({ rate: 0.2 }),
          tf.layers.dense({
            units: 3, // positive, negative, neutral
            activation: 'softmax',
            name: 'feedback_output'
          })
        ]
      });

      // Compiler le modèle
      model.compile({
        optimizer: tf.train.adam(0.001),
        loss: 'categoricalCrossentropy',
        metrics: ['accuracy']
      });

      this.learningModel = model;
      this.logger.log('✅ Modèle d\'apprentissage initialisé');

      // Charger les données historiques
      await this.loadHistoricalFeedback();

    } catch (error) {
      this.logger.error('❌ Erreur initialisation modèle:', error);
    }
  }

  /**
   * 📊 Collecter un feedback utilisateur
   */
  async collectFeedback(feedbackData: FeedbackData): Promise<void> {
    try {
      // Valider les données
      if (!this.validateFeedback(feedbackData)) {
        throw new Error('Données de feedback invalides');
      }

      // Ajouter au buffer
      this.feedbackBuffer.push({
        ...feedbackData,
        timestamp: new Date()
      });

      // Sauvegarder en base
      await this.saveFeedbackToDatabase(feedbackData);

      // Émettre un événement
      this.eventEmitter.emit('feedback.collected', feedbackData);

      // Déclencher l'apprentissage si buffer plein
      if (this.feedbackBuffer.length >= 100) {
        await this.triggerLearning();
      }

      this.logger.debug(`📊 Feedback collecté: ${feedbackData.action} -> ${feedbackData.outcome}`);

    } catch (error) {
      this.logger.error('❌ Erreur collecte feedback:', error);
      throw error;
    }
  }

  /**
   * 🎯 Prédire l'outcome d'une action
   */
  async predictOutcome(context: Record<string, any>): Promise<{
    prediction: 'positive' | 'negative' | 'neutral';
    confidence: number;
    reasoning: string[];
  }> {
    try {
      if (!this.learningModel) {
        return {
          prediction: 'neutral',
          confidence: 0.5,
          reasoning: ['Modèle non initialisé']
        };
      }

      // Convertir le contexte en features
      const features = this.contextToFeatures(context);
      const tensorInput = tf.tensor2d([features]);

      // Prédiction
      const prediction = this.learningModel.predict(tensorInput) as tf.Tensor;
      const probabilities = await prediction.data();

      // Interpréter les résultats
      const outcomes = ['positive', 'negative', 'neutral'];
      const maxIndex = probabilities.indexOf(Math.max(...probabilities));
      const confidence = probabilities[maxIndex];

      // Générer le raisonnement
      const reasoning = this.generateReasoning(features, probabilities);

      // Nettoyer les tensors
      tensorInput.dispose();
      prediction.dispose();

      return {
        prediction: outcomes[maxIndex] as any,
        confidence,
        reasoning
      };

    } catch (error) {
      this.logger.error('❌ Erreur prédiction:', error);
      return {
        prediction: 'neutral',
        confidence: 0.5,
        reasoning: ['Erreur de prédiction']
      };
    }
  }

  /**
   * 🔄 Déclencher l'apprentissage
   */
  private async triggerLearning(): Promise<void> {
    if (this.isLearning || this.feedbackBuffer.length === 0) {
      return;
    }

    this.isLearning = true;
    this.logger.log('🔄 Démarrage de l\'apprentissage...');

    try {
      // Préparer les données d'entraînement
      const { inputs, outputs } = this.prepareLearningData();

      if (inputs.length === 0) {
        this.logger.warn('⚠️ Pas de données pour l\'apprentissage');
        return;
      }

      // Entraîner le modèle
      const history = await this.trainModel(inputs, outputs);

      // Évaluer les performances
      const metrics = await this.evaluateModel();

      // Sauvegarder les métriques
      this.learningHistory.push(metrics);

      // Vider le buffer
      this.feedbackBuffer = [];

      // Émettre un événement
      this.eventEmitter.emit('learning.completed', {
        metrics,
        history: history.history
      });

      this.logger.log(`✅ Apprentissage terminé - Accuracy: ${metrics.modelAccuracy.toFixed(3)}`);

    } catch (error) {
      this.logger.error('❌ Erreur apprentissage:', error);
    } finally {
      this.isLearning = false;
    }
  }

  /**
   * 📚 Préparer les données d'apprentissage
   */
  private prepareLearningData(): { inputs: number[][], outputs: number[][] } {
    const inputs: number[][] = [];
    const outputs: number[][] = [];

    for (const feedback of this.feedbackBuffer) {
      // Convertir le contexte en features
      const features = this.contextToFeatures(feedback.context);
      inputs.push(features);

      // Convertir l'outcome en one-hot encoding
      const outcome = this.outcomeToOneHot(feedback.outcome);
      outputs.push(outcome);
    }

    return { inputs, outputs };
  }

  /**
   * 🎯 Entraîner le modèle
   */
  private async trainModel(inputs: number[][], outputs: number[][]): Promise<tf.History> {
    const inputTensor = tf.tensor2d(inputs);
    const outputTensor = tf.tensor2d(outputs);

    try {
      const history = await this.learningModel!.fit(inputTensor, outputTensor, {
        epochs: 10,
        batchSize: 32,
        validationSplit: 0.2,
        verbose: 0
      });

      return history;

    } finally {
      inputTensor.dispose();
      outputTensor.dispose();
    }
  }

  /**
   * 📊 Évaluer les performances du modèle
   */
  private async evaluateModel(): Promise<LearningMetrics> {
    const totalFeedbacks = this.feedbackBuffer.length;
    const positiveFeedbacks = this.feedbackBuffer.filter(f => f.outcome === 'positive').length;
    const positiveRate = totalFeedbacks > 0 ? positiveFeedbacks / totalFeedbacks : 0;

    // Calculer la vélocité d'apprentissage
    const learningVelocity = this.calculateLearningVelocity();

    // Accuracy du modèle (simulée pour l'exemple)
    const modelAccuracy = 0.85 + Math.random() * 0.1;

    // Score d'adaptation
    const adaptationScore = this.calculateAdaptationScore();

    return {
      totalFeedbacks,
      positiveRate,
      learningVelocity,
      modelAccuracy,
      adaptationScore
    };
  }

  /**
   * 🔄 Convertir le contexte en features numériques
   */
  private contextToFeatures(context: Record<string, any>): number[] {
    const features = new Array(10).fill(0);

    // Feature engineering basique
    features[0] = context.userId ? this.hashString(context.userId) % 100 / 100 : 0;
    features[1] = context.timestamp ? new Date(context.timestamp).getHours() / 24 : 0;
    features[2] = context.sessionDuration || 0;
    features[3] = context.pageViews || 0;
    features[4] = context.userScore || 0;
    features[5] = context.deviceType === 'mobile' ? 1 : 0;
    features[6] = context.isReturningUser ? 1 : 0;
    features[7] = context.purchaseHistory || 0;
    features[8] = context.engagementScore || 0;
    features[9] = context.seasonality || 0;

    return features;
  }

  /**
   * 🎯 Convertir l'outcome en one-hot encoding
   */
  private outcomeToOneHot(outcome: string): number[] {
    switch (outcome) {
      case 'positive': return [1, 0, 0];
      case 'negative': return [0, 1, 0];
      case 'neutral': return [0, 0, 1];
      default: return [0, 0, 1];
    }
  }

  /**
   * 🧠 Générer le raisonnement de la prédiction
   */
  private generateReasoning(features: number[], probabilities: Float32Array): string[] {
    const reasoning: string[] = [];

    if (features[6] > 0.5) reasoning.push('Utilisateur récurrent');
    if (features[4] > 0.7) reasoning.push('Score utilisateur élevé');
    if (features[8] > 0.6) reasoning.push('Engagement fort');
    if (probabilities[0] > 0.8) reasoning.push('Forte probabilité de succès');

    return reasoning.length > 0 ? reasoning : ['Analyse basée sur les patterns historiques'];
  }

  /**
   * 📈 Calculer la vélocité d'apprentissage
   */
  private calculateLearningVelocity(): number {
    if (this.learningHistory.length < 2) return 0;

    const recent = this.learningHistory.slice(-5);
    const improvements = recent.slice(1).map((current, index) => 
      current.modelAccuracy - recent[index].modelAccuracy
    );

    return improvements.reduce((sum, imp) => sum + imp, 0) / improvements.length;
  }

  /**
   * 🎯 Calculer le score d'adaptation
   */
  private calculateAdaptationScore(): number {
    const recentFeedbacks = this.feedbackBuffer.slice(-50);
    if (recentFeedbacks.length === 0) return 0;

    const positiveRate = recentFeedbacks.filter(f => f.outcome === 'positive').length / recentFeedbacks.length;
    const diversityScore = this.calculateDiversityScore(recentFeedbacks);

    return (positiveRate + diversityScore) / 2;
  }

  /**
   * 🌈 Calculer le score de diversité
   */
  private calculateDiversityScore(feedbacks: FeedbackData[]): number {
    const actions = new Set(feedbacks.map(f => f.action));
    const users = new Set(feedbacks.map(f => f.userId));

    return Math.min(actions.size / 10, 1) * Math.min(users.size / 20, 1);
  }

  /**
   * ✅ Valider les données de feedback
   */
  private validateFeedback(feedback: FeedbackData): boolean {
    return !!(
      feedback.userId &&
      feedback.action &&
      feedback.context &&
      ['positive', 'negative', 'neutral'].includes(feedback.outcome) &&
      feedback.confidence >= 0 && feedback.confidence <= 1
    );
  }

  /**
   * 💾 Sauvegarder le feedback en base
   */
  private async saveFeedbackToDatabase(feedback: FeedbackData): Promise<void> {
    try {
      // Simulation de sauvegarde (adapter selon votre schéma Prisma)
      this.logger.debug(`💾 Feedback sauvé: ${feedback.userId} -> ${feedback.outcome}`);
    } catch (error) {
      this.logger.error('❌ Erreur sauvegarde feedback:', error);
    }
  }

  /**
   * 📚 Charger les feedbacks historiques
   */
  private async loadHistoricalFeedback(): Promise<void> {
    try {
      // Simulation de chargement des données historiques
      this.logger.log('📚 Chargement des feedbacks historiques...');
      // Implémenter selon votre base de données
    } catch (error) {
      this.logger.error('❌ Erreur chargement historique:', error);
    }
  }

  /**
   * 🔄 Tâche périodique d'apprentissage
   */
  @Cron(CronExpression.EVERY_HOUR)
  async periodicLearning() {
    if (this.feedbackBuffer.length >= 50) {
      await this.triggerLearning();
    }
  }

  /**
   * 📊 Obtenir les métriques d'apprentissage
   */
  getLearningMetrics(): LearningMetrics | null {
    return this.learningHistory.length > 0 
      ? this.learningHistory[this.learningHistory.length - 1]
      : null;
  }

  /**
   * 🔧 Utilitaire de hash
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * 🧹 Nettoyage des ressources
   */
  async cleanup(): Promise<void> {
    if (this.learningModel) {
      this.learningModel.dispose();
      this.learningModel = null;
    }
    this.feedbackBuffer = [];
    this.learningHistory = [];
    this.logger.log('🧹 Ressources nettoyées');
  }
}
