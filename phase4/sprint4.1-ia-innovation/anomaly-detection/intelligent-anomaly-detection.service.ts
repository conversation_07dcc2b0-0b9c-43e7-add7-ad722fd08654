/**
 * 🔍 Service de Détection d'Anomalies Intelligente
 * Phase 4 Sprint 4.1 : Prévention proactive des incidents avec IA
 */

import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';
import { MetricsService } from '../../monitoring/metrics.service';
import * as tf from '@tensorflow/tfjs-node';

interface SystemMetrics {
  timestamp: Date;
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  responseTime: number;
  errorRate: number;
  throughput: number;
  activeUsers: number;
}

interface AnomalyAlert {
  id: string;
  type: 'performance' | 'security' | 'business' | 'infrastructure';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  metrics: SystemMetrics;
  confidence: number;
  predictedImpact: string;
  suggestedActions: string[];
  timestamp: Date;
  resolved?: boolean;
  resolvedAt?: Date;
}

interface AnomalyPattern {
  name: string;
  features: number[];
  threshold: number;
  description: string;
  actions: string[];
}

@Injectable()
export class IntelligentAnomalyDetectionService {
  private readonly logger = new Logger(IntelligentAnomalyDetectionService.name);
  private anomalyModel: tf.LayersModel | null = null;
  private timeSeriesModel: tf.LayersModel | null = null;
  private metricsHistory: SystemMetrics[] = [];
  private activeAlerts: Map<string, AnomalyAlert> = new Map();
  private patterns: AnomalyPattern[] = [];
  private isTraining = false;

  constructor(
    private readonly configService: ConfigService,
    private readonly metricsService: MetricsService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeModels();
    this.loadAnomalyPatterns();
  }

  /**
   * 🧠 Initialiser les modèles de détection
   */
  private async initializeModels() {
    try {
      this.logger.log('🧠 Initialisation des modèles de détection d\'anomalies...');

      // Modèle de détection d'anomalies (Autoencoder)
      await this.initializeAnomalyModel();
      
      // Modèle de prédiction temporelle (LSTM)
      await this.initializeTimeSeriesModel();

      this.logger.log('✅ Modèles de détection initialisés');

    } catch (error) {
      this.logger.error('❌ Erreur initialisation modèles:', error);
    }
  }

  /**
   * 🔍 Initialiser le modèle autoencoder pour anomalies
   */
  private async initializeAnomalyModel() {
    const inputDim = 8; // Nombre de métriques système

    // Encoder
    const input = tf.input({ shape: [inputDim] });
    const encoded1 = tf.layers.dense({ units: 16, activation: 'relu' }).apply(input);
    const encoded2 = tf.layers.dense({ units: 8, activation: 'relu' }).apply(encoded1);
    const encoded3 = tf.layers.dense({ units: 4, activation: 'relu' }).apply(encoded2);

    // Decoder
    const decoded1 = tf.layers.dense({ units: 8, activation: 'relu' }).apply(encoded3);
    const decoded2 = tf.layers.dense({ units: 16, activation: 'relu' }).apply(decoded1);
    const output = tf.layers.dense({ units: inputDim, activation: 'sigmoid' }).apply(decoded2);

    this.anomalyModel = tf.model({ inputs: input, outputs: output });

    this.anomalyModel.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });
  }

  /**
   * ⏰ Initialiser le modèle LSTM pour prédiction temporelle
   */
  private async initializeTimeSeriesModel() {
    const sequenceLength = 24; // 24 heures de données
    const features = 8;

    const model = tf.sequential({
      layers: [
        tf.layers.lstm({
          units: 50,
          returnSequences: true,
          inputShape: [sequenceLength, features],
          name: 'lstm1'
        }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.lstm({
          units: 50,
          returnSequences: false,
          name: 'lstm2'
        }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({
          units: 25,
          activation: 'relu',
          name: 'dense1'
        }),
        tf.layers.dense({
          units: features,
          activation: 'sigmoid',
          name: 'output'
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });

    this.timeSeriesModel = model;
  }

  /**
   * 📊 Collecter les métriques système
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async collectSystemMetrics() {
    try {
      const metrics = await this.getCurrentSystemMetrics();
      
      // Ajouter à l'historique
      this.metricsHistory.push(metrics);
      
      // Garder seulement les 7 derniers jours
      if (this.metricsHistory.length > 7 * 24 * 60) {
        this.metricsHistory.shift();
      }

      // Détecter les anomalies
      await this.detectAnomalies(metrics);

      this.logger.debug('📊 Métriques collectées et analysées');

    } catch (error) {
      this.logger.error('❌ Erreur collecte métriques:', error);
    }
  }

  /**
   * 🔍 Détecter les anomalies en temps réel
   */
  private async detectAnomalies(currentMetrics: SystemMetrics) {
    try {
      if (!this.anomalyModel || this.metricsHistory.length < 100) {
        return; // Pas assez de données historiques
      }

      // Normaliser les métriques
      const normalizedMetrics = this.normalizeMetrics(currentMetrics);
      
      // Prédiction avec l'autoencoder
      const input = tf.tensor2d([normalizedMetrics]);
      const reconstruction = this.anomalyModel.predict(input) as tf.Tensor;
      const reconstructionData = await reconstruction.data();

      // Calculer l'erreur de reconstruction
      const reconstructionError = this.calculateReconstructionError(
        normalizedMetrics,
        Array.from(reconstructionData)
      );

      // Nettoyer les tensors
      input.dispose();
      reconstruction.dispose();

      // Seuil d'anomalie adaptatif
      const threshold = this.calculateAdaptiveThreshold();

      if (reconstructionError > threshold) {
        await this.handleAnomalyDetected(currentMetrics, reconstructionError, threshold);
      }

      // Prédiction future
      await this.predictFutureAnomalies(currentMetrics);

    } catch (error) {
      this.logger.error('❌ Erreur détection anomalies:', error);
    }
  }

  /**
   * 🚨 Gérer une anomalie détectée
   */
  private async handleAnomalyDetected(
    metrics: SystemMetrics,
    error: number,
    threshold: number
  ) {
    const anomalyId = `anomaly_${Date.now()}`;
    const confidence = Math.min((error / threshold) * 100, 100);

    // Analyser le type d'anomalie
    const anomalyType = this.classifyAnomaly(metrics);
    const severity = this.calculateSeverity(error, threshold, anomalyType);

    // Créer l'alerte
    const alert: AnomalyAlert = {
      id: anomalyId,
      type: anomalyType,
      severity,
      title: this.generateAlertTitle(anomalyType, metrics),
      description: this.generateAlertDescription(anomalyType, metrics, error),
      metrics,
      confidence,
      predictedImpact: this.predictImpact(anomalyType, metrics),
      suggestedActions: this.getSuggestedActions(anomalyType, metrics),
      timestamp: new Date()
    };

    // Sauvegarder l'alerte
    this.activeAlerts.set(anomalyId, alert);

    // Émettre l'événement
    this.eventEmitter.emit('anomaly.detected', alert);

    // Auto-healing si possible
    if (severity !== 'critical') {
      await this.attemptAutoHealing(alert);
    }

    this.logger.warn(`🚨 Anomalie détectée: ${alert.title} (${confidence.toFixed(1)}% confiance)`);
  }

  /**
   * 🔮 Prédire les anomalies futures
   */
  private async predictFutureAnomalies(currentMetrics: SystemMetrics) {
    try {
      if (!this.timeSeriesModel || this.metricsHistory.length < 24) {
        return;
      }

      // Préparer la séquence temporelle
      const sequence = this.prepareTimeSequence();
      const input = tf.tensor3d([sequence]);

      // Prédiction
      const prediction = this.timeSeriesModel.predict(input) as tf.Tensor;
      const predictionData = await prediction.data();

      // Analyser la prédiction
      const futureMetrics = this.denormalizeMetrics(Array.from(predictionData));
      const riskScore = this.calculateRiskScore(futureMetrics);

      if (riskScore > 0.7) {
        await this.createPredictiveAlert(futureMetrics, riskScore);
      }

      // Nettoyer
      input.dispose();
      prediction.dispose();

    } catch (error) {
      this.logger.error('❌ Erreur prédiction future:', error);
    }
  }

  /**
   * 🔧 Tentative d'auto-healing
   */
  private async attemptAutoHealing(alert: AnomalyAlert): Promise<boolean> {
    try {
      this.logger.log(`🔧 Tentative d'auto-healing pour: ${alert.title}`);

      switch (alert.type) {
        case 'performance':
          return await this.healPerformanceIssue(alert);
        case 'infrastructure':
          return await this.healInfrastructureIssue(alert);
        case 'business':
          return await this.healBusinessIssue(alert);
        default:
          return false;
      }

    } catch (error) {
      this.logger.error('❌ Erreur auto-healing:', error);
      return false;
    }
  }

  /**
   * ⚡ Guérir les problèmes de performance
   */
  private async healPerformanceIssue(alert: AnomalyAlert): Promise<boolean> {
    const { metrics } = alert;

    // CPU élevé
    if (metrics.cpu > 80) {
      await this.scaleUpResources('cpu');
      return true;
    }

    // Mémoire élevée
    if (metrics.memory > 85) {
      await this.clearCaches();
      await this.scaleUpResources('memory');
      return true;
    }

    // Temps de réponse élevé
    if (metrics.responseTime > 1000) {
      await this.optimizeQueries();
      await this.enableCaching();
      return true;
    }

    return false;
  }

  /**
   * 🏗️ Guérir les problèmes d'infrastructure
   */
  private async healInfrastructureIssue(alert: AnomalyAlert): Promise<boolean> {
    const { metrics } = alert;

    // Disque plein
    if (metrics.disk > 90) {
      await this.cleanupLogs();
      await this.archiveOldData();
      return true;
    }

    // Réseau saturé
    if (metrics.network > 80) {
      await this.enableCompression();
      await this.optimizeNetworkRoutes();
      return true;
    }

    return false;
  }

  /**
   * 💼 Guérir les problèmes business
   */
  private async healBusinessIssue(alert: AnomalyAlert): Promise<boolean> {
    const { metrics } = alert;

    // Taux d'erreur élevé
    if (metrics.errorRate > 5) {
      await this.restartFailingServices();
      await this.switchToBackupServices();
      return true;
    }

    return false;
  }

  /**
   * 📈 Calculer le seuil adaptatif
   */
  private calculateAdaptiveThreshold(): number {
    if (this.metricsHistory.length < 100) {
      return 0.1; // Seuil par défaut
    }

    // Calculer la moyenne et l'écart-type des erreurs récentes
    const recentErrors = this.metricsHistory
      .slice(-100)
      .map(m => this.calculateBaselineError(m));

    const mean = recentErrors.reduce((sum, err) => sum + err, 0) / recentErrors.length;
    const variance = recentErrors.reduce((sum, err) => sum + Math.pow(err - mean, 2), 0) / recentErrors.length;
    const stdDev = Math.sqrt(variance);

    // Seuil = moyenne + 2 * écart-type
    return mean + (2 * stdDev);
  }

  /**
   * 🔢 Normaliser les métriques
   */
  private normalizeMetrics(metrics: SystemMetrics): number[] {
    return [
      metrics.cpu / 100,
      metrics.memory / 100,
      metrics.disk / 100,
      metrics.network / 100,
      Math.min(metrics.responseTime / 1000, 1),
      Math.min(metrics.errorRate / 10, 1),
      Math.min(metrics.throughput / 1000, 1),
      Math.min(metrics.activeUsers / 1000, 1)
    ];
  }

  /**
   * 📊 Obtenir les métriques système actuelles
   */
  private async getCurrentSystemMetrics(): Promise<SystemMetrics> {
    // Simulation - adapter selon votre infrastructure
    return {
      timestamp: new Date(),
      cpu: 30 + Math.random() * 40,
      memory: 50 + Math.random() * 30,
      disk: 60 + Math.random() * 20,
      network: 20 + Math.random() * 30,
      responseTime: 100 + Math.random() * 200,
      errorRate: Math.random() * 2,
      throughput: 500 + Math.random() * 300,
      activeUsers: 100 + Math.random() * 200
    };
  }

  /**
   * 📋 Charger les patterns d'anomalies
   */
  private loadAnomalyPatterns() {
    this.patterns = [
      {
        name: 'CPU Spike',
        features: [0.8, 0, 0, 0, 0, 0, 0, 0],
        threshold: 0.15,
        description: 'Pic de CPU détecté',
        actions: ['Scale up CPU', 'Optimize processes']
      },
      {
        name: 'Memory Leak',
        features: [0, 0.9, 0, 0, 0, 0, 0, 0],
        threshold: 0.12,
        description: 'Fuite mémoire possible',
        actions: ['Restart services', 'Clear caches']
      },
      {
        name: 'Network Congestion',
        features: [0, 0, 0, 0.8, 0.5, 0, 0, 0],
        threshold: 0.18,
        description: 'Congestion réseau',
        actions: ['Enable compression', 'Optimize routes']
      }
    ];
  }

  /**
   * 🎯 Classifier le type d'anomalie
   */
  private classifyAnomaly(metrics: SystemMetrics): AnomalyAlert['type'] {
    if (metrics.cpu > 80 || metrics.memory > 85 || metrics.responseTime > 1000) {
      return 'performance';
    }
    if (metrics.disk > 90 || metrics.network > 80) {
      return 'infrastructure';
    }
    if (metrics.errorRate > 5) {
      return 'business';
    }
    return 'performance';
  }

  /**
   * 📊 Obtenir les statistiques d'anomalies
   */
  getAnomalyStatistics(): {
    totalAlerts: number;
    activeAlerts: number;
    resolvedAlerts: number;
    averageResolutionTime: number;
    alertsByType: Record<string, number>;
    alertsBySeverity: Record<string, number>;
  } {
    const alerts = Array.from(this.activeAlerts.values());
    const resolved = alerts.filter(a => a.resolved);

    const alertsByType = alerts.reduce((acc, alert) => {
      acc[alert.type] = (acc[alert.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const alertsBySeverity = alerts.reduce((acc, alert) => {
      acc[alert.severity] = (acc[alert.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const avgResolutionTime = resolved.length > 0
      ? resolved.reduce((sum, alert) => {
          const duration = alert.resolvedAt!.getTime() - alert.timestamp.getTime();
          return sum + duration;
        }, 0) / resolved.length
      : 0;

    return {
      totalAlerts: alerts.length,
      activeAlerts: alerts.filter(a => !a.resolved).length,
      resolvedAlerts: resolved.length,
      averageResolutionTime: avgResolutionTime,
      alertsByType,
      alertsBySeverity
    };
  }

  // Méthodes utilitaires (implémentation simplifiée)
  private calculateReconstructionError(original: number[], reconstructed: number[]): number {
    return original.reduce((sum, val, i) => sum + Math.pow(val - reconstructed[i], 2), 0) / original.length;
  }

  private calculateBaselineError(metrics: SystemMetrics): number { return 0.05; }
  private calculateSeverity(error: number, threshold: number, type: string): AnomalyAlert['severity'] { return 'medium'; }
  private generateAlertTitle(type: string, metrics: SystemMetrics): string { return `Anomalie ${type} détectée`; }
  private generateAlertDescription(type: string, metrics: SystemMetrics, error: number): string { return `Anomalie de type ${type}`; }
  private predictImpact(type: string, metrics: SystemMetrics): string { return 'Impact modéré prévu'; }
  private getSuggestedActions(type: string, metrics: SystemMetrics): string[] { return ['Surveiller', 'Analyser']; }
  private prepareTimeSequence(): number[][] { return []; }
  private denormalizeMetrics(data: number[]): SystemMetrics { return {} as SystemMetrics; }
  private calculateRiskScore(metrics: SystemMetrics): number { return 0.5; }
  private async createPredictiveAlert(metrics: SystemMetrics, risk: number): Promise<void> {}
  private async scaleUpResources(type: string): Promise<void> {}
  private async clearCaches(): Promise<void> {}
  private async optimizeQueries(): Promise<void> {}
  private async enableCaching(): Promise<void> {}
  private async cleanupLogs(): Promise<void> {}
  private async archiveOldData(): Promise<void> {}
  private async enableCompression(): Promise<void> {}
  private async optimizeNetworkRoutes(): Promise<void> {}
  private async restartFailingServices(): Promise<void> {}
  private async switchToBackupServices(): Promise<void> {}

  /**
   * 🧹 Nettoyage des ressources
   */
  async cleanup(): Promise<void> {
    if (this.anomalyModel) this.anomalyModel.dispose();
    if (this.timeSeriesModel) this.timeSeriesModel.dispose();
    this.activeAlerts.clear();
    this.metricsHistory = [];
    this.logger.log('🧹 Service de détection d\'anomalies nettoyé');
  }
}
