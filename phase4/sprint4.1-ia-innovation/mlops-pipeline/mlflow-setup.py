#!/usr/bin/env python3
"""
🤖 MLOps Pipeline - MLflow Setup
Phase 4 Sprint 4.1 : Configuration MLflow pour pipeline ML avancé
"""

import os
import mlflow
import mlflow.tracking
from mlflow.tracking import MlflowClient
from mlflow.models.signature import infer_signature
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import joblib
import logging
from datetime import datetime
import yaml

# Configuration logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MLflowPipeline:
    """Pipeline MLOps avec MLflow pour Hanuman IA"""
    
    def __init__(self, tracking_uri="http://localhost:5000", experiment_name="hanuman-ai-models"):
        """
        Initialiser le pipeline MLflow
        
        Args:
            tracking_uri: URI du serveur MLflow
            experiment_name: Nom de l'expérience MLflow
        """
        self.tracking_uri = tracking_uri
        self.experiment_name = experiment_name
        self.client = None
        self.experiment_id = None
        
        self.setup_mlflow()
    
    def setup_mlflow(self):
        """Configuration initiale de MLflow"""
        try:
            # Configurer MLflow
            mlflow.set_tracking_uri(self.tracking_uri)
            
            # Créer ou récupérer l'expérience
            try:
                experiment = mlflow.get_experiment_by_name(self.experiment_name)
                if experiment is None:
                    self.experiment_id = mlflow.create_experiment(self.experiment_name)
                    logger.info(f"✅ Expérience créée: {self.experiment_name}")
                else:
                    self.experiment_id = experiment.experiment_id
                    logger.info(f"✅ Expérience existante: {self.experiment_name}")
            except Exception as e:
                logger.error(f"❌ Erreur création expérience: {e}")
                # Fallback vers expérience par défaut
                self.experiment_id = "0"
            
            # Initialiser le client
            self.client = MlflowClient(tracking_uri=self.tracking_uri)
            
            logger.info(f"🚀 MLflow configuré - URI: {self.tracking_uri}")
            
        except Exception as e:
            logger.error(f"❌ Erreur configuration MLflow: {e}")
            # Mode dégradé sans MLflow
            self.client = None
    
    def train_recommendation_model(self, data_path=None):
        """
        Entraîner un modèle de recommandations avancé
        
        Args:
            data_path: Chemin vers les données d'entraînement
        """
        with mlflow.start_run(experiment_id=self.experiment_id, run_name="recommendation_model_v2"):
            try:
                # Générer des données synthétiques si pas de données réelles
                if data_path is None:
                    logger.info("📊 Génération de données synthétiques...")
                    X, y = self.generate_synthetic_recommendation_data()
                else:
                    logger.info(f"📊 Chargement des données: {data_path}")
                    data = pd.read_csv(data_path)
                    X = data.drop('target', axis=1)
                    y = data['target']
                
                # Split des données
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.2, random_state=42
                )
                
                # Paramètres du modèle
                params = {
                    'n_estimators': 100,
                    'max_depth': 10,
                    'min_samples_split': 5,
                    'min_samples_leaf': 2,
                    'random_state': 42
                }
                
                # Log des paramètres
                mlflow.log_params(params)
                
                # Entraînement
                logger.info("🤖 Entraînement du modèle...")
                model = RandomForestClassifier(**params)
                model.fit(X_train, y_train)
                
                # Prédictions et métriques
                y_pred = model.predict(X_test)
                
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, average='weighted')
                recall = recall_score(y_test, y_pred, average='weighted')
                f1 = f1_score(y_test, y_pred, average='weighted')
                
                # Log des métriques
                metrics = {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'training_samples': len(X_train),
                    'test_samples': len(X_test)
                }
                
                mlflow.log_metrics(metrics)
                
                # Signature du modèle
                signature = infer_signature(X_train, y_pred)
                
                # Enregistrement du modèle
                model_info = mlflow.sklearn.log_model(
                    sk_model=model,
                    artifact_path="recommendation_model",
                    signature=signature,
                    registered_model_name="hanuman_recommendation_model"
                )
                
                # Sauvegarder le modèle localement aussi
                model_path = f"models/recommendation_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.joblib"
                os.makedirs("models", exist_ok=True)
                joblib.dump(model, model_path)
                
                logger.info(f"✅ Modèle entraîné avec succès!")
                logger.info(f"📊 Accuracy: {accuracy:.4f}")
                logger.info(f"📊 Precision: {precision:.4f}")
                logger.info(f"📊 Recall: {recall:.4f}")
                logger.info(f"📊 F1-Score: {f1:.4f}")
                logger.info(f"💾 Modèle sauvé: {model_path}")
                
                return model_info, metrics
                
            except Exception as e:
                logger.error(f"❌ Erreur entraînement modèle: {e}")
                mlflow.log_param("error", str(e))
                raise
    
    def train_anomaly_detection_model(self):
        """Entraîner un modèle de détection d'anomalies"""
        with mlflow.start_run(experiment_id=self.experiment_id, run_name="anomaly_detection_v2"):
            try:
                from sklearn.ensemble import IsolationForest
                from sklearn.preprocessing import StandardScaler
                
                # Générer des données d'anomalies
                logger.info("📊 Génération de données d'anomalies...")
                X_normal, X_anomaly = self.generate_anomaly_data()
                
                # Préparation des données
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X_normal)
                
                # Paramètres
                params = {
                    'contamination': 0.1,
                    'random_state': 42,
                    'n_estimators': 100
                }
                
                mlflow.log_params(params)
                
                # Entraînement
                logger.info("🤖 Entraînement détection d'anomalies...")
                model = IsolationForest(**params)
                model.fit(X_scaled)
                
                # Test sur données normales et anomalies
                normal_scores = model.decision_function(X_scaled)
                anomaly_scores = model.decision_function(scaler.transform(X_anomaly))
                
                # Métriques
                normal_predictions = model.predict(X_scaled)
                anomaly_predictions = model.predict(scaler.transform(X_anomaly))
                
                normal_accuracy = (normal_predictions == 1).mean()
                anomaly_detection_rate = (anomaly_predictions == -1).mean()
                
                metrics = {
                    'normal_accuracy': normal_accuracy,
                    'anomaly_detection_rate': anomaly_detection_rate,
                    'avg_normal_score': normal_scores.mean(),
                    'avg_anomaly_score': anomaly_scores.mean()
                }
                
                mlflow.log_metrics(metrics)
                
                # Enregistrement du modèle et scaler
                mlflow.sklearn.log_model(
                    sk_model=model,
                    artifact_path="anomaly_model",
                    registered_model_name="hanuman_anomaly_detection"
                )
                
                mlflow.sklearn.log_model(
                    sk_model=scaler,
                    artifact_path="anomaly_scaler",
                    registered_model_name="hanuman_anomaly_scaler"
                )
                
                logger.info(f"✅ Modèle d'anomalies entraîné!")
                logger.info(f"📊 Précision normale: {normal_accuracy:.4f}")
                logger.info(f"📊 Détection anomalies: {anomaly_detection_rate:.4f}")
                
                return metrics
                
            except Exception as e:
                logger.error(f"❌ Erreur modèle anomalies: {e}")
                raise
    
    def generate_synthetic_recommendation_data(self, n_samples=10000):
        """Générer des données synthétiques pour les recommandations"""
        np.random.seed(42)
        
        # Features utilisateur
        user_age = np.random.normal(35, 10, n_samples)
        user_experience = np.random.exponential(2, n_samples)
        user_budget = np.random.lognormal(7, 0.5, n_samples)
        
        # Features retraite
        retreat_duration = np.random.choice([3, 7, 14, 21], n_samples)
        retreat_price = np.random.normal(500, 200, n_samples)
        retreat_rating = np.random.beta(8, 2, n_samples) * 5
        
        # Features contextuelles
        season = np.random.choice([0, 1, 2, 3], n_samples)  # 0=hiver, 1=printemps, etc.
        day_of_week = np.random.choice(range(7), n_samples)
        
        # Créer la matrice de features
        X = np.column_stack([
            user_age, user_experience, user_budget,
            retreat_duration, retreat_price, retreat_rating,
            season, day_of_week
        ])
        
        # Target basé sur une logique business
        y = (
            (user_budget > retreat_price) &
            (retreat_rating > 4.0) &
            (user_experience > 1)
        ).astype(int)
        
        return X, y
    
    def generate_anomaly_data(self, n_normal=5000, n_anomaly=500):
        """Générer des données pour la détection d'anomalies"""
        np.random.seed(42)
        
        # Données normales (métriques système)
        cpu_usage = np.random.normal(30, 10, n_normal)
        memory_usage = np.random.normal(50, 15, n_normal)
        response_time = np.random.exponential(100, n_normal)
        error_rate = np.random.beta(1, 99, n_normal)
        
        X_normal = np.column_stack([cpu_usage, memory_usage, response_time, error_rate])
        
        # Données d'anomalies
        cpu_anomaly = np.random.normal(80, 5, n_anomaly)  # CPU élevé
        memory_anomaly = np.random.normal(90, 5, n_anomaly)  # Mémoire élevée
        response_anomaly = np.random.exponential(1000, n_anomaly)  # Temps réponse élevé
        error_anomaly = np.random.beta(10, 90, n_anomaly)  # Taux erreur élevé
        
        X_anomaly = np.column_stack([cpu_anomaly, memory_anomaly, response_anomaly, error_anomaly])
        
        return X_normal, X_anomaly
    
    def deploy_model_to_production(self, model_name, version="latest"):
        """Déployer un modèle en production"""
        try:
            if self.client is None:
                logger.warning("⚠️ MLflow non disponible - déploiement simulé")
                return True
            
            # Transition vers production
            self.client.transition_model_version_stage(
                name=model_name,
                version=version,
                stage="Production"
            )
            
            logger.info(f"🚀 Modèle {model_name} v{version} déployé en production")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur déploiement: {e}")
            return False
    
    def get_model_metrics(self, model_name):
        """Récupérer les métriques d'un modèle"""
        try:
            if self.client is None:
                return {"status": "mlflow_unavailable"}
            
            # Récupérer la dernière version
            latest_version = self.client.get_latest_versions(
                model_name, stages=["Production", "Staging"]
            )
            
            if not latest_version:
                return {"status": "no_model_found"}
            
            version = latest_version[0]
            run = self.client.get_run(version.run_id)
            
            return {
                "model_name": model_name,
                "version": version.version,
                "stage": version.current_stage,
                "metrics": run.data.metrics,
                "params": run.data.params,
                "timestamp": run.info.start_time
            }
            
        except Exception as e:
            logger.error(f"❌ Erreur récupération métriques: {e}")
            return {"status": "error", "message": str(e)}

def main():
    """Fonction principale pour tester le pipeline"""
    logger.info("🚀 Démarrage du pipeline MLOps Hanuman...")
    
    # Initialiser le pipeline
    pipeline = MLflowPipeline()
    
    # Entraîner les modèles
    logger.info("📊 Entraînement modèle de recommandations...")
    rec_info, rec_metrics = pipeline.train_recommendation_model()
    
    logger.info("🔍 Entraînement modèle de détection d'anomalies...")
    anomaly_metrics = pipeline.train_anomaly_detection_model()
    
    # Résumé
    logger.info("✅ Pipeline MLOps terminé avec succès!")
    logger.info(f"📊 Recommandations - Accuracy: {rec_metrics['accuracy']:.4f}")
    logger.info(f"🔍 Anomalies - Détection: {anomaly_metrics['anomaly_detection_rate']:.4f}")

if __name__ == "__main__":
    main()
