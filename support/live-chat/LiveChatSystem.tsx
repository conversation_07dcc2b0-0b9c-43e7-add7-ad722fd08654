/**
 * Live Chat Support System - Sprint 19
 * 24/7 customer support with AI assistance
 * Date: 23 Juillet 2025
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useWebSocket } from '../hooks/useWebSocket';
import { useAuth } from '../hooks/useAuth';
import { SupportAPI } from '../services/supportAPI';
import { AIAssistant } from './AIAssistant';

// Types
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'agent' | 'bot';
  timestamp: Date;
  type: 'text' | 'image' | 'file' | 'quick_reply';
  metadata?: {
    agentName?: string;
    agentAvatar?: string;
    quickReplies?: string[];
    attachments?: Array<{ name: string; url: string; type: string }>;
  };
}

interface ChatSession {
  id: string;
  status: 'waiting' | 'active' | 'resolved' | 'closed';
  agent?: {
    id: string;
    name: string;
    avatar: string;
    isOnline: boolean;
  };
  queue: {
    position: number;
    estimatedWait: number;
  };
  satisfaction?: {
    rating: number;
    feedback: string;
  };
}

interface QuickReply {
  id: string;
  text: string;
  category: string;
}

// Quick Replies Data
const QUICK_REPLIES: QuickReply[] = [
  { id: '1', text: 'Comment créer une retraite ?', category: 'getting_started' },
  { id: '2', text: 'Problème de paiement', category: 'billing' },
  { id: '3', text: 'Modifier mon profil', category: 'account' },
  { id: '4', text: 'Annuler ma réservation', category: 'booking' },
  { id: '5', text: 'Contacter un humain', category: 'escalation' }
];

// Chat Widget Component
const ChatWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  return (
    <>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className={`chat-widget ${isMinimized ? 'minimized' : ''}`}
          >
            <LiveChat
              onMinimize={() => setIsMinimized(true)}
              onClose={() => setIsOpen(false)}
              onUnreadChange={setUnreadCount}
            />
          </motion.div>
        )}
      </AnimatePresence>

      <motion.button
        className="chat-trigger"
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        animate={{ 
          backgroundColor: unreadCount > 0 ? '#ef4444' : '#6366f1'
        }}
      >
        {isOpen ? (
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        ) : (
          <>
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
            </svg>
            {unreadCount > 0 && (
              <span className="unread-badge">{unreadCount}</span>
            )}
          </>
        )}
      </motion.button>
    </>
  );
};

// Main Live Chat Component
const LiveChat: React.FC<{
  onMinimize: () => void;
  onClose: () => void;
  onUnreadChange: (count: number) => void;
}> = ({ onMinimize, onClose, onUnreadChange }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [session, setSession] = useState<ChatSession | null>(null);
  const [showQuickReplies, setShowQuickReplies] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { user } = useAuth();
  const { socket, isConnected } = useWebSocket('/support/chat');

  useEffect(() => {
    initializeChat();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (socket) {
      socket.on('message', handleIncomingMessage);
      socket.on('agent_typing', handleAgentTyping);
      socket.on('session_update', handleSessionUpdate);
      
      return () => {
        socket.off('message');
        socket.off('agent_typing');
        socket.off('session_update');
      };
    }
  }, [socket]);

  const initializeChat = async () => {
    try {
      // Initialize chat session
      const sessionData = await SupportAPI.initializeSession(user?.id);
      setSession(sessionData);

      // Add welcome message
      const welcomeMessage: Message = {
        id: 'welcome',
        content: `Bonjour ${user?.name || 'cher utilisateur'} ! 👋\n\nJe suis votre assistant virtuel. Comment puis-je vous aider aujourd'hui ?`,
        sender: 'bot',
        timestamp: new Date(),
        type: 'text',
        metadata: {
          quickReplies: QUICK_REPLIES.slice(0, 3).map(qr => qr.text)
        }
      };
      
      setMessages([welcomeMessage]);
    } catch (error) {
      console.error('Failed to initialize chat:', error);
    }
  };

  const handleIncomingMessage = (message: Message) => {
    setMessages(prev => [...prev, message]);
    setIsTyping(false);
    
    // Update unread count if chat is minimized
    onUnreadChange(1);
  };

  const handleAgentTyping = (isTyping: boolean) => {
    setIsTyping(isTyping);
  };

  const handleSessionUpdate = (sessionData: Partial<ChatSession>) => {
    setSession(prev => prev ? { ...prev, ...sessionData } : null);
  };

  const sendMessage = async (content: string, type: 'text' | 'quick_reply' = 'text') => {
    if (!content.trim()) return;

    const message: Message = {
      id: Date.now().toString(),
      content,
      sender: 'user',
      timestamp: new Date(),
      type
    };

    setMessages(prev => [...prev, message]);
    setInputValue('');
    setShowQuickReplies(false);

    // Send to server
    if (socket && session) {
      socket.emit('send_message', {
        sessionId: session.id,
        message
      });
    }

    // Handle quick reply actions
    if (type === 'quick_reply') {
      handleQuickReplyAction(content);
    }
  };

  const handleQuickReplyAction = async (replyText: string) => {
    const quickReply = QUICK_REPLIES.find(qr => qr.text === replyText);
    
    if (quickReply?.category === 'escalation') {
      // Escalate to human agent
      await SupportAPI.escalateToHuman(session?.id);
      
      const escalationMessage: Message = {
        id: Date.now().toString(),
        content: 'Je vous mets en relation avec un agent humain. Veuillez patienter...',
        sender: 'bot',
        timestamp: new Date(),
        type: 'text'
      };
      
      setMessages(prev => [...prev, escalationMessage]);
    } else {
      // Handle with AI assistant
      const aiResponse = await AIAssistant.processQuery(replyText, quickReply?.category);
      
      const botMessage: Message = {
        id: Date.now().toString(),
        content: aiResponse.content,
        sender: 'bot',
        timestamp: new Date(),
        type: 'text',
        metadata: {
          quickReplies: aiResponse.suggestedActions
        }
      };
      
      setMessages(prev => [...prev, botMessage]);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    
    try {
      const uploadedFile = await SupportAPI.uploadFile(file, session?.id);
      
      const fileMessage: Message = {
        id: Date.now().toString(),
        content: `Fichier envoyé: ${file.name}`,
        sender: 'user',
        timestamp: new Date(),
        type: 'file',
        metadata: {
          attachments: [{
            name: file.name,
            url: uploadedFile.url,
            type: file.type
          }]
        }
      };
      
      setMessages(prev => [...prev, fileMessage]);
    } catch (error) {
      console.error('File upload failed:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const renderMessage = (message: Message) => {
    const isUser = message.sender === 'user';
    const isBot = message.sender === 'bot';

    return (
      <motion.div
        key={message.id}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`message ${message.sender}`}
      >
        {!isUser && (
          <div className="message-avatar">
            {isBot ? '🤖' : (message.metadata?.agentAvatar || '👤')}
          </div>
        )}
        
        <div className="message-content">
          {!isUser && (
            <div className="message-sender">
              {isBot ? 'Assistant IA' : message.metadata?.agentName || 'Agent'}
            </div>
          )}
          
          <div className="message-bubble">
            {message.content.split('\n').map((line, index) => (
              <p key={index}>{line}</p>
            ))}
            
            {message.metadata?.attachments && (
              <div className="message-attachments">
                {message.metadata.attachments.map((attachment, index) => (
                  <a
                    key={index}
                    href={attachment.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="attachment-link"
                  >
                    📎 {attachment.name}
                  </a>
                ))}
              </div>
            )}
          </div>
          
          <div className="message-time">
            {message.timestamp.toLocaleTimeString('fr-FR', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="live-chat">
      {/* Chat Header */}
      <div className="chat-header">
        <div className="chat-info">
          <div className="chat-status">
            <span className={`status-indicator ${isConnected ? 'online' : 'offline'}`} />
            <span className="status-text">
              {session?.status === 'waiting' 
                ? `En attente (${session.queue.position} en file)`
                : session?.status === 'active'
                ? `Connecté avec ${session.agent?.name || 'un agent'}`
                : 'Support Retreat And Be'
              }
            </span>
          </div>
        </div>
        
        <div className="chat-actions">
          <button onClick={onMinimize} className="chat-action">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 13H5v-2h14v2z"/>
            </svg>
          </button>
          <button onClick={onClose} className="chat-action">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
      </div>

      {/* Messages Area */}
      <div className="chat-messages">
        {messages.map(renderMessage)}
        
        {isTyping && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="typing-indicator"
          >
            <div className="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span className="typing-text">L'agent écrit...</span>
          </motion.div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Replies */}
      {showQuickReplies && (
        <div className="quick-replies">
          {QUICK_REPLIES.slice(0, 3).map((reply) => (
            <button
              key={reply.id}
              className="quick-reply-button"
              onClick={() => sendMessage(reply.text, 'quick_reply')}
            >
              {reply.text}
            </button>
          ))}
        </div>
      )}

      {/* Input Area */}
      <div className="chat-input">
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileUpload}
          style={{ display: 'none' }}
          accept="image/*,.pdf,.doc,.docx"
        />
        
        <button
          className="attachment-button"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
        >
          {isUploading ? '⏳' : '📎'}
        </button>
        
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && sendMessage(inputValue)}
          placeholder="Tapez votre message..."
          className="message-input"
        />
        
        <button
          className="send-button"
          onClick={() => sendMessage(inputValue)}
          disabled={!inputValue.trim()}
        >
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
          </svg>
        </button>
      </div>
    </div>
  );
};

export { ChatWidget, LiveChat };
export default ChatWidget;
