/**
 * Pricing Plans Component - Sprint 19
 * Commercial pricing strategy with conversion optimization
 * Date: 23 Juillet 2025
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router';
import { useAuth } from '../hooks/useAuth';
import { useAnalytics } from '../hooks/useAnalytics';
import { SubscriptionAPI } from '../services/subscriptionAPI';

// Types
interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
  };
  features: string[];
  limits: {
    participants: number | 'unlimited';
    retreats: number | 'unlimited';
    storage: string;
    support: string;
  };
  popular?: boolean;
  enterprise?: boolean;
  cta: string;
  badge?: string;
}

interface PricingFeature {
  name: string;
  starter: boolean | string;
  professional: boolean | string;
  enterprise: boolean | string;
  tooltip?: string;
}

// Pricing Plans Data
const PRICING_PLANS: PricingPlan[] = [
  {
    id: 'starter',
    name: 'Starter',
    description: 'Parfait pour débuter dans l\'organisation de retraites',
    price: {
      monthly: 29,
      yearly: 290 // 2 mois gratuits
    },
    features: [
      'Jusqu\'à 100 participants',
      'Gestion de base des retraites',
      'Support email',
      'Application mobile',
      'Paiements sécurisés',
      'Calendrier intégré'
    ],
    limits: {
      participants: 100,
      retreats: 5,
      storage: '5 GB',
      support: 'Email (48h)'
    },
    cta: 'Commencer l\'essai gratuit'
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'Pour les organisateurs sérieux qui veulent faire grandir leur activité',
    price: {
      monthly: 99,
      yearly: 990 // 2 mois gratuits
    },
    features: [
      'Jusqu\'à 500 participants',
      'Analytics avancées',
      'Support prioritaire',
      'Branding personnalisé',
      'API et intégrations',
      'Automatisations marketing',
      'Rapports détaillés',
      'Multi-utilisateurs'
    ],
    limits: {
      participants: 500,
      retreats: 'unlimited',
      storage: '50 GB',
      support: 'Chat + Email (4h)'
    },
    popular: true,
    badge: 'Le plus populaire',
    cta: 'Démarrer Professional'
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Solution complète pour les grandes organisations',
    price: {
      monthly: 299,
      yearly: 2990 // 2 mois gratuits
    },
    features: [
      'Participants illimités',
      'Solution white-label',
      'Support dédié 24/7',
      'Intégrations personnalisées',
      'SLA garantis',
      'Formation équipe',
      'Gestionnaire de compte',
      'Sécurité avancée'
    ],
    limits: {
      participants: 'unlimited',
      retreats: 'unlimited',
      storage: '500 GB',
      support: 'Téléphone + Chat 24/7'
    },
    enterprise: true,
    cta: 'Contacter les ventes'
  }
];

// Feature Comparison Data
const FEATURE_COMPARISON: PricingFeature[] = [
  {
    name: 'Participants maximum',
    starter: '100',
    professional: '500',
    enterprise: 'Illimité'
  },
  {
    name: 'Retraites par mois',
    starter: '5',
    professional: 'Illimité',
    enterprise: 'Illimité'
  },
  {
    name: 'Stockage',
    starter: '5 GB',
    professional: '50 GB',
    enterprise: '500 GB'
  },
  {
    name: 'Support client',
    starter: 'Email (48h)',
    professional: 'Chat + Email (4h)',
    enterprise: '24/7 Téléphone + Chat'
  },
  {
    name: 'Analytics de base',
    starter: true,
    professional: true,
    enterprise: true
  },
  {
    name: 'Analytics avancées',
    starter: false,
    professional: true,
    enterprise: true
  },
  {
    name: 'Branding personnalisé',
    starter: false,
    professional: true,
    enterprise: true
  },
  {
    name: 'API et intégrations',
    starter: false,
    professional: true,
    enterprise: true
  },
  {
    name: 'White-label',
    starter: false,
    professional: false,
    enterprise: true
  },
  {
    name: 'SLA garantis',
    starter: false,
    professional: false,
    enterprise: true
  }
];

// Pricing Plan Card Component
const PricingCard: React.FC<{
  plan: PricingPlan;
  isYearly: boolean;
  onSelect: (planId: string) => void;
}> = ({ plan, isYearly, onSelect }) => {
  const { track } = useAnalytics();
  const price = isYearly ? plan.price.yearly : plan.price.monthly;
  const monthlyPrice = isYearly ? plan.price.yearly / 12 : plan.price.monthly;
  const savings = isYearly ? ((plan.price.monthly * 12) - plan.price.yearly) : 0;

  const handleSelect = () => {
    track('pricing_plan_select', {
      plan_id: plan.id,
      plan_name: plan.name,
      billing_cycle: isYearly ? 'yearly' : 'monthly',
      price: price
    });
    
    onSelect(plan.id);
  };

  return (
    <motion.div
      className={`pricing-card ${plan.popular ? 'popular' : ''} ${plan.enterprise ? 'enterprise' : ''}`}
      whileHover={{ scale: 1.02, y: -5 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {plan.badge && (
        <div className="plan-badge">{plan.badge}</div>
      )}
      
      <div className="plan-header">
        <h3 className="plan-name">{plan.name}</h3>
        <p className="plan-description">{plan.description}</p>
        
        <div className="plan-pricing">
          <div className="price-display">
            <span className="currency">€</span>
            <span className="amount">{Math.round(monthlyPrice)}</span>
            <span className="period">/mois</span>
          </div>
          
          {isYearly && savings > 0 && (
            <div className="savings-badge">
              Économisez {savings}€/an
            </div>
          )}
          
          {isYearly && (
            <div className="billing-note">
              Facturé {price}€ annuellement
            </div>
          )}
        </div>
      </div>

      <div className="plan-features">
        <ul>
          {plan.features.map((feature, index) => (
            <motion.li
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <span className="feature-check">✓</span>
              {feature}
            </motion.li>
          ))}
        </ul>
      </div>

      <div className="plan-limits">
        <div className="limit-item">
          <span className="limit-label">Participants:</span>
          <span className="limit-value">{plan.limits.participants}</span>
        </div>
        <div className="limit-item">
          <span className="limit-label">Stockage:</span>
          <span className="limit-value">{plan.limits.storage}</span>
        </div>
        <div className="limit-item">
          <span className="limit-label">Support:</span>
          <span className="limit-value">{plan.limits.support}</span>
        </div>
      </div>

      <button
        className={`plan-cta ${plan.popular ? 'primary' : 'secondary'}`}
        onClick={handleSelect}
      >
        {plan.cta}
      </button>

      {plan.id === 'starter' && (
        <div className="trial-note">
          ✨ 14 jours d'essai gratuit
        </div>
      )}
    </motion.div>
  );
};

// Feature Comparison Table
const FeatureComparison: React.FC = () => {
  return (
    <div className="feature-comparison">
      <h3>Comparaison détaillée des fonctionnalités</h3>
      
      <div className="comparison-table">
        <div className="table-header">
          <div className="feature-column">Fonctionnalités</div>
          <div className="plan-column">Starter</div>
          <div className="plan-column">Professional</div>
          <div className="plan-column">Enterprise</div>
        </div>
        
        {FEATURE_COMPARISON.map((feature, index) => (
          <motion.div
            key={feature.name}
            className="table-row"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
          >
            <div className="feature-name">
              {feature.name}
              {feature.tooltip && (
                <span className="tooltip" title={feature.tooltip}>ℹ️</span>
              )}
            </div>
            <div className="feature-value">
              {typeof feature.starter === 'boolean' 
                ? (feature.starter ? '✓' : '✗')
                : feature.starter
              }
            </div>
            <div className="feature-value">
              {typeof feature.professional === 'boolean' 
                ? (feature.professional ? '✓' : '✗')
                : feature.professional
              }
            </div>
            <div className="feature-value">
              {typeof feature.enterprise === 'boolean' 
                ? (feature.enterprise ? '✓' : '✗')
                : feature.enterprise
              }
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

// FAQ Section
const PricingFAQ: React.FC = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const faqs = [
    {
      question: 'Puis-je changer de plan à tout moment ?',
      answer: 'Oui, vous pouvez upgrader ou downgrader votre plan à tout moment. Les changements prennent effet immédiatement et nous ajustons la facturation au prorata.'
    },
    {
      question: 'Y a-t-il des frais cachés ?',
      answer: 'Non, nos prix sont transparents. Aucun frais de configuration, aucun frais caché. Vous payez uniquement le prix affiché pour votre plan.'
    },
    {
      question: 'Que se passe-t-il si je dépasse mes limites ?',
      answer: 'Nous vous préviendrons avant d\'atteindre vos limites. Vous pourrez alors upgrader votre plan ou nous contacter pour une solution personnalisée.'
    },
    {
      question: 'Proposez-vous des remises pour les associations ?',
      answer: 'Oui, nous offrons des tarifs préférentiels pour les associations à but non lucratif. Contactez-nous pour en savoir plus.'
    }
  ];

  return (
    <div className="pricing-faq">
      <h3>Questions fréquentes</h3>
      
      <div className="faq-list">
        {faqs.map((faq, index) => (
          <motion.div
            key={index}
            className="faq-item"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: index * 0.1 }}
          >
            <button
              className="faq-question"
              onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
            >
              {faq.question}
              <span className={`faq-arrow ${openFAQ === index ? 'open' : ''}`}>
                ▼
              </span>
            </button>
            
            {openFAQ === index && (
              <motion.div
                className="faq-answer"
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
              >
                {faq.answer}
              </motion.div>
            )}
          </motion.div>
        ))}
      </div>
    </div>
  );
};

// Main Pricing Component
export const PricingPlans: React.FC = () => {
  const [isYearly, setIsYearly] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const router = useRouter();
  const { user } = useAuth();
  const { track } = useAnalytics();

  useEffect(() => {
    track('pricing_page_view', {
      user_id: user?.id,
      billing_toggle: isYearly ? 'yearly' : 'monthly'
    });
  }, [isYearly]);

  const handlePlanSelect = async (planId: string) => {
    setSelectedPlan(planId);
    
    if (planId === 'enterprise') {
      // Redirect to contact sales
      track('contact_sales_click', { plan: planId });
      router.push('/contact-sales');
    } else {
      // Redirect to checkout
      track('checkout_start', { 
        plan: planId,
        billing_cycle: isYearly ? 'yearly' : 'monthly'
      });
      
      const plan = PRICING_PLANS.find(p => p.id === planId);
      const price = isYearly ? plan?.price.yearly : plan?.price.monthly;
      
      router.push(`/checkout?plan=${planId}&billing=${isYearly ? 'yearly' : 'monthly'}&price=${price}`);
    }
  };

  return (
    <div className="pricing-section">
      <div className="container">
        {/* Header */}
        <div className="pricing-header">
          <h2>Choisissez le plan qui vous convient</h2>
          <p>
            Commencez gratuitement, puis choisissez un plan qui évolue avec votre activité.
            Tous les plans incluent un essai gratuit de 14 jours.
          </p>
          
          {/* Billing Toggle */}
          <div className="billing-toggle">
            <span className={!isYearly ? 'active' : ''}>Mensuel</span>
            <button
              className="toggle-switch"
              onClick={() => {
                setIsYearly(!isYearly);
                track('billing_toggle', { 
                  new_value: !isYearly ? 'yearly' : 'monthly' 
                });
              }}
            >
              <span className={`toggle-slider ${isYearly ? 'yearly' : ''}`} />
            </button>
            <span className={isYearly ? 'active' : ''}>
              Annuel
              <span className="savings-badge">-17%</span>
            </span>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="pricing-grid">
          {PRICING_PLANS.map((plan, index) => (
            <PricingCard
              key={plan.id}
              plan={plan}
              isYearly={isYearly}
              onSelect={handlePlanSelect}
            />
          ))}
        </div>

        {/* Feature Comparison */}
        <FeatureComparison />

        {/* FAQ */}
        <PricingFAQ />

        {/* Trust Signals */}
        <div className="trust-signals">
          <div className="trust-item">
            <span className="trust-icon">🔒</span>
            <span>Paiements sécurisés SSL</span>
          </div>
          <div className="trust-item">
            <span className="trust-icon">↩️</span>
            <span>Garantie satisfait ou remboursé 30 jours</span>
          </div>
          <div className="trust-item">
            <span className="trust-icon">🎧</span>
            <span>Support client 24/7</span>
          </div>
          <div className="trust-item">
            <span className="trust-icon">📱</span>
            <span>Accès mobile inclus</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingPlans;
