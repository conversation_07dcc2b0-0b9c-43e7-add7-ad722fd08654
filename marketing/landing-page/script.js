/**
 * Retreat And Be - Landing Page JavaScript
 * Sprint 19 - Commercial Launch Preparation
 * Date: 23 Juillet 2025
 */

// Global configuration
const CONFIG = {
    API_BASE_URL: 'https://api.retreatandbe.com',
    TRACKING_ID: 'GA_MEASUREMENT_ID',
    HOTJAR_ID: '3456789',
    DEMO_VIDEO_URL: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    SIGNUP_URL: '/signup',
    LOGIN_URL: '/login'
};

// Analytics and tracking
class Analytics {
    static track(event, properties = {}) {
        // Google Analytics 4
        if (typeof gtag !== 'undefined') {
            gtag('event', event, {
                event_category: properties.category || 'engagement',
                event_label: properties.label || '',
                value: properties.value || 1,
                ...properties
            });
        }

        // Hotjar tracking
        if (typeof hj !== 'undefined') {
            hj('event', event);
        }

        // Custom analytics
        this.sendCustomEvent(event, properties);
    }

    static trackConversion(type, value = 0) {
        this.track('conversion', {
            category: 'conversion',
            label: type,
            value: value,
            send_to: 'AW-CONVERSION_ID/CONVERSION_LABEL'
        });
    }

    static trackPageView(page) {
        if (typeof gtag !== 'undefined') {
            gtag('config', CONFIG.TRACKING_ID, {
                page_title: document.title,
                page_location: window.location.href,
                page_path: page || window.location.pathname
            });
        }
    }

    static sendCustomEvent(event, properties) {
        // Send to custom analytics endpoint
        fetch(`${CONFIG.API_BASE_URL}/analytics/track`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                event,
                properties: {
                    ...properties,
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    referrer: document.referrer,
                    user_agent: navigator.userAgent
                }
            })
        }).catch(error => {
            console.warn('Analytics tracking failed:', error);
        });
    }
}

// Landing page functionality
class LandingPage {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupScrollEffects();
        this.setupMobileMenu();
        this.setupModal();
        this.trackPageLoad();
        this.setupFormValidation();
        this.setupAnimations();
    }

    setupEventListeners() {
        // CTA buttons
        document.querySelectorAll('[onclick="startTrial()"]').forEach(button => {
            button.addEventListener('click', this.handleStartTrial.bind(this));
        });

        document.querySelectorAll('[onclick="watchDemo()"]').forEach(button => {
            button.addEventListener('click', this.handleWatchDemo.bind(this));
        });

        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', this.handleNavClick.bind(this));
        });

        // Feature cards hover
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', this.handleFeatureHover.bind(this));
        });

        // Scroll to section links
        document.querySelectorAll('a[href^="#"]').forEach(link => {
            link.addEventListener('click', this.handleSmoothScroll.bind(this));
        });
    }

    setupScrollEffects() {
        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = 'none';
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    
                    // Track section views
                    const sectionName = entry.target.id || entry.target.className;
                    Analytics.track('section_view', {
                        category: 'engagement',
                        label: sectionName
                    });
                }
            });
        }, observerOptions);

        // Observe sections
        document.querySelectorAll('section').forEach(section => {
            observer.observe(section);
        });
    }

    setupMobileMenu() {
        const toggle = document.querySelector('.mobile-menu-toggle');
        const menu = document.querySelector('.nav-menu');

        if (toggle && menu) {
            toggle.addEventListener('click', () => {
                menu.classList.toggle('mobile-open');
                toggle.classList.toggle('active');
                
                Analytics.track('mobile_menu_toggle', {
                    category: 'navigation',
                    label: menu.classList.contains('mobile-open') ? 'open' : 'close'
                });
            });
        }
    }

    setupModal() {
        const modal = document.getElementById('demo-modal');
        const closeBtn = document.querySelector('.close');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeModal();
            });
        }

        // Close modal on outside click
        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                this.closeModal();
            }
        });

        // Close modal on escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && modal.style.display === 'block') {
                this.closeModal();
            }
        });
    }

    setupFormValidation() {
        // Email validation for newsletter signup
        document.querySelectorAll('input[type="email"]').forEach(input => {
            input.addEventListener('blur', this.validateEmail.bind(this));
        });
    }

    setupAnimations() {
        // Add CSS classes for animations
        const style = document.createElement('style');
        style.textContent = `
            .animate-in {
                animation: slideInUp 0.6s ease-out forwards;
            }
            
            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .mobile-open {
                display: flex !important;
                flex-direction: column;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                padding: 1rem;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            }
            
            .mobile-menu-toggle.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
            }
            
            .mobile-menu-toggle.active span:nth-child(2) {
                opacity: 0;
            }
            
            .mobile-menu-toggle.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -6px);
            }
        `;
        document.head.appendChild(style);
    }

    handleStartTrial(event) {
        event.preventDefault();
        
        // Track conversion
        Analytics.trackConversion('trial_start', 1);
        
        // Track button location
        const buttonLocation = this.getButtonLocation(event.target);
        Analytics.track('cta_click', {
            category: 'conversion',
            label: `start_trial_${buttonLocation}`,
            button_text: event.target.textContent.trim()
        });

        // Add loading state
        const originalText = event.target.textContent;
        event.target.textContent = 'Redirection...';
        event.target.disabled = true;

        // Simulate loading and redirect
        setTimeout(() => {
            window.location.href = `${CONFIG.SIGNUP_URL}?source=landing_${buttonLocation}&utm_campaign=trial_signup`;
        }, 500);
    }

    handleWatchDemo(event) {
        event.preventDefault();
        
        const buttonLocation = this.getButtonLocation(event.target);
        Analytics.track('demo_view', {
            category: 'engagement',
            label: `demo_${buttonLocation}`
        });

        this.showModal();
    }

    handleNavClick(event) {
        const linkText = event.target.textContent.trim();
        Analytics.track('nav_click', {
            category: 'navigation',
            label: linkText.toLowerCase()
        });
    }

    handleFeatureHover(event) {
        const featureTitle = event.target.querySelector('.feature-title')?.textContent;
        Analytics.track('feature_hover', {
            category: 'engagement',
            label: featureTitle?.toLowerCase() || 'unknown'
        });
    }

    handleSmoothScroll(event) {
        event.preventDefault();
        const targetId = event.target.getAttribute('href');
        const targetElement = document.querySelector(targetId);
        
        if (targetElement) {
            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
            
            Analytics.track('scroll_to_section', {
                category: 'navigation',
                label: targetId.substring(1)
            });
        }
    }

    showModal() {
        const modal = document.getElementById('demo-modal');
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            // Load video iframe
            const iframe = modal.querySelector('iframe');
            if (iframe && !iframe.src) {
                iframe.src = CONFIG.DEMO_VIDEO_URL;
            }
        }
    }

    closeModal() {
        const modal = document.getElementById('demo-modal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
            
            // Stop video
            const iframe = modal.querySelector('iframe');
            if (iframe) {
                iframe.src = iframe.src; // Reload to stop video
            }
            
            Analytics.track('demo_close', {
                category: 'engagement',
                label: 'modal_close'
            });
        }
    }

    validateEmail(event) {
        const email = event.target.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            event.target.setCustomValidity('Veuillez entrer une adresse email valide');
            event.target.classList.add('invalid');
        } else {
            event.target.setCustomValidity('');
            event.target.classList.remove('invalid');
        }
    }

    getButtonLocation(button) {
        // Determine button location for tracking
        const section = button.closest('section');
        if (section) {
            return section.className.split(' ')[0] || section.id || 'unknown';
        }
        return 'unknown';
    }

    trackPageLoad() {
        // Track page load with performance metrics
        window.addEventListener('load', () => {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            
            Analytics.track('page_load', {
                category: 'performance',
                label: 'landing_page',
                value: Math.round(loadTime)
            });

            // Track page performance
            if ('connection' in navigator) {
                Analytics.track('connection_type', {
                    category: 'performance',
                    label: navigator.connection.effectiveType || 'unknown'
                });
            }
        });

        // Track time on page
        let timeOnPage = 0;
        const startTime = Date.now();
        
        setInterval(() => {
            timeOnPage = Math.round((Date.now() - startTime) / 1000);
        }, 1000);

        // Track when user leaves
        window.addEventListener('beforeunload', () => {
            Analytics.track('time_on_page', {
                category: 'engagement',
                label: 'landing_page',
                value: timeOnPage
            });
        });
    }
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Global functions for inline event handlers (backward compatibility)
function startTrial() {
    if (window.landingPage) {
        const event = { target: document.activeElement, preventDefault: () => {} };
        window.landingPage.handleStartTrial(event);
    }
}

function watchDemo() {
    if (window.landingPage) {
        const event = { target: document.activeElement, preventDefault: () => {} };
        window.landingPage.handleWatchDemo(event);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.landingPage = new LandingPage();
    
    // Track initial page view
    Analytics.trackPageView('/');
    
    console.log('🚀 Retreat And Be Landing Page initialized');
});

// Error handling
window.addEventListener('error', (event) => {
    Analytics.track('javascript_error', {
        category: 'error',
        label: event.error?.message || 'unknown_error',
        value: 1
    });
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LandingPage, Analytics };
}
