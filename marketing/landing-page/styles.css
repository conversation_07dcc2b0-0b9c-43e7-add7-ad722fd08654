/* Retreat And Be - Landing Page Styles */
/* Sprint 19 - Commercial Launch Preparation */

:root {
  /* Colors */
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #a5b4fc;
  --secondary-color: #10b981;
  --accent-color: #f59e0b;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --background: #ffffff;
  --background-light: #f9fafb;
  --border-color: #e5e7eb;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-display: 'Playfair Display', serif;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: 3.5rem;
  font-family: var(--font-display);
}

h2 {
  font-size: 2.5rem;
  font-family: var(--font-display);
}

h3 {
  font-size: 1.5rem;
}

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

/* Buttons */
.cta-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.cta-button.primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  box-shadow: var(--shadow-md);
}

.cta-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.cta-button.secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.cta-button.secondary:hover {
  background: var(--primary-color);
  color: white;
}

.cta-button.large {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: 1.125rem;
}

.cta-button .play-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: var(--spacing-sm);
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  transition: all var(--transition-normal);
}

.navbar .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo {
  height: 2rem;
  width: auto;
}

.brand-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-display);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.nav-link {
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.nav-link:hover {
  color: var(--primary-color);
}

.login-link {
  color: var(--text-secondary);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  transition: all var(--transition-fast);
}

/* Hero Section */
.hero {
  padding: 8rem 0 4rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 3.5rem;
  line-height: 1.1;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.hero-title .highlight {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: var(--spacing-2xl);
  color: var(--text-secondary);
}

.hero-stats {
  display: flex;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-display);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.hero-cta {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.hero-note {
  font-size: 0.875rem;
  color: var(--text-light);
  margin: 0;
}

/* Hero Visual */
.hero-visual {
  position: relative;
}

.hero-image {
  position: relative;
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
}

.dashboard-image {
  width: 100%;
  height: auto;
  border-radius: 1rem;
  box-shadow: var(--shadow-xl);
}

.floating-card {
  position: absolute;
  background: white;
  border-radius: 0.75rem;
  padding: var(--spacing-md);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  animation: float 3s ease-in-out infinite;
}

.floating-card.card-1 {
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.floating-card.card-2 {
  top: 50%;
  right: -15%;
  animation-delay: 1s;
}

.floating-card.card-3 {
  bottom: 20%;
  left: -5%;
  animation-delay: 2s;
}

.card-icon {
  font-size: 1.5rem;
}

.card-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.card-subtitle {
  font-size: 0.75rem;
  color: var(--text-light);
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Social Proof */
.social-proof {
  padding: var(--spacing-2xl) 0;
  background: var(--background);
  border-bottom: 1px solid var(--border-color);
}

.social-proof-text {
  text-align: center;
  font-size: 0.875rem;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: var(--spacing-lg);
}

.logos-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: var(--spacing-lg);
  align-items: center;
}

.logo-item {
  height: 2rem;
  width: auto;
  opacity: 0.6;
  transition: opacity var(--transition-normal);
  filter: grayscale(100%);
}

.logo-item:hover {
  opacity: 1;
  filter: grayscale(0%);
}

/* Features Section */
.features {
  padding: var(--spacing-3xl) 0;
  background: var(--background-light);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.section-title {
  margin-bottom: var(--spacing-md);
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-2xl);
}

.feature-card {
  background: white;
  padding: var(--spacing-2xl);
  border-radius: 1rem;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-color);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
}

.feature-icon svg {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}

.feature-title {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.feature-description {
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.feature-list {
  list-style: none;
}

.feature-list li {
  padding: var(--spacing-xs) 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--spacing-lg);
}

.feature-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--success-color);
  font-weight: 600;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.modal-content {
  position: relative;
  background-color: white;
  margin: 5% auto;
  padding: 0;
  border-radius: 1rem;
  width: 90%;
  max-width: 800px;
  overflow: hidden;
}

.close {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  color: white;
  font-size: 2rem;
  font-weight: bold;
  cursor: pointer;
  z-index: 1;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-container {
  position: relative;
  padding-bottom: 56.25%;
  height: 0;
  overflow: hidden;
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-cta {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-stats {
    justify-content: center;
  }
  
  .logos-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .floating-card {
    display: none;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-md);
  }
  
  .hero {
    padding: 6rem 0 2rem;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .cta-button.large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
  }
}
