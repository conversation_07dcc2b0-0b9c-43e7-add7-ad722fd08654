# 🔐 BACKEND NESTJS - CONFIGURATION ENVIRONNEMENT
# Sprint 15 - Migration des secrets

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/retreatandbe_dev
MONGODB_URI=mongodb://localhost:27017/retreatandbe

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT & Authentication
JWT_SECRET=your-super-secret-jwt-key-minimum-32-chars
JWT_REFRESH_SECRET=your-super-secret-refresh-key-minimum-32-chars
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# API Keys
OPENAI_API_KEY=sk-your-openai-api-key-here
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key-here
SENDGRID_API_KEY=SG.your-sendgrid-api-key-here

# External Services
SENTRY_DSN=https://your-sentry-dsn-here
CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here
HASH_SALT=your-hash-salt-here
CORS_ORIGIN=http://localhost:3000,http://localhost:5173

# Server Configuration
PORT=3001
NODE_ENV=development
API_PREFIX=api/v1

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./uploads

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100
