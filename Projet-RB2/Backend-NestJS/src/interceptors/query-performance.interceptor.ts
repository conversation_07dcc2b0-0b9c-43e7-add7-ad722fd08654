import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

/**
 * 📊 INTERCEPTEUR DE PERFORMANCE DES REQUÊTES
 * 
 * Monitore les performances des requêtes et détecte les requêtes lentes
 * Fait partie des optimisations Sprint 16 - Performance
 */
@Injectable()
export class QueryPerformanceInterceptor implements NestInterceptor {
  private readonly logger = new Logger(QueryPerformanceInterceptor.name);

  // Seuils de performance (en millisecondes)
  private readonly SLOW_QUERY_THRESHOLD = 100;
  private readonly VERY_SLOW_QUERY_THRESHOLD = 1000;

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const startTime = Date.now();
    const method = request.method;
    const url = request.url;
    const userAgent = request.get('User-Agent') || 'Unknown';

    return next.handle().pipe(
      tap(() => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // Log des métriques de base
        this.logPerformanceMetrics(method, url, duration, userAgent);

        // Alertes pour requêtes lentes
        if (duration > this.VERY_SLOW_QUERY_THRESHOLD) {
          this.logger.error(
            `🚨 REQUÊTE TRÈS LENTE: ${method} ${url} - ${duration}ms`,
            {
              method,
              url,
              duration,
              userAgent,
              threshold: this.VERY_SLOW_QUERY_THRESHOLD,
            }
          );
        } else if (duration > this.SLOW_QUERY_THRESHOLD) {
          this.logger.warn(
            `⚠️ REQUÊTE LENTE: ${method} ${url} - ${duration}ms`,
            {
              method,
              url,
              duration,
              userAgent,
              threshold: this.SLOW_QUERY_THRESHOLD,
            }
          );
        }

        // Métriques pour monitoring externe (Prometheus, etc.)
        this.recordMetrics(method, url, duration);
      })
    );
  }

  /**
   * Log des métriques de performance
   */
  private logPerformanceMetrics(
    method: string,
    url: string,
    duration: number,
    userAgent: string
  ): void {
    const logLevel = this.getLogLevel(duration);
    const message = `${method} ${url} - ${duration}ms`;

    switch (logLevel) {
      case 'error':
        this.logger.error(message);
        break;
      case 'warn':
        this.logger.warn(message);
        break;
      case 'debug':
        this.logger.debug(message);
        break;
      default:
        this.logger.log(message);
    }
  }

  /**
   * Détermine le niveau de log basé sur la durée
   */
  private getLogLevel(duration: number): string {
    if (duration > this.VERY_SLOW_QUERY_THRESHOLD) {
      return 'error';
    } else if (duration > this.SLOW_QUERY_THRESHOLD) {
      return 'warn';
    } else if (duration > 50) {
      return 'debug';
    }
    return 'log';
  }

  /**
   * Enregistre les métriques pour monitoring externe
   */
  private recordMetrics(method: string, url: string, duration: number): void {
    // Ici, vous pouvez intégrer avec Prometheus, StatsD, etc.
    // Exemple avec un service de métriques personnalisé
    
    try {
      // Simulation d'envoi de métriques
      const metrics = {
        timestamp: new Date().toISOString(),
        method,
        url: this.sanitizeUrl(url),
        duration,
        status: this.getPerformanceStatus(duration),
      };

      // Log pour debugging (remplacer par votre service de métriques)
      this.logger.debug('📊 Métriques enregistrées', metrics);

      // TODO: Intégrer avec votre service de monitoring
      // await this.metricsService.record(metrics);
    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'enregistrement des métriques', error);
    }
  }

  /**
   * Nettoie l'URL pour les métriques (supprime les IDs, etc.)
   */
  private sanitizeUrl(url: string): string {
    return url
      .replace(/\/\d+/g, '/:id') // Remplace les IDs numériques
      .replace(/\/[a-f0-9-]{36}/g, '/:uuid') // Remplace les UUIDs
      .replace(/\?.*$/, ''); // Supprime les query parameters
  }

  /**
   * Détermine le statut de performance
   */
  private getPerformanceStatus(duration: number): string {
    if (duration <= 50) return 'excellent';
    if (duration <= 100) return 'good';
    if (duration <= 500) return 'acceptable';
    if (duration <= 1000) return 'slow';
    return 'very_slow';
  }
}

/**
 * 📊 INTERFACE POUR LES MÉTRIQUES DE PERFORMANCE
 */
export interface PerformanceMetrics {
  timestamp: string;
  method: string;
  url: string;
  duration: number;
  status: 'excellent' | 'good' | 'acceptable' | 'slow' | 'very_slow';
  userAgent?: string;
}

/**
 * 🎯 CONFIGURATION DES SEUILS DE PERFORMANCE
 */
export const PERFORMANCE_THRESHOLDS = {
  EXCELLENT: 50,    // ≤ 50ms
  GOOD: 100,        // ≤ 100ms (objectif Sprint 16)
  ACCEPTABLE: 500,  // ≤ 500ms
  SLOW: 1000,       // ≤ 1000ms
  VERY_SLOW: Infinity, // > 1000ms
} as const;

/**
 * 📈 UTILITAIRES POUR L'ANALYSE DES PERFORMANCES
 */
export class PerformanceAnalyzer {
  /**
   * Calcule les percentiles P50, P95, P99
   */
  static calculatePercentiles(durations: number[]): {
    p50: number;
    p95: number;
    p99: number;
  } {
    const sorted = durations.sort((a, b) => a - b);
    const length = sorted.length;

    return {
      p50: sorted[Math.floor(length * 0.5)],
      p95: sorted[Math.floor(length * 0.95)],
      p99: sorted[Math.floor(length * 0.99)],
    };
  }

  /**
   * Génère un rapport de performance
   */
  static generateReport(metrics: PerformanceMetrics[]): {
    totalRequests: number;
    averageDuration: number;
    percentiles: { p50: number; p95: number; p99: number };
    statusDistribution: Record<string, number>;
  } {
    const durations = metrics.map(m => m.duration);
    const statusCounts = metrics.reduce((acc, m) => {
      acc[m.status] = (acc[m.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalRequests: metrics.length,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      percentiles: this.calculatePercentiles(durations),
      statusDistribution: statusCounts,
    };
  }
}
