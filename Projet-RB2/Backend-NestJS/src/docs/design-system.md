# Design System Integration - Backend NestJS

## Sprint 15 - Documentation API

Le Backend NestJS utilise le Design System pour:

1. **Documentation Swagger** - Styles cohérents
2. **Emails Templates** - Composants réutilisables  
3. **Admin Interface** - UI unifiée

## Configuration

```typescript
// swagger.config.ts
export const swaggerConfig = {
  customCss: '@import url("design-system/dist/styles.css");',
  customSiteTitle: 'Retreat And Be API - Design System',
};
```

## Utilisation

Les templates d'emails utilisent les tokens de couleur du Design System pour maintenir la cohérence visuelle.
