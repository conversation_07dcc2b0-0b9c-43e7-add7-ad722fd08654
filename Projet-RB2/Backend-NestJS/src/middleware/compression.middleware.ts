import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as compression from 'compression';

/**
 * 🗜️ MIDDLEWARE DE COMPRESSION AVANCÉE
 * 
 * Optimise les réponses HTTP avec compression intelligente
 * Fait partie des optimisations Sprint 16 - Performance
 */
@Injectable()
export class CompressionMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CompressionMiddleware.name);
  private readonly compressionHandler: any;

  constructor() {
    this.compressionHandler = compression({
      // Niveau de compression (0-9, 6 est optimal)
      level: 6,
      
      // Seuil minimum pour compression (1KB)
      threshold: 1024,
      
      // Types MIME à compresser
      filter: this.shouldCompress.bind(this),
      
      // Chunk size pour streaming
      chunkSize: 16 * 1024, // 16KB
      
      // Window size pour gzip
      windowBits: 15,
      
      // Memory level pour gzip
      memLevel: 8,
    });
  }

  use(req: Request, res: Response, next: NextFunction): void {
    // Log de la requête entrante
    this.logCompressionAttempt(req);

    // Appliquer la compression
    this.compressionHandler(req, res, (err?: any) => {
      if (err) {
        this.logger.error('❌ Erreur de compression', {
          error: err.message,
          url: req.url,
          method: req.method,
        });
      } else {
        this.logCompressionResult(req, res);
      }
      next(err);
    });
  }

  /**
   * Détermine si la réponse doit être compressée
   */
  private shouldCompress(req: Request, res: Response): boolean {
    // Ne pas compresser si déjà compressé
    if (res.getHeader('content-encoding')) {
      return false;
    }

    // Ne pas compresser les petites réponses
    const contentLength = res.getHeader('content-length');
    if (contentLength && parseInt(contentLength.toString()) < 1024) {
      return false;
    }

    // Types MIME à compresser
    const contentType = res.getHeader('content-type')?.toString() || '';
    const compressibleTypes = [
      'text/',
      'application/json',
      'application/javascript',
      'application/xml',
      'application/rss+xml',
      'application/atom+xml',
      'image/svg+xml',
      'application/x-font-ttf',
      'application/vnd.ms-fontobject',
      'font/opentype',
    ];

    const shouldCompress = compressibleTypes.some(type => 
      contentType.startsWith(type)
    );

    // Types à exclure explicitement
    const excludeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'video/',
      'audio/',
      'application/pdf',
      'application/zip',
      'application/gzip',
    ];

    const shouldExclude = excludeTypes.some(type => 
      contentType.startsWith(type)
    );

    return shouldCompress && !shouldExclude;
  }

  /**
   * Log de la tentative de compression
   */
  private logCompressionAttempt(req: Request): void {
    const acceptEncoding = req.get('Accept-Encoding') || '';
    const supportsGzip = acceptEncoding.includes('gzip');
    const supportsBrotli = acceptEncoding.includes('br');

    this.logger.debug('🗜️ Tentative de compression', {
      url: req.url,
      method: req.method,
      acceptEncoding,
      supportsGzip,
      supportsBrotli,
    });
  }

  /**
   * Log du résultat de compression
   */
  private logCompressionResult(req: Request, res: Response): void {
    const contentEncoding = res.getHeader('content-encoding');
    const contentLength = res.getHeader('content-length');
    const originalSize = res.getHeader('x-original-size');

    if (contentEncoding) {
      const compressionRatio = originalSize && contentLength 
        ? ((parseInt(originalSize.toString()) - parseInt(contentLength.toString())) / parseInt(originalSize.toString()) * 100).toFixed(1)
        : 'N/A';

      this.logger.log('✅ Compression appliquée', {
        url: req.url,
        encoding: contentEncoding,
        originalSize: originalSize || 'N/A',
        compressedSize: contentLength || 'N/A',
        compressionRatio: compressionRatio + '%',
      });
    }
  }
}

/**
 * 📊 CONFIGURATION DE COMPRESSION AVANCÉE
 */
export interface CompressionConfig {
  level: number;
  threshold: number;
  chunkSize: number;
  windowBits: number;
  memLevel: number;
  strategy: number;
}

/**
 * 🎯 CONFIGURATIONS PRÉDÉFINIES
 */
export const COMPRESSION_CONFIGS = {
  // Configuration rapide (moins de compression, plus rapide)
  FAST: {
    level: 1,
    threshold: 2048,
    chunkSize: 32 * 1024,
    windowBits: 15,
    memLevel: 8,
    strategy: 0, // Z_DEFAULT_STRATEGY
  } as CompressionConfig,

  // Configuration équilibrée (recommandée)
  BALANCED: {
    level: 6,
    threshold: 1024,
    chunkSize: 16 * 1024,
    windowBits: 15,
    memLevel: 8,
    strategy: 0,
  } as CompressionConfig,

  // Configuration maximale (plus de compression, plus lent)
  MAX: {
    level: 9,
    threshold: 512,
    chunkSize: 8 * 1024,
    windowBits: 15,
    memLevel: 9,
    strategy: 0,
  } as CompressionConfig,
} as const;

/**
 * 📈 ANALYSEUR DE COMPRESSION
 */
export class CompressionAnalyzer {
  private static compressionStats: Map<string, {
    requests: number;
    totalOriginalSize: number;
    totalCompressedSize: number;
    averageRatio: number;
  }> = new Map();

  /**
   * Enregistre les statistiques de compression
   */
  static recordCompression(
    contentType: string,
    originalSize: number,
    compressedSize: number
  ): void {
    const stats = this.compressionStats.get(contentType) || {
      requests: 0,
      totalOriginalSize: 0,
      totalCompressedSize: 0,
      averageRatio: 0,
    };

    stats.requests++;
    stats.totalOriginalSize += originalSize;
    stats.totalCompressedSize += compressedSize;
    stats.averageRatio = ((stats.totalOriginalSize - stats.totalCompressedSize) / stats.totalOriginalSize) * 100;

    this.compressionStats.set(contentType, stats);
  }

  /**
   * Génère un rapport de compression
   */
  static generateReport(): {
    totalRequests: number;
    totalSavings: number;
    averageCompressionRatio: number;
    byContentType: Record<string, any>;
  } {
    let totalRequests = 0;
    let totalOriginalSize = 0;
    let totalCompressedSize = 0;
    const byContentType: Record<string, any> = {};

    for (const [contentType, stats] of this.compressionStats.entries()) {
      totalRequests += stats.requests;
      totalOriginalSize += stats.totalOriginalSize;
      totalCompressedSize += stats.totalCompressedSize;

      byContentType[contentType] = {
        requests: stats.requests,
        originalSize: this.formatBytes(stats.totalOriginalSize),
        compressedSize: this.formatBytes(stats.totalCompressedSize),
        savings: this.formatBytes(stats.totalOriginalSize - stats.totalCompressedSize),
        ratio: stats.averageRatio.toFixed(1) + '%',
      };
    }

    return {
      totalRequests,
      totalSavings: totalOriginalSize - totalCompressedSize,
      averageCompressionRatio: totalOriginalSize > 0 
        ? ((totalOriginalSize - totalCompressedSize) / totalOriginalSize * 100)
        : 0,
      byContentType,
    };
  }

  /**
   * Formate les bytes en format lisible
   */
  private static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Réinitialise les statistiques
   */
  static resetStats(): void {
    this.compressionStats.clear();
  }
}

/**
 * 🔧 UTILITAIRES DE COMPRESSION
 */
export class CompressionUtils {
  /**
   * Vérifie si un type MIME est compressible
   */
  static isCompressible(contentType: string): boolean {
    const compressibleTypes = [
      'text/',
      'application/json',
      'application/javascript',
      'application/xml',
      'image/svg+xml',
    ];

    return compressibleTypes.some(type => contentType.startsWith(type));
  }

  /**
   * Calcule le ratio de compression
   */
  static calculateCompressionRatio(originalSize: number, compressedSize: number): number {
    if (originalSize === 0) return 0;
    return ((originalSize - compressedSize) / originalSize) * 100;
  }

  /**
   * Détermine le meilleur algorithme de compression
   */
  static getBestCompressionAlgorithm(acceptEncoding: string): string {
    if (acceptEncoding.includes('br')) return 'br'; // Brotli (meilleur)
    if (acceptEncoding.includes('gzip')) return 'gzip'; // Gzip (standard)
    if (acceptEncoding.includes('deflate')) return 'deflate'; // Deflate (basique)
    return 'identity'; // Pas de compression
  }
}
