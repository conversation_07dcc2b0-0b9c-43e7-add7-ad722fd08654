/**
 * 🤖 DTOs Phase 4 - IA & Innovation
 * Data Transfer Objects pour les APIs d'intelligence artificielle
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsObject, IsEnum, IsOptional, IsArray, Min, Max } from 'class-validator';

// ===== FEEDBACK LOOPS =====

export enum FeedbackOutcome {
  POSITIVE = 'positive',
  NEGATIVE = 'negative',
  NEUTRAL = 'neutral'
}

export class CollectFeedbackDto {
  @ApiProperty({
    description: 'Identifiant unique de l\'utilisateur',
    example: 'user_12345'
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Action effectuée par l\'utilisateur',
    example: 'retreat_booking'
  })
  @IsString()
  action: string;

  @ApiProperty({
    description: 'Contexte de l\'action (données structurées)',
    example: {
      sessionDuration: 1200,
      pageViews: 5,
      deviceType: 'mobile',
      timestamp: '2025-05-29T10:30:00Z'
    }
  })
  @IsObject()
  context: Record<string, any>;

  @ApiProperty({
    description: 'Résultat de l\'action',
    enum: FeedbackOutcome,
    example: FeedbackOutcome.POSITIVE
  })
  @IsEnum(FeedbackOutcome)
  outcome: FeedbackOutcome;

  @ApiProperty({
    description: 'Niveau de confiance du feedback (0-1)',
    example: 0.85,
    minimum: 0,
    maximum: 1
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence: number;

  @ApiPropertyOptional({
    description: 'Métadonnées supplémentaires',
    example: { source: 'user_interaction', version: '1.0' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class PredictOutcomeDto {
  @ApiProperty({
    description: 'Contexte pour la prédiction',
    example: {
      userId: 'user_12345',
      sessionDuration: 800,
      pageViews: 3,
      userScore: 0.7,
      deviceType: 'desktop'
    }
  })
  @IsObject()
  context: Record<string, any>;
}

// ===== RECOMMANDATIONS =====

export enum RecommendationType {
  COLLABORATIVE = 'collaborative',
  CONTENT = 'content',
  HYBRID = 'hybrid',
  CONTEXTUAL = 'contextual',
  ALL = 'all'
}

export class GenerateRecommendationsDto {
  @ApiProperty({
    description: 'Identifiant de l\'utilisateur',
    example: 'user_12345'
  })
  @IsString()
  userId: string;

  @ApiPropertyOptional({
    description: 'Nombre de recommandations à générer',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 50
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  count?: number;

  @ApiPropertyOptional({
    description: 'Type de recommandation',
    enum: RecommendationType,
    example: RecommendationType.HYBRID,
    default: RecommendationType.HYBRID
  })
  @IsOptional()
  @IsEnum(RecommendationType)
  type?: RecommendationType;

  @ApiPropertyOptional({
    description: 'Contexte pour les recommandations',
    example: {
      timeOfDay: 14,
      dayOfWeek: 2,
      season: 'spring',
      location: 'France'
    }
  })
  @IsOptional()
  @IsObject()
  context?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Filtres à appliquer',
    example: {
      priceRange: { min: 100, max: 500 },
      duration: { min: 3, max: 14 },
      category: 'meditation'
    }
  })
  @IsOptional()
  @IsObject()
  filters?: Record<string, any>;
}

// ===== ANOMALIES =====

export class AnomalyFilterDto {
  @ApiPropertyOptional({
    description: 'Type d\'anomalie à filtrer',
    example: 'performance',
    enum: ['performance', 'security', 'business', 'infrastructure']
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description: 'Niveau de sévérité minimum',
    example: 'medium',
    enum: ['low', 'medium', 'high', 'critical']
  })
  @IsOptional()
  @IsString()
  severity?: string;

  @ApiPropertyOptional({
    description: 'Période de recherche en heures',
    example: 24,
    minimum: 1,
    maximum: 168
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(168)
  periodHours?: number;
}

// ===== MLOPS =====

export class ModelRetrainDto {
  @ApiProperty({
    description: 'Nom du modèle à ré-entraîner',
    example: 'hanuman_recommendation_model'
  })
  @IsString()
  modelName: string;

  @ApiPropertyOptional({
    description: 'Paramètres d\'entraînement personnalisés',
    example: {
      epochs: 50,
      batchSize: 32,
      learningRate: 0.001
    }
  })
  @IsOptional()
  @IsObject()
  trainingParams?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Données d\'entraînement à utiliser',
    example: 'latest_30_days'
  })
  @IsOptional()
  @IsString()
  dataSource?: string;
}

export class ExperimentDto {
  @ApiProperty({
    description: 'Nom de l\'expérience',
    example: 'recommendation_model_v2'
  })
  @IsString()
  experimentName: string;

  @ApiProperty({
    description: 'Description de l\'expérience',
    example: 'Test d\'un nouveau modèle de recommandations avec attention mechanism'
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Paramètres de l\'expérience',
    example: {
      model_type: 'transformer',
      attention_heads: 8,
      hidden_size: 256
    }
  })
  @IsObject()
  parameters: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Tags pour l\'expérience',
    example: ['recommendation', 'transformer', 'v2']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

// ===== INNOVATION LAB =====

export class CreateNotebookDto {
  @ApiProperty({
    description: 'Nom du notebook',
    example: 'hanuman_sentiment_analysis'
  })
  @IsString()
  notebookName: string;

  @ApiProperty({
    description: 'Type de notebook',
    example: 'ml_experiment',
    enum: ['ml_experiment', 'data_analysis', 'prototype', 'research']
  })
  @IsEnum(['ml_experiment', 'data_analysis', 'prototype', 'research'])
  type: string;

  @ApiPropertyOptional({
    description: 'Template à utiliser',
    example: 'tensorflow_template'
  })
  @IsOptional()
  @IsString()
  template?: string;

  @ApiPropertyOptional({
    description: 'Description du notebook',
    example: 'Analyse de sentiment pour les avis utilisateurs'
  })
  @IsOptional()
  @IsString()
  description?: string;
}

export class PrototypeDto {
  @ApiProperty({
    description: 'Nom du prototype',
    example: 'voice_recommendation_assistant'
  })
  @IsString()
  prototypeName: string;

  @ApiProperty({
    description: 'Technologies utilisées',
    example: ['tensorflow', 'speech-to-text', 'nlp']
  })
  @IsArray()
  @IsString({ each: true })
  technologies: string[];

  @ApiProperty({
    description: 'Objectif du prototype',
    example: 'Assistant vocal pour recommandations de retraites'
  })
  @IsString()
  objective: string;

  @ApiPropertyOptional({
    description: 'Durée estimée en jours',
    example: 14,
    minimum: 1,
    maximum: 90
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(90)
  estimatedDays?: number;

  @ApiPropertyOptional({
    description: 'Équipe assignée',
    example: ['data_scientist_1', 'ml_engineer_1']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  assignedTeam?: string[];
}

// ===== RESPONSES =====

export class AIServiceResponse<T = any> {
  @ApiProperty({
    description: 'Statut de la réponse',
    example: true
  })
  success: boolean;

  @ApiPropertyOptional({
    description: 'Message de réponse'
  })
  message?: string;

  @ApiPropertyOptional({
    description: 'Données de réponse'
  })
  data?: T;

  @ApiPropertyOptional({
    description: 'Erreur si applicable'
  })
  error?: string;

  @ApiProperty({
    description: 'Timestamp de la réponse',
    example: '2025-05-29T10:30:00.000Z'
  })
  timestamp: string;
}

export class RecommendationResponse {
  @ApiProperty({
    description: 'Identifiant de l\'item recommandé',
    example: 'retreat_12345'
  })
  itemId: string;

  @ApiProperty({
    description: 'Score de recommandation (0-1)',
    example: 0.92,
    minimum: 0,
    maximum: 1
  })
  score: number;

  @ApiProperty({
    description: 'Niveau de confiance (0-1)',
    example: 0.85,
    minimum: 0,
    maximum: 1
  })
  confidence: number;

  @ApiProperty({
    description: 'Raisons de la recommandation',
    example: ['Utilisateurs similaires ont apprécié', 'Correspond à vos préférences']
  })
  reasoning: string[];

  @ApiProperty({
    description: 'Type de recommandation',
    enum: RecommendationType,
    example: RecommendationType.HYBRID
  })
  type: RecommendationType;

  @ApiProperty({
    description: 'Métadonnées supplémentaires',
    example: { category: 'meditation', similarity: 0.78 }
  })
  metadata: Record<string, any>;
}

export class AnomalyAlertResponse {
  @ApiProperty({
    description: 'Identifiant unique de l\'alerte',
    example: 'anomaly_001'
  })
  id: string;

  @ApiProperty({
    description: 'Type d\'anomalie',
    example: 'performance',
    enum: ['performance', 'security', 'business', 'infrastructure']
  })
  type: string;

  @ApiProperty({
    description: 'Niveau de sévérité',
    example: 'medium',
    enum: ['low', 'medium', 'high', 'critical']
  })
  severity: string;

  @ApiProperty({
    description: 'Titre de l\'alerte',
    example: 'Temps de réponse élevé détecté'
  })
  title: string;

  @ApiProperty({
    description: 'Description détaillée',
    example: 'Le temps de réponse moyen a dépassé le seuil de 500ms'
  })
  description: string;

  @ApiProperty({
    description: 'Niveau de confiance (0-1)',
    example: 0.85,
    minimum: 0,
    maximum: 1
  })
  confidence: number;

  @ApiProperty({
    description: 'Impact prédit',
    example: 'Dégradation de l\'expérience utilisateur'
  })
  predictedImpact: string;

  @ApiProperty({
    description: 'Actions suggérées',
    example: ['Vérifier la charge serveur', 'Optimiser les requêtes']
  })
  suggestedActions: string[];

  @ApiProperty({
    description: 'Timestamp de détection',
    example: '2025-05-29T10:30:00.000Z'
  })
  timestamp: string;
}
