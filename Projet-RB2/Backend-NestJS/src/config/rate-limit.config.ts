import { ThrottlerModuleOptions } from '@nestjs/throttler';

/**
 * 🚦 CONFIGURATION RATE LIMITING OPTIMISÉE
 * 
 * Stratégies de limitation de débit pour optimiser les performances
 * Fait partie des optimisations Sprint 16 - Performance
 */

/**
 * 📊 STRATÉGIES DE RATE LIMITING
 */
export const RATE_LIMIT_STRATEGIES = {
  // Stratégie courte - Protection contre les attaques rapides
  SHORT: {
    name: 'short',
    ttl: 1000, // 1 seconde
    limit: 10, // 10 requêtes par seconde
    blockDuration: 60000, // Blocage 1 minute
  },

  // Stratégie moyenne - Usage normal
  MEDIUM: {
    name: 'medium',
    ttl: 60000, // 1 minute
    limit: 100, // 100 requêtes par minute
    blockDuration: 300000, // Blocage 5 minutes
  },

  // Stratégie longue - Protection contre l'abus
  LONG: {
    name: 'long',
    ttl: 3600000, // 1 heure
    limit: 1000, // 1000 requêtes par heure
    blockDuration: 3600000, // Blocage 1 heure
  },
} as const;

/**
 * 🎯 CONFIGURATIONS PAR TYPE D'API
 */
export const API_RATE_LIMITS = {
  // API publique - Plus restrictive
  PUBLIC: {
    ttl: 60000, // 1 minute
    limit: 30, // 30 requêtes par minute
    skipIf: (context: any) => {
      // Skip pour les IPs whitelistées
      const whitelistedIPs = ['127.0.0.1', '::1'];
      const clientIP = context.getRequest().ip;
      return whitelistedIPs.includes(clientIP);
    },
  },

  // API authentifiée - Plus permissive
  AUTHENTICATED: {
    ttl: 60000, // 1 minute
    limit: 100, // 100 requêtes par minute
    skipIf: (context: any) => {
      // Skip pour les utilisateurs premium
      const user = context.getRequest().user;
      return user?.isPremium === true;
    },
  },

  // API admin - Très permissive
  ADMIN: {
    ttl: 60000, // 1 minute
    limit: 500, // 500 requêtes par minute
    skipIf: (context: any) => {
      // Skip pour les super admins
      const user = context.getRequest().user;
      return user?.role === 'super_admin';
    },
  },

  // Upload de fichiers - Très restrictive
  UPLOAD: {
    ttl: 300000, // 5 minutes
    limit: 10, // 10 uploads par 5 minutes
    skipIf: () => false, // Jamais skip
  },

  // Recherche - Modérément restrictive
  SEARCH: {
    ttl: 60000, // 1 minute
    limit: 50, // 50 recherches par minute
    skipIf: (context: any) => {
      // Skip pour les utilisateurs authentifiés
      const user = context.getRequest().user;
      return !!user;
    },
  },
} as const;

/**
 * 🔧 CONFIGURATION PRINCIPALE DU THROTTLER
 */
export const throttlerConfig: ThrottlerModuleOptions = {
  throttlers: [
    // Configuration par défaut
    {
      name: 'default',
      ttl: RATE_LIMIT_STRATEGIES.MEDIUM.ttl,
      limit: RATE_LIMIT_STRATEGIES.MEDIUM.limit,
    },
    // Configuration courte pour protection DDoS
    {
      name: 'short',
      ttl: RATE_LIMIT_STRATEGIES.SHORT.ttl,
      limit: RATE_LIMIT_STRATEGIES.SHORT.limit,
    },
    // Configuration longue pour abus
    {
      name: 'long',
      ttl: RATE_LIMIT_STRATEGIES.LONG.ttl,
      limit: RATE_LIMIT_STRATEGIES.LONG.limit,
    },
  ],
  
  // Storage en mémoire (Redis recommandé en production)
  storage: undefined, // Utilise MemoryStorage par défaut
  
  // Ignorer les erreurs de storage
  ignoreUserAgents: [
    /googlebot/gi,
    /bingbot/gi,
    /slurp/gi,
    /duckduckbot/gi,
  ],
  
  // Skip les requêtes selon certaines conditions
  skipIf: (context: any) => {
    const request = context.getRequest();
    
    // Skip pour les health checks
    if (request.url === '/health' || request.url === '/metrics') {
      return true;
    }
    
    // Skip pour les IPs locales en développement
    if (process.env.NODE_ENV === 'development') {
      const localIPs = ['127.0.0.1', '::1', 'localhost'];
      return localIPs.includes(request.ip);
    }
    
    return false;
  },
};

/**
 * 📈 CONFIGURATION REDIS POUR RATE LIMITING
 */
export const redisRateLimitConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_RATE_LIMIT_DB || '1'),
  keyPrefix: 'rate_limit:',
  
  // Configuration de connexion
  connectTimeout: 5000,
  lazyConnect: true,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  
  // Configuration de pool
  family: 4,
  keepAlive: true,
  
  // Configuration de cluster (si applicable)
  enableReadyCheck: true,
  maxRetriesPerRequest: null,
};

/**
 * 🎯 DECORATEURS PERSONNALISÉS POUR RATE LIMITING
 */
export const RATE_LIMIT_DECORATORS = {
  // Décorateur pour API publique
  PublicAPI: () => ({
    throttlers: [{ name: 'public', ...API_RATE_LIMITS.PUBLIC }],
  }),
  
  // Décorateur pour API authentifiée
  AuthenticatedAPI: () => ({
    throttlers: [{ name: 'authenticated', ...API_RATE_LIMITS.AUTHENTICATED }],
  }),
  
  // Décorateur pour API admin
  AdminAPI: () => ({
    throttlers: [{ name: 'admin', ...API_RATE_LIMITS.ADMIN }],
  }),
  
  // Décorateur pour uploads
  UploadAPI: () => ({
    throttlers: [{ name: 'upload', ...API_RATE_LIMITS.UPLOAD }],
  }),
  
  // Décorateur pour recherche
  SearchAPI: () => ({
    throttlers: [{ name: 'search', ...API_RATE_LIMITS.SEARCH }],
  }),
} as const;

/**
 * 📊 MÉTRIQUES DE RATE LIMITING
 */
export class RateLimitMetrics {
  private static metrics: Map<string, {
    requests: number;
    blocked: number;
    lastReset: Date;
  }> = new Map();

  /**
   * Enregistre une requête
   */
  static recordRequest(key: string, blocked: boolean = false): void {
    const metric = this.metrics.get(key) || {
      requests: 0,
      blocked: 0,
      lastReset: new Date(),
    };

    metric.requests++;
    if (blocked) {
      metric.blocked++;
    }

    this.metrics.set(key, metric);
  }

  /**
   * Obtient les métriques pour une clé
   */
  static getMetrics(key: string): {
    requests: number;
    blocked: number;
    blockRate: number;
    lastReset: Date;
  } | null {
    const metric = this.metrics.get(key);
    if (!metric) return null;

    return {
      ...metric,
      blockRate: metric.requests > 0 ? (metric.blocked / metric.requests) * 100 : 0,
    };
  }

  /**
   * Obtient toutes les métriques
   */
  static getAllMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [key, metric] of this.metrics.entries()) {
      result[key] = {
        ...metric,
        blockRate: metric.requests > 0 ? (metric.blocked / metric.requests) * 100 : 0,
      };
    }
    
    return result;
  }

  /**
   * Réinitialise les métriques
   */
  static resetMetrics(): void {
    this.metrics.clear();
  }

  /**
   * Génère un rapport de rate limiting
   */
  static generateReport(): {
    totalRequests: number;
    totalBlocked: number;
    overallBlockRate: number;
    topBlockedEndpoints: Array<{ endpoint: string; blockRate: number; requests: number }>;
  } {
    let totalRequests = 0;
    let totalBlocked = 0;
    const endpoints: Array<{ endpoint: string; blockRate: number; requests: number }> = [];

    for (const [key, metric] of this.metrics.entries()) {
      totalRequests += metric.requests;
      totalBlocked += metric.blocked;
      
      const blockRate = metric.requests > 0 ? (metric.blocked / metric.requests) * 100 : 0;
      endpoints.push({
        endpoint: key,
        blockRate,
        requests: metric.requests,
      });
    }

    // Trier par taux de blocage décroissant
    endpoints.sort((a, b) => b.blockRate - a.blockRate);

    return {
      totalRequests,
      totalBlocked,
      overallBlockRate: totalRequests > 0 ? (totalBlocked / totalRequests) * 100 : 0,
      topBlockedEndpoints: endpoints.slice(0, 10), // Top 10
    };
  }
}

/**
 * 🔧 UTILITAIRES POUR RATE LIMITING
 */
export class RateLimitUtils {
  /**
   * Génère une clé de rate limiting basée sur l'IP et l'endpoint
   */
  static generateKey(ip: string, endpoint: string, userId?: string): string {
    const baseKey = `${ip}:${endpoint}`;
    return userId ? `${baseKey}:${userId}` : baseKey;
  }

  /**
   * Calcule le temps restant avant reset
   */
  static getTimeUntilReset(ttl: number, lastRequest: Date): number {
    const now = new Date().getTime();
    const lastRequestTime = lastRequest.getTime();
    const timeElapsed = now - lastRequestTime;
    return Math.max(0, ttl - timeElapsed);
  }

  /**
   * Formate le temps en format lisible
   */
  static formatTime(milliseconds: number): string {
    if (milliseconds < 1000) return `${milliseconds}ms`;
    if (milliseconds < 60000) return `${Math.round(milliseconds / 1000)}s`;
    if (milliseconds < 3600000) return `${Math.round(milliseconds / 60000)}m`;
    return `${Math.round(milliseconds / 3600000)}h`;
  }

  /**
   * Détermine si une IP est suspecte
   */
  static isSuspiciousIP(ip: string, requestCount: number, timeWindow: number): boolean {
    // Logique simple de détection d'abus
    const requestsPerSecond = requestCount / (timeWindow / 1000);
    return requestsPerSecond > 50; // Plus de 50 req/sec est suspect
  }
}
