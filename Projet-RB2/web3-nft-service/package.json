{"name": "web3-nft-service", "version": "1.0.0", "description": "Web3 and NFT service for Retreat And Be platform", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@ethersproject/contracts": "^5.8.0", "@ethersproject/providers": "^5.8.0", "@ethersproject/units": "^5.8.0", "@mui/icons-material": "^5.14.20", "@mui/material": "^5.14.20", "@orbs-network/ton-access": "^2.3.3", "@tanstack/react-query": "^4.36.1", "@ton/core": "^0.56.0", "@ton/crypto": "^3.3.0", "@ton/ton": "^13.11.2", "@wagmi/core": "^1.4.10", "@web3-react/core": "^6.1.9", "@web3-react/injected-connector": "^6.0.7", "@web3-react/walletconnect-connector": "^6.2.13", "@web3-react/walletlink-connector": "^6.2.14", "@web3modal/ethereum": "^2.7.1", "@web3modal/react": "^2.7.1", "axios": "^1.6.2", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "ethers": "^5.7.2", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ipfs-http-client": "^60.0.0", "morgan": "^1.10.0", "notistack": "^3.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-router-dom": "^6.20.1", "supertest": "^6.3.3", "viem": "^1.19.11", "wagmi": "^1.4.10", "web3": "^4.2.2", "winston": "^3.11.0", "@retreat-and-be/design-system": "file:../design-system", "class-variance-authority": "^0.7.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/morgan": "^1.9.9", "@types/node": "^20.10.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3", "vite": "^5.0.8"}, "scripts": {"dev": "vite", "start": "node dist/server.js", "server": "nodemon src/server.ts", "build": "tsc && vite build", "preview": "vite preview"}}