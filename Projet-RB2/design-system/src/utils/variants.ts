/**
 * Variant utilities for Design System components
 */

export { cva, type VariantProps } from 'class-variance-authority';

// Common variant configurations
export const commonVariants = {
  size: {
    sm: 'text-sm',
    default: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
  },
  variant: {
    default: 'bg-primary text-primary-foreground',
    secondary: 'bg-secondary text-secondary-foreground',
    outline: 'border border-input bg-background',
    ghost: 'bg-transparent',
  },
} as const;
