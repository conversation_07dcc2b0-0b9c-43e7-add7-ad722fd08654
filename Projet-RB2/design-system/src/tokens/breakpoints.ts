/**
 * Design Tokens - Breakpoints
 * Retreat And Be Design System
 */

export const breakpoints = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

export const screens = {
  xs: { max: '474px' },
  sm: { min: '640px' },
  md: { min: '768px' },
  lg: { min: '1024px' },
  xl: { min: '1280px' },
  '2xl': { min: '1536px' },
} as const;

export type Breakpoint = keyof typeof breakpoints;
