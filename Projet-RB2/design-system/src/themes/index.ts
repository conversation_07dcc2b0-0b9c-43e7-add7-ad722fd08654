/**
 * Design System Themes
 * Retreat And Be Design System
 */

import { colors } from '../tokens/colors';

export const lightTheme = {
  colors: {
    background: colors.neutral[50],
    foreground: colors.neutral[900],
    card: colors.neutral[50],
    'card-foreground': colors.neutral[900],
    popover: colors.neutral[50],
    'popover-foreground': colors.neutral[900],
    primary: colors.primary[500],
    'primary-foreground': colors.neutral[50],
    secondary: colors.secondary[100],
    'secondary-foreground': colors.secondary[900],
    muted: colors.secondary[100],
    'muted-foreground': colors.secondary[500],
    accent: colors.accent[100],
    'accent-foreground': colors.accent[900],
    destructive: colors.error[500],
    'destructive-foreground': colors.neutral[50],
    border: colors.secondary[200],
    input: colors.secondary[200],
    ring: colors.primary[500],
  },
} as const;

export const darkTheme = {
  colors: {
    background: colors.neutral[950],
    foreground: colors.neutral[50],
    card: colors.neutral[950],
    'card-foreground': colors.neutral[50],
    popover: colors.neutral[950],
    'popover-foreground': colors.neutral[50],
    primary: colors.primary[400],
    'primary-foreground': colors.neutral[900],
    secondary: colors.secondary[800],
    'secondary-foreground': colors.secondary[50],
    muted: colors.secondary[800],
    'muted-foreground': colors.secondary[400],
    accent: colors.accent[800],
    'accent-foreground': colors.accent[50],
    destructive: colors.error[400],
    'destructive-foreground': colors.neutral[50],
    border: colors.secondary[800],
    input: colors.secondary[800],
    ring: colors.primary[400],
  },
} as const;

export const retreatTheme = {
  colors: {
    ...lightTheme.colors,
    primary: colors.retreat.nature,
    'primary-foreground': colors.neutral[50],
    accent: colors.retreat.wellness,
    'accent-foreground': colors.neutral[50],
  },
} as const;

export type Theme = typeof lightTheme;
export type ThemeName = 'light' | 'dark' | 'retreat';

export const themes = {
  light: lightTheme,
  dark: darkTheme,
  retreat: retreatTheme,
} as const;
