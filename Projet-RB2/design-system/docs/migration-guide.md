# Guide de Migration vers le Design System

## 🎯 Objectif
Migrer tous les composants UI existants vers le Design System unifié Retreat And Be.

## 📋 Étapes de Migration

### 1. Installation
Le Design System est déjà installé dans votre service via:
```json
"@retreat-and-be/design-system": "file:../design-system"
```

### 2. Import des Composants
```typescript
import { Button, Card, CardHeader, CardTitle, CardContent } from '@retreat-and-be/design-system';
```

### 3. Remplacement des Composants Existants

#### Avant (Composant personnalisé)
```typescript
import { CustomButton } from './components/CustomButton';

function MyComponent() {
  return <CustomButton variant="primary">Click me</CustomButton>;
}
```

#### Après (Design System)
```typescript
import { Button } from '@retreat-and-be/design-system';

function MyComponent() {
  return <Button variant="default">Click me</Button>;
}
```

## 🎨 Composants Disponibles

### Button
- Variants: default, destructive, outline, secondary, ghost, link, success, warning
- Sizes: default, sm, lg, xl, icon
- Props: loading, leftIcon, rightIcon

### Card
- Variants: default, elevated, outlined, ghost
- Padding: none, sm, default, lg
- Sous-composants: CardHeader, CardTitle, CardDescription, CardContent, CardFooter

## 🔧 Tokens Disponibles

### Couleurs
```typescript
import { colors } from '@retreat-and-be/design-system';
// colors.primary[500], colors.secondary[100], etc.
```

### Typographie
```typescript
import { typography } from '@retreat-and-be/design-system';
// typography.fontSize.lg, typography.fontWeight.bold, etc.
```

## 📝 Checklist de Migration

- [ ] Identifier tous les composants UI existants
- [ ] Mapper les composants vers le Design System
- [ ] Remplacer les imports
- [ ] Tester les composants migrés
- [ ] Supprimer les anciens composants
- [ ] Mettre à jour la documentation

## 🚨 Points d'Attention

1. **Variants**: Vérifier la correspondance des variants
2. **Props**: Adapter les props aux nouvelles interfaces
3. **Styles**: Utiliser les tokens du Design System
4. **Tests**: Mettre à jour les tests unitaires
