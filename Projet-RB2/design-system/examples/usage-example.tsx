import React from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>ontent, 
  CardFooter 
} from '@retreat-and-be/design-system';

export function ExampleUsage() {
  return (
    <div className="p-6 space-y-6">
      {/* Buttons */}
      <div className="space-x-4">
        <Button variant="default">Primary</Button>
        <Button variant="secondary">Secondary</Button>
        <Button variant="outline">Outline</Button>
        <Button variant="ghost">Ghost</Button>
        <Button variant="success">Success</Button>
        <Button variant="warning">Warning</Button>
        <Button variant="destructive">Destructive</Button>
      </div>

      {/* Card Example */}
      <Card className="w-96">
        <CardHeader>
          <CardTitle>Retreat Card</CardTitle>
        </CardHeader>
        <CardContent>
          <p>This is an example of using the Design System Card component.</p>
        </CardContent>
        <CardFooter>
          <Button variant="default">Book Now</Button>
        </CardFooter>
      </Card>

      {/* Loading Button */}
      <Button loading>Loading...</Button>

      {/* Button with Icons */}
      <Button 
        leftIcon={<span>🏠</span>}
        rightIcon={<span>→</span>}
      >
        Go Home
      </Button>
    </div>
  );
}