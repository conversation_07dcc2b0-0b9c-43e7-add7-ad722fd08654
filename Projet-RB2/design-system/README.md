# Retreat And Be Design System

## 🎨 Vue d'ensemble
Design System unifié pour tous les microservices de la plateforme Retreat And Be.

## 📦 Installation
```bash
npm install @retreat-and-be/design-system
```

## 🚀 Usage Rapide
```typescript
import { But<PERSON>, <PERSON> } from '@retreat-and-be/design-system';

function App() {
  return (
    <Card>
      <Button variant="default">Hello World</Button>
    </Card>
  );
}
```

## 📚 Documentation
- [Guide de Migration](./migration-guide.md)
- [Exemples d'Usage](../examples/usage-example.tsx)

## 🎯 Objectifs
- Cohérence visuelle entre tous les services
- Réutilisabilité des composants
- Maintenabilité simplifiée
- Performance optimisée

## 🔧 Développement
```bash
# Build
npm run build

# Development
npm run dev

# Type checking
npm run type-check
```

## 📊 Statut d'Intégration
- ✅ Front-Audrey-V1-Main-main
- ✅ Agent IA
- ✅ Backend-NestJS
- ✅ Financial-Management
- ✅ Hotel-Booking
- ✅ Education
- ✅ Marketplace
- ✅ web3-nft-service
- ⚠️ Analyzer (conflits de dépendances)
- ❌ Social-Platform (service manquant)
