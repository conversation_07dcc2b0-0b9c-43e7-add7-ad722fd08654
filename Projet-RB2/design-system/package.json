{"name": "@retreat-and-be/design-system", "version": "1.0.0", "description": "Design System unifié pour tous les microservices Retreat And Be", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"build": "tsc", "dev": "tsc --watch", "type-check": "tsc --noEmit"}, "keywords": ["design-system", "react", "typescript", "retreat-and-be"], "author": "Retreat And Be Team", "license": "MIT", "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "dependencies": {"clsx": "^2.1.1", "class-variance-authority": "^0.7.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0"}}