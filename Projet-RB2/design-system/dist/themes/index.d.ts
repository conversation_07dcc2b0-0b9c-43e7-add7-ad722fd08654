/**
 * Design System Themes
 * Retreat And Be Design System
 */
export declare const lightTheme: {
    readonly colors: {
        readonly background: "#fafafa";
        readonly foreground: "#171717";
        readonly card: "#fafafa";
        readonly 'card-foreground': "#171717";
        readonly popover: "#fafafa";
        readonly 'popover-foreground': "#171717";
        readonly primary: "#0ea5e9";
        readonly 'primary-foreground': "#fafafa";
        readonly secondary: "#f1f5f9";
        readonly 'secondary-foreground': "#0f172a";
        readonly muted: "#f1f5f9";
        readonly 'muted-foreground': "#64748b";
        readonly accent: "#fae8ff";
        readonly 'accent-foreground': "#701a75";
        readonly destructive: "#ef4444";
        readonly 'destructive-foreground': "#fafafa";
        readonly border: "#e2e8f0";
        readonly input: "#e2e8f0";
        readonly ring: "#0ea5e9";
    };
};
export declare const darkTheme: {
    readonly colors: {
        readonly background: "#0a0a0a";
        readonly foreground: "#fafafa";
        readonly card: "#0a0a0a";
        readonly 'card-foreground': "#fafafa";
        readonly popover: "#0a0a0a";
        readonly 'popover-foreground': "#fafafa";
        readonly primary: "#38bdf8";
        readonly 'primary-foreground': "#171717";
        readonly secondary: "#1e293b";
        readonly 'secondary-foreground': "#f8fafc";
        readonly muted: "#1e293b";
        readonly 'muted-foreground': "#94a3b8";
        readonly accent: "#86198f";
        readonly 'accent-foreground': "#fdf4ff";
        readonly destructive: "#f87171";
        readonly 'destructive-foreground': "#fafafa";
        readonly border: "#1e293b";
        readonly input: "#1e293b";
        readonly ring: "#38bdf8";
    };
};
export declare const retreatTheme: {
    readonly colors: {
        readonly primary: "#22c55e";
        readonly 'primary-foreground': "#fafafa";
        readonly accent: "#8b5cf6";
        readonly 'accent-foreground': "#fafafa";
        readonly background: "#fafafa";
        readonly foreground: "#171717";
        readonly card: "#fafafa";
        readonly 'card-foreground': "#171717";
        readonly popover: "#fafafa";
        readonly 'popover-foreground': "#171717";
        readonly secondary: "#f1f5f9";
        readonly 'secondary-foreground': "#0f172a";
        readonly muted: "#f1f5f9";
        readonly 'muted-foreground': "#64748b";
        readonly destructive: "#ef4444";
        readonly 'destructive-foreground': "#fafafa";
        readonly border: "#e2e8f0";
        readonly input: "#e2e8f0";
        readonly ring: "#0ea5e9";
    };
};
export type Theme = typeof lightTheme;
export type ThemeName = 'light' | 'dark' | 'retreat';
export declare const themes: {
    readonly light: {
        readonly colors: {
            readonly background: "#fafafa";
            readonly foreground: "#171717";
            readonly card: "#fafafa";
            readonly 'card-foreground': "#171717";
            readonly popover: "#fafafa";
            readonly 'popover-foreground': "#171717";
            readonly primary: "#0ea5e9";
            readonly 'primary-foreground': "#fafafa";
            readonly secondary: "#f1f5f9";
            readonly 'secondary-foreground': "#0f172a";
            readonly muted: "#f1f5f9";
            readonly 'muted-foreground': "#64748b";
            readonly accent: "#fae8ff";
            readonly 'accent-foreground': "#701a75";
            readonly destructive: "#ef4444";
            readonly 'destructive-foreground': "#fafafa";
            readonly border: "#e2e8f0";
            readonly input: "#e2e8f0";
            readonly ring: "#0ea5e9";
        };
    };
    readonly dark: {
        readonly colors: {
            readonly background: "#0a0a0a";
            readonly foreground: "#fafafa";
            readonly card: "#0a0a0a";
            readonly 'card-foreground': "#fafafa";
            readonly popover: "#0a0a0a";
            readonly 'popover-foreground': "#fafafa";
            readonly primary: "#38bdf8";
            readonly 'primary-foreground': "#171717";
            readonly secondary: "#1e293b";
            readonly 'secondary-foreground': "#f8fafc";
            readonly muted: "#1e293b";
            readonly 'muted-foreground': "#94a3b8";
            readonly accent: "#86198f";
            readonly 'accent-foreground': "#fdf4ff";
            readonly destructive: "#f87171";
            readonly 'destructive-foreground': "#fafafa";
            readonly border: "#1e293b";
            readonly input: "#1e293b";
            readonly ring: "#38bdf8";
        };
    };
    readonly retreat: {
        readonly colors: {
            readonly primary: "#22c55e";
            readonly 'primary-foreground': "#fafafa";
            readonly accent: "#8b5cf6";
            readonly 'accent-foreground': "#fafafa";
            readonly background: "#fafafa";
            readonly foreground: "#171717";
            readonly card: "#fafafa";
            readonly 'card-foreground': "#171717";
            readonly popover: "#fafafa";
            readonly 'popover-foreground': "#171717";
            readonly secondary: "#f1f5f9";
            readonly 'secondary-foreground': "#0f172a";
            readonly muted: "#f1f5f9";
            readonly 'muted-foreground': "#64748b";
            readonly destructive: "#ef4444";
            readonly 'destructive-foreground': "#fafafa";
            readonly border: "#e2e8f0";
            readonly input: "#e2e8f0";
            readonly ring: "#0ea5e9";
        };
    };
};
//# sourceMappingURL=index.d.ts.map