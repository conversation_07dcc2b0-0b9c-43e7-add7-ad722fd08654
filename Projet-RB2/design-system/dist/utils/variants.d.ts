/**
 * Variant utilities for Design System components
 */
export { cva, type VariantProps } from 'class-variance-authority';
export declare const commonVariants: {
    readonly size: {
        readonly sm: "text-sm";
        readonly default: "text-base";
        readonly lg: "text-lg";
        readonly xl: "text-xl";
    };
    readonly variant: {
        readonly default: "bg-primary text-primary-foreground";
        readonly secondary: "bg-secondary text-secondary-foreground";
        readonly outline: "border border-input bg-background";
        readonly ghost: "bg-transparent";
    };
};
//# sourceMappingURL=variants.d.ts.map