/**
 * Design Tokens - Breakpoints
 * Retreat And Be Design System
 */
export declare const breakpoints: {
    readonly xs: "475px";
    readonly sm: "640px";
    readonly md: "768px";
    readonly lg: "1024px";
    readonly xl: "1280px";
    readonly '2xl': "1536px";
};
export declare const screens: {
    readonly xs: {
        readonly max: "474px";
    };
    readonly sm: {
        readonly min: "640px";
    };
    readonly md: {
        readonly min: "768px";
    };
    readonly lg: {
        readonly min: "1024px";
    };
    readonly xl: {
        readonly min: "1280px";
    };
    readonly '2xl': {
        readonly min: "1536px";
    };
};
export type Breakpoint = keyof typeof breakpoints;
//# sourceMappingURL=breakpoints.d.ts.map