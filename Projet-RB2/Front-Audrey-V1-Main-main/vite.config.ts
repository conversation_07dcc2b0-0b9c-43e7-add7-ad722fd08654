import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// Configuration optimisée pour performance - Sprint 16
export default defineConfig({
  plugins: [
    react({
      // Optimisation React
      babel: {
        plugins: [
          // Suppression des PropTypes en production
          ['babel-plugin-transform-remove-console', { exclude: ['error', 'warn'] }]
        ]
      }
    })
  ],
  
  // Optimisation du build
  build: {
    // Taille de chunk optimale
    chunkSizeWarningLimit: 500,
    
    // Minification avancée
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log']
      }
    },
    
    // Code splitting optimisé
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['@headlessui/react', '@heroicons/react'],
          utils: ['lodash', 'date-fns', 'clsx']
        }
      }
    },
    
    // Optimisation des assets
    assetsInlineLimit: 4096,
    
    // Source maps pour production
    sourcemap: false
  },
  
  // Optimisation du serveur de dev
  server: {
    hmr: {
      overlay: false
    }
  },
  
  // Résolution des modules
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@pages': resolve(__dirname, 'src/pages'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  },
  
  // Optimisation CSS
  css: {
    devSourcemap: false,
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    }
  }
});
