// Exemple d'utilisation du Design System - Sprint 15
import React from 'react';
import { Button, Input } from '@retreatandbe/design-system';

export const DesignSystemExample: React.FC = () => {
  return (
    <div className="p-6 space-y-4">
      <h2 className="text-2xl font-bold text-neutral-900">Design System - Sprint 15</h2>
      
      <div className="space-y-4">
        <Button variant="primary" size="md">
          Bouton Principal
        </Button>
        
        <Button variant="secondary" size="md">
          Bouton Secondaire
        </Button>
        
        <Button variant="outline" size="md">
          Bouton Outline
        </Button>
        
        <Input
          label="Nom d'utilisateur"
          placeholder="Entrez votre nom"
          helperText="Utilisé pour votre profil"
        />
        
        <Input
          label="Email"
          type="email"
          placeholder="<EMAIL>"
          error="Format d'email invalide"
        />
      </div>
    </div>
  );
};
