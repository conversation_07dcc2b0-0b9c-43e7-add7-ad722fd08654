import React, { Suspense, ComponentType } from 'react';

// Utilitaire de lazy loading avec fallback - Sprint 16
interface LazyLoadOptions {
  fallback?: React.ReactNode;
  delay?: number;
}

export const lazyLoad = <T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
) => {
  const LazyComponent = React.lazy(importFunc);
  
  return (props: React.ComponentProps<T>) => (
    <Suspense 
      fallback={
        options.fallback || (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        )
      }
    >
      <LazyComponent {...props} />
    </Suspense>
  );
};

// Hook pour lazy loading conditionnel
export const useLazyLoad = (condition: boolean, importFunc: () => Promise<any>) => {
  const [Component, setComponent] = React.useState<ComponentType | null>(null);
  
  React.useEffect(() => {
    if (condition && !Component) {
      importFunc().then(module => {
        setComponent(() => module.default);
      });
    }
  }, [condition, Component, importFunc]);
  
  return Component;
};
