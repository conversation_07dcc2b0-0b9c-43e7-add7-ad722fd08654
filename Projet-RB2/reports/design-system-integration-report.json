{"sprint": "Sprint 15 - Intégration Design System", "date": "2025-05-28T17:35:35.784Z", "summary": {"totalServices": 10, "integratedServices": 8, "failedServices": 1, "missingServices": 1}, "results": {"Front-Audrey-V1-Main-main": {"name": "Front-Audrey-V1-Main-main", "path": "/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/Projet-RB2/Front-Audrey-V1-Main-main", "exists": true, "integrated": true, "hasPackageJson": true, "errors": []}, "Agent IA": {"name": "Agent IA", "path": "/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/Projet-RB2/Agent IA", "exists": true, "integrated": true, "hasPackageJson": true, "errors": []}, "Backend-NestJS": {"name": "Backend-NestJS", "path": "/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/Projet-RB2/Backend-NestJS", "exists": true, "integrated": true, "hasPackageJson": true, "errors": []}, "Analyzer": {"name": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/Projet-RB2/Analyzer", "exists": true, "integrated": false, "hasPackageJson": true, "errors": ["Command failed: npm install\nnpm error code ERESOLVE\nnpm error ERESOLVE could not resolve\nnpm error\nnpm error While resolving: @nestjs/core@11.0.10\nnpm error Found: @nestjs/microservices@9.4.3\nnpm error node_modules/@nestjs/microservices\nnpm error   @nestjs/microservices@\"^9.0.0\" from the root project\nnpm error\nnpm error Could not resolve dependency:\nnpm error peerOptional @nestjs/microservices@\"^11.0.0\" from @nestjs/core@11.0.10\nnpm error node_modules/@nestjs/core\nnpm error   dev @nestjs/core@\"^11.0.10\" from the root project\nnpm error   peer @nestjs/core@\"^11.0.0\" from @nestjs/testing@11.0.10\nnpm error   node_modules/@nestjs/testing\nnpm error     dev @nestjs/testing@\"^11.0.10\" from the root project\nnpm error\nnpm error Conflicting peer dependency: @nestjs/microservices@11.1.2\nnpm error node_modules/@nestjs/microservices\nnpm error   peerOptional @nestjs/microservices@\"^11.0.0\" from @nestjs/core@11.0.10\nnpm error   node_modules/@nestjs/core\nnpm error     dev @nestjs/core@\"^11.0.10\" from the root project\nnpm error     peer @nestjs/core@\"^11.0.0\" from @nestjs/testing@11.0.10\nnpm error     node_modules/@nestjs/testing\nnpm error       dev @nestjs/testing@\"^11.0.10\" from the root project\nnpm error\nnpm error Fix the upstream dependency conflict, or retry\nnpm error this command with --force or --legacy-peer-deps\nnpm error to accept an incorrect (and potentially broken) dependency resolution.\nnpm error\nnpm error\nnpm error For a full report see:\nnpm error /Users/<USER>/.npm/_logs/2025-05-28T17_33_08_615Z-eresolve-report.txt\nnpm error A complete log of this run can be found in: /Users/<USER>/.npm/_logs/2025-05-28T17_33_08_615Z-debug-0.log\n"]}, "Financial-Management": {"name": "Financial-Management", "path": "/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/Projet-RB2/Financial-Management", "exists": true, "integrated": true, "hasPackageJson": true, "errors": []}, "Hotel-Booking": {"name": "Hotel-Booking", "path": "/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/Projet-RB2/Hotel-Booking", "exists": true, "integrated": true, "hasPackageJson": true, "errors": []}, "Education": {"name": "Education", "path": "/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/Projet-RB2/Education", "exists": true, "integrated": true, "hasPackageJson": true, "errors": []}, "Marketplace": {"name": "Marketplace", "path": "/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/Projet-RB2/Marketplace", "exists": true, "integrated": true, "hasPackageJson": true, "errors": []}, "Social-Platform": {"name": "Social-Platform", "path": "/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/Projet-RB2/Social-Platform", "exists": false, "integrated": false, "hasPackageJson": false, "errors": ["Service non trouvé"]}, "web3-nft-service": {"name": "web3-nft-service", "path": "/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/Projet-RB2/web3-nft-service", "exists": true, "integrated": true, "hasPackageJson": true, "errors": []}}, "nextSteps": [{"priority": "high", "action": "Migrer les composants UI existants vers le Design System", "services": ["Front-Audrey-V1-Main-main", "Agent IA", "Backend-NestJS", "Financial-Management", "Hotel-Booking", "Education", "Marketplace", "web3-nft-service"], "estimatedDays": 4}, {"priority": "medium", "action": "Résoudre les erreurs d'intégration", "services": ["<PERSON><PERSON><PERSON>"], "estimatedDays": 0.25}, {"priority": "medium", "action": "Créer la documentation d'usage du Design System", "estimatedDays": 1}, {"priority": "low", "action": "Configurer Storybook pour la documentation des composants", "estimatedDays": 0.5}]}