#!/usr/bin/env node

/**
 * Script d'intégration du Design System
 * Automatise l'installation et la configuration du Design System dans tous les microservices
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class DesignSystemIntegrator {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.designSystemPackage = '@retreat-and-be/design-system';
    this.microservices = [
      'Front-Audrey-V1-Main-main',
      'Agent IA',
      'Backend-NestJS',
      'Analyzer',
      'Financial-Management',
      'Hotel-Booking',
      'Education',
      'Marketplace',
      'Social-Platform',
      'web3-nft-service'
    ];
    this.integrationResults = {};
  }

  async start() {
    console.log('🎨 INTÉGRATION DESIGN SYSTEM - SPRINT 15');
    console.log('=========================================');
    console.log('Intégration automatique du Design System dans tous les microservices');
    console.log('');

    try {
      // 1. Build du Design System
      await this.buildDesignSystem();

      // 2. Intégration dans chaque microservice
      await this.integrateIntoServices();

      // 3. Génération des rapports
      await this.generateIntegrationReport();

      console.log('\n✅ INTÉGRATION DESIGN SYSTEM TERMINÉE AVEC SUCCÈS');
      this.displaySummary();

    } catch (error) {
      console.error('\n❌ Erreur lors de l\'intégration:', error.message);
      process.exit(1);
    }
  }

  async buildDesignSystem() {
    console.log('🔨 Build du Design System...');
    
    const designSystemPath = path.join(this.projectRoot, 'design-system');
    
    if (!fs.existsSync(designSystemPath)) {
      throw new Error('Design System non trouvé. Veuillez d\'abord créer le package.');
    }

    try {
      // Installation des dépendances
      console.log('  📦 Installation des dépendances...');
      execSync('npm install', { 
        cwd: designSystemPath, 
        stdio: 'pipe' 
      });

      // Build du package
      console.log('  🔨 Build du package...');
      execSync('npm run build', { 
        cwd: designSystemPath, 
        stdio: 'pipe' 
      });

      console.log('  ✅ Design System buildé avec succès');
    } catch (error) {
      throw new Error(`Erreur lors du build du Design System: ${error.message}`);
    }
  }

  async integrateIntoServices() {
    console.log('\n🔗 Intégration dans les microservices...');

    for (const serviceName of this.microservices) {
      console.log(`\n📋 Intégration: ${serviceName}`);
      
      const servicePath = path.resolve(this.projectRoot, serviceName);
      const result = {
        name: serviceName,
        path: servicePath,
        exists: fs.existsSync(servicePath),
        integrated: false,
        hasPackageJson: false,
        errors: []
      };

      if (!result.exists) {
        console.log(`  ❌ Service non trouvé: ${servicePath}`);
        result.errors.push('Service non trouvé');
        this.integrationResults[serviceName] = result;
        continue;
      }

      const packageJsonPath = path.join(servicePath, 'package.json');
      result.hasPackageJson = fs.existsSync(packageJsonPath);

      if (!result.hasPackageJson) {
        console.log(`  ⚠️  Pas de package.json trouvé, service ignoré`);
        result.errors.push('Pas de package.json');
        this.integrationResults[serviceName] = result;
        continue;
      }

      try {
        // Lire le package.json existant
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

        // Ajouter le Design System aux dépendances
        if (!packageJson.dependencies) {
          packageJson.dependencies = {};
        }

        // Utiliser le chemin local pour le développement
        const designSystemPath = path.relative(servicePath, path.join(this.projectRoot, 'design-system'));
        packageJson.dependencies[this.designSystemPackage] = `file:${designSystemPath}`;

        // Ajouter les dépendances peer si nécessaire
        if (!packageJson.dependencies['class-variance-authority']) {
          packageJson.dependencies['class-variance-authority'] = '^0.7.0';
        }

        // Sauvegarder le package.json modifié
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

        // Installer les dépendances
        console.log(`  📦 Installation des dépendances...`);
        execSync('npm install', { 
          cwd: servicePath, 
          stdio: 'pipe' 
        });

        result.integrated = true;
        console.log(`  ✅ Intégration réussie`);

      } catch (error) {
        console.log(`  ❌ Erreur d'intégration: ${error.message}`);
        result.errors.push(error.message);
      }

      this.integrationResults[serviceName] = result;
    }
  }

  async generateIntegrationReport() {
    console.log('\n📊 Génération du rapport d\'intégration...');

    const reportsDir = path.join(this.projectRoot, 'reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    const report = {
      sprint: 'Sprint 15 - Intégration Design System',
      date: new Date().toISOString(),
      summary: {
        totalServices: this.microservices.length,
        integratedServices: Object.values(this.integrationResults).filter(r => r.integrated).length,
        failedServices: Object.values(this.integrationResults).filter(r => !r.integrated && r.exists).length,
        missingServices: Object.values(this.integrationResults).filter(r => !r.exists).length
      },
      results: this.integrationResults,
      nextSteps: this.generateNextSteps()
    };

    fs.writeFileSync(
      path.join(reportsDir, 'design-system-integration-report.json'),
      JSON.stringify(report, null, 2)
    );

    console.log('  ✅ Rapport généré: reports/design-system-integration-report.json');
  }

  generateNextSteps() {
    const integratedServices = Object.values(this.integrationResults).filter(r => r.integrated);
    const failedServices = Object.values(this.integrationResults).filter(r => !r.integrated && r.exists);

    const steps = [];

    if (integratedServices.length > 0) {
      steps.push({
        priority: 'high',
        action: 'Migrer les composants UI existants vers le Design System',
        services: integratedServices.map(s => s.name),
        estimatedDays: integratedServices.length * 0.5
      });
    }

    if (failedServices.length > 0) {
      steps.push({
        priority: 'medium',
        action: 'Résoudre les erreurs d\'intégration',
        services: failedServices.map(s => s.name),
        estimatedDays: failedServices.length * 0.25
      });
    }

    steps.push({
      priority: 'medium',
      action: 'Créer la documentation d\'usage du Design System',
      estimatedDays: 1
    });

    steps.push({
      priority: 'low',
      action: 'Configurer Storybook pour la documentation des composants',
      estimatedDays: 0.5
    });

    return steps;
  }

  displaySummary() {
    const integrated = Object.values(this.integrationResults).filter(r => r.integrated).length;
    const failed = Object.values(this.integrationResults).filter(r => !r.integrated && r.exists).length;
    const missing = Object.values(this.integrationResults).filter(r => !r.exists).length;

    console.log('\n📊 RÉSUMÉ DE L\'INTÉGRATION DESIGN SYSTEM');
    console.log('==========================================');
    console.log(`📦 Services analysés: ${this.microservices.length}`);
    console.log(`✅ Services intégrés: ${integrated}`);
    console.log(`❌ Échecs d'intégration: ${failed}`);
    console.log(`⚠️  Services manquants: ${missing}`);
    console.log('');
    console.log('🎯 PROCHAINES ÉTAPES:');
    console.log('  1. Migrer les composants UI vers le Design System');
    console.log('  2. Créer la documentation d\'usage');
    console.log('  3. Configurer Storybook');
    console.log('  4. Former les équipes de développement');
    console.log('');
    console.log('📋 Rapport détaillé: reports/design-system-integration-report.json');
  }
}

// Exécution si appelé directement
if (require.main === module) {
  const integrator = new DesignSystemIntegrator();
  integrator.start().catch(console.error);
}

module.exports = DesignSystemIntegrator;
