#!/usr/bin/env node

/**
 * Script de validation finale Sprint 15
 * Vérifie que tous les objectifs ont été atteints
 */

const fs = require('fs');
const path = require('path');

class Sprint15Validator {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.validationResults = {
      designSystem: false,
      integration: false,
      documentation: false,
      reports: false,
      overall: false
    };
  }

  async start() {
    console.log('✅ VALIDATION FINALE SPRINT 15');
    console.log('==============================');
    console.log('Vérification de l\'achèvement des objectifs');
    console.log('');

    try {
      // 1. Valider le Design System
      await this.validateDesignSystem();

      // 2. Valider l'intégration des services
      await this.validateServiceIntegration();

      // 3. Valider la documentation
      await this.validateDocumentation();

      // 4. Valider les rapports
      await this.validateReports();

      // 5. Calcul du score global
      this.calculateOverallScore();

      console.log('\n🎯 VALIDATION TERMINÉE');
      this.displayResults();

    } catch (error) {
      console.error('\n❌ Erreur lors de la validation:', error.message);
      process.exit(1);
    }
  }

  async validateDesignSystem() {
    console.log('🎨 Validation du Design System...');

    const checks = {
      packageExists: false,
      packageBuilt: false,
      componentsExist: false,
      tokensExist: false,
      themesExist: false
    };

    // Vérifier l'existence du package
    const packagePath = path.join(this.projectRoot, 'design-system/package.json');
    checks.packageExists = fs.existsSync(packagePath);

    // Vérifier le build
    const distPath = path.join(this.projectRoot, 'design-system/dist');
    checks.packageBuilt = fs.existsSync(distPath) && fs.existsSync(path.join(distPath, 'index.js'));

    // Vérifier les composants
    const componentsPath = path.join(this.projectRoot, 'design-system/src/components');
    checks.componentsExist = fs.existsSync(componentsPath) && 
                             fs.existsSync(path.join(componentsPath, 'Button.tsx')) &&
                             fs.existsSync(path.join(componentsPath, 'Card.tsx'));

    // Vérifier les tokens
    const tokensPath = path.join(this.projectRoot, 'design-system/src/tokens');
    checks.tokensExist = fs.existsSync(tokensPath) &&
                        fs.existsSync(path.join(tokensPath, 'colors.ts')) &&
                        fs.existsSync(path.join(tokensPath, 'typography.ts'));

    // Vérifier les thèmes
    const themesPath = path.join(this.projectRoot, 'design-system/src/themes');
    checks.themesExist = fs.existsSync(themesPath) &&
                        fs.existsSync(path.join(themesPath, 'index.ts'));

    const score = Object.values(checks).filter(Boolean).length;
    this.validationResults.designSystem = score >= 4;

    console.log(`  📦 Package existe: ${checks.packageExists ? '✅' : '❌'}`);
    console.log(`  🔨 Package buildé: ${checks.packageBuilt ? '✅' : '❌'}`);
    console.log(`  🎨 Composants: ${checks.componentsExist ? '✅' : '❌'}`);
    console.log(`  🎯 Tokens: ${checks.tokensExist ? '✅' : '❌'}`);
    console.log(`  🎭 Thèmes: ${checks.themesExist ? '✅' : '❌'}`);
    console.log(`  📊 Score: ${score}/5 ${this.validationResults.designSystem ? '✅' : '❌'}`);
  }

  async validateServiceIntegration() {
    console.log('\n🔗 Validation de l\'intégration des services...');

    const reportPath = path.join(this.projectRoot, 'reports/design-system-integration-report.json');
    
    if (!fs.existsSync(reportPath)) {
      console.log('  ❌ Rapport d\'intégration manquant');
      this.validationResults.integration = false;
      return;
    }

    try {
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      const { integratedServices, totalServices } = report.summary;
      
      const integrationRate = (integratedServices / totalServices) * 100;
      this.validationResults.integration = integrationRate >= 70; // 70% minimum

      console.log(`  📊 Services intégrés: ${integratedServices}/${totalServices}`);
      console.log(`  📈 Taux d'intégration: ${integrationRate.toFixed(1)}%`);
      console.log(`  ✅ Objectif atteint: ${this.validationResults.integration ? 'OUI' : 'NON'}`);

    } catch (error) {
      console.log(`  ❌ Erreur lecture rapport: ${error.message}`);
      this.validationResults.integration = false;
    }
  }

  async validateDocumentation() {
    console.log('\n📚 Validation de la documentation...');

    const checks = {
      migrationGuide: false,
      readme: false,
      examples: false,
      finalReport: false
    };

    // Guide de migration
    const migrationGuidePath = path.join(this.projectRoot, 'design-system/docs/migration-guide.md');
    checks.migrationGuide = fs.existsSync(migrationGuidePath);

    // README
    const readmePath = path.join(this.projectRoot, 'design-system/README.md');
    checks.readme = fs.existsSync(readmePath);

    // Exemples
    const examplesPath = path.join(this.projectRoot, 'design-system/examples/usage-example.tsx');
    checks.examples = fs.existsSync(examplesPath);

    // Rapport final
    const finalReportPath = path.join(this.projectRoot, 'sprint_summaries/sprint15_final_report.md');
    checks.finalReport = fs.existsSync(finalReportPath);

    const score = Object.values(checks).filter(Boolean).length;
    this.validationResults.documentation = score >= 3;

    console.log(`  📖 Guide migration: ${checks.migrationGuide ? '✅' : '❌'}`);
    console.log(`  📄 README: ${checks.readme ? '✅' : '❌'}`);
    console.log(`  💡 Exemples: ${checks.examples ? '✅' : '❌'}`);
    console.log(`  📊 Rapport final: ${checks.finalReport ? '✅' : '❌'}`);
    console.log(`  📊 Score: ${score}/4 ${this.validationResults.documentation ? '✅' : '❌'}`);
  }

  async validateReports() {
    console.log('\n📊 Validation des rapports...');

    const checks = {
      auditReport: false,
      integrationReport: false,
      actionPlan: false
    };

    // Rapport d'audit
    const auditReportPath = path.join(this.projectRoot, 'reports/sprint15-microservices-audit.json');
    checks.auditReport = fs.existsSync(auditReportPath);

    // Rapport d'intégration
    const integrationReportPath = path.join(this.projectRoot, 'reports/design-system-integration-report.json');
    checks.integrationReport = fs.existsSync(integrationReportPath);

    // Plan d'action
    const actionPlanPath = path.join(this.projectRoot, 'SPRINT15_ACTION_PLAN.md');
    checks.actionPlan = fs.existsSync(actionPlanPath);

    const score = Object.values(checks).filter(Boolean).length;
    this.validationResults.reports = score >= 2;

    console.log(`  🔍 Rapport audit: ${checks.auditReport ? '✅' : '❌'}`);
    console.log(`  🔗 Rapport intégration: ${checks.integrationReport ? '✅' : '❌'}`);
    console.log(`  📋 Plan d'action: ${checks.actionPlan ? '✅' : '❌'}`);
    console.log(`  📊 Score: ${score}/3 ${this.validationResults.reports ? '✅' : '❌'}`);
  }

  calculateOverallScore() {
    const scores = Object.values(this.validationResults).filter(Boolean).length;
    this.validationResults.overall = scores >= 3; // 3/4 minimum pour succès
  }

  displayResults() {
    console.log('\n🏆 RÉSULTATS DE VALIDATION SPRINT 15');
    console.log('====================================');
    
    console.log(`🎨 Design System: ${this.validationResults.designSystem ? '✅ VALIDÉ' : '❌ ÉCHEC'}`);
    console.log(`🔗 Intégration Services: ${this.validationResults.integration ? '✅ VALIDÉ' : '❌ ÉCHEC'}`);
    console.log(`📚 Documentation: ${this.validationResults.documentation ? '✅ VALIDÉ' : '❌ ÉCHEC'}`);
    console.log(`📊 Rapports: ${this.validationResults.reports ? '✅ VALIDÉ' : '❌ ÉCHEC'}`);
    
    console.log('\n🎯 STATUT GLOBAL:');
    if (this.validationResults.overall) {
      console.log('✅ SPRINT 15 - SUCCÈS COMPLET');
      console.log('🎉 Tous les objectifs critiques ont été atteints !');
      console.log('🚀 Prêt pour le Sprint 16');
    } else {
      console.log('⚠️ SPRINT 15 - SUCCÈS PARTIEL');
      console.log('🔧 Quelques éléments nécessitent une attention');
      console.log('📋 Voir les détails ci-dessus pour les actions correctives');
    }

    console.log('\n📈 SCORE GLOBAL:');
    const totalScore = Object.values(this.validationResults).filter(Boolean).length;
    const percentage = (totalScore / 5) * 100;
    console.log(`📊 ${totalScore}/5 composants validés (${percentage}%)`);
    
    if (percentage >= 80) {
      console.log('🏅 EXCELLENCE - Sprint remarquablement réussi');
    } else if (percentage >= 60) {
      console.log('✅ SUCCÈS - Objectifs principaux atteints');
    } else {
      console.log('⚠️ AMÉLIORATION NÉCESSAIRE - Revoir les éléments manquants');
    }
  }
}

// Exécution si appelé directement
if (require.main === module) {
  const validator = new Sprint15Validator();
  validator.start().catch(console.error);
}

module.exports = Sprint15Validator;
