#!/usr/bin/env node

/**
 * Script de migration des composants UI vers le Design System
 * Automatise la migration des composants existants vers le Design System unifié
 */

const fs = require('fs');
const path = require('path');

class UIComponentMigrator {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.designSystemPackage = '@retreat-and-be/design-system';
    this.integratedServices = [
      'Front-Audrey-V1-Main-main',
      'Agent IA',
      'Backend-NestJS',
      'Financial-Management',
      'Hotel-Booking',
      'Education',
      'Marketplace',
      'web3-nft-service'
    ];
    this.migrationResults = {};
  }

  async start() {
    console.log('🔄 MIGRATION COMPOSANTS UI - SPRINT 15');
    console.log('======================================');
    console.log('Migration automatique des composants UI vers le Design System');
    console.log('');

    try {
      // 1. Analyser les composants existants
      await this.analyzeExistingComponents();

      // 2. Créer les guides de migration
      await this.createMigrationGuides();

      // 3. Générer les exemples d'usage
      await this.generateUsageExamples();

      // 4. Créer la documentation
      await this.createDocumentation();

      console.log('\n✅ MIGRATION UI COMPONENTS PRÉPARÉE AVEC SUCCÈS');
      this.displaySummary();

    } catch (error) {
      console.error('\n❌ Erreur lors de la migration:', error.message);
      process.exit(1);
    }
  }

  async analyzeExistingComponents() {
    console.log('🔍 Analyse des composants UI existants...');

    for (const serviceName of this.integratedServices) {
      console.log(`\n📋 Analyse: ${serviceName}`);
      
      const servicePath = path.resolve(this.projectRoot, serviceName);
      const result = {
        name: serviceName,
        path: servicePath,
        components: [],
        migrationPriority: 'medium'
      };

      // Analyser les composants dans src/components
      const componentsPath = path.join(servicePath, 'src/components');
      if (fs.existsSync(componentsPath)) {
        result.components = await this.findUIComponents(componentsPath);
        console.log(`  🎨 Composants trouvés: ${result.components.length}`);
      }

      // Déterminer la priorité de migration
      result.migrationPriority = this.calculateMigrationPriority(result);

      this.migrationResults[serviceName] = result;
    }

    console.log('\n✅ Analyse des composants terminée');
  }

  async findUIComponents(dir) {
    const components = [];

    try {
      const files = fs.readdirSync(dir);

      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          components.push(...await this.findUIComponents(filePath));
        } else if (file.match(/\.(tsx?|jsx?)$/) && !file.includes('.test.') && !file.includes('.stories.')) {
          const component = {
            name: file,
            path: path.relative(dir, filePath),
            type: this.detectComponentType(file),
            migrationComplexity: this.assessMigrationComplexity(filePath)
          };
          components.push(component);
        }
      }
    } catch (error) {
      // Ignorer les erreurs de lecture
    }

    return components;
  }

  detectComponentType(filename) {
    const name = filename.toLowerCase();
    
    if (name.includes('button')) return 'button';
    if (name.includes('card')) return 'card';
    if (name.includes('input') || name.includes('form')) return 'form';
    if (name.includes('modal') || name.includes('dialog')) return 'modal';
    if (name.includes('nav') || name.includes('menu')) return 'navigation';
    if (name.includes('header') || name.includes('footer')) return 'layout';
    if (name.includes('alert') || name.includes('toast')) return 'feedback';
    if (name.includes('spinner') || name.includes('loading')) return 'loading';
    
    return 'component';
  }

  assessMigrationComplexity(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Facteurs de complexité
      let complexity = 0;
      
      if (content.includes('styled-components')) complexity += 2;
      if (content.includes('@emotion')) complexity += 2;
      if (content.includes('makeStyles')) complexity += 3;
      if (content.includes('useTheme')) complexity += 1;
      if (content.includes('className')) complexity += 1;
      if (content.length > 1000) complexity += 1;
      
      if (complexity <= 2) return 'low';
      if (complexity <= 4) return 'medium';
      return 'high';
    } catch (error) {
      return 'unknown';
    }
  }

  calculateMigrationPriority(result) {
    const componentCount = result.components.length;
    const highComplexityCount = result.components.filter(c => c.migrationComplexity === 'high').length;
    
    if (componentCount > 50 || highComplexityCount > 10) return 'high';
    if (componentCount > 20 || highComplexityCount > 5) return 'medium';
    return 'low';
  }

  async createMigrationGuides() {
    console.log('\n📚 Création des guides de migration...');

    const guidesDir = path.join(this.projectRoot, 'design-system/docs');
    if (!fs.existsSync(guidesDir)) {
      fs.mkdirSync(guidesDir, { recursive: true });
    }

    // Guide de migration général
    const migrationGuide = `# Guide de Migration vers le Design System

## 🎯 Objectif
Migrer tous les composants UI existants vers le Design System unifié Retreat And Be.

## 📋 Étapes de Migration

### 1. Installation
Le Design System est déjà installé dans votre service via:
\`\`\`json
"@retreat-and-be/design-system": "file:../design-system"
\`\`\`

### 2. Import des Composants
\`\`\`typescript
import { Button, Card, CardHeader, CardTitle, CardContent } from '@retreat-and-be/design-system';
\`\`\`

### 3. Remplacement des Composants Existants

#### Avant (Composant personnalisé)
\`\`\`typescript
import { CustomButton } from './components/CustomButton';

function MyComponent() {
  return <CustomButton variant="primary">Click me</CustomButton>;
}
\`\`\`

#### Après (Design System)
\`\`\`typescript
import { Button } from '@retreat-and-be/design-system';

function MyComponent() {
  return <Button variant="default">Click me</Button>;
}
\`\`\`

## 🎨 Composants Disponibles

### Button
- Variants: default, destructive, outline, secondary, ghost, link, success, warning
- Sizes: default, sm, lg, xl, icon
- Props: loading, leftIcon, rightIcon

### Card
- Variants: default, elevated, outlined, ghost
- Padding: none, sm, default, lg
- Sous-composants: CardHeader, CardTitle, CardDescription, CardContent, CardFooter

## 🔧 Tokens Disponibles

### Couleurs
\`\`\`typescript
import { colors } from '@retreat-and-be/design-system';
// colors.primary[500], colors.secondary[100], etc.
\`\`\`

### Typographie
\`\`\`typescript
import { typography } from '@retreat-and-be/design-system';
// typography.fontSize.lg, typography.fontWeight.bold, etc.
\`\`\`

## 📝 Checklist de Migration

- [ ] Identifier tous les composants UI existants
- [ ] Mapper les composants vers le Design System
- [ ] Remplacer les imports
- [ ] Tester les composants migrés
- [ ] Supprimer les anciens composants
- [ ] Mettre à jour la documentation

## 🚨 Points d'Attention

1. **Variants**: Vérifier la correspondance des variants
2. **Props**: Adapter les props aux nouvelles interfaces
3. **Styles**: Utiliser les tokens du Design System
4. **Tests**: Mettre à jour les tests unitaires
`;

    fs.writeFileSync(path.join(guidesDir, 'migration-guide.md'), migrationGuide);
    console.log('  ✅ Guide de migration créé');
  }

  async generateUsageExamples() {
    console.log('\n💡 Génération des exemples d\'usage...');

    const examplesDir = path.join(this.projectRoot, 'design-system/examples');
    if (!fs.existsSync(examplesDir)) {
      fs.mkdirSync(examplesDir, { recursive: true });
    }

    // Exemple d'usage des composants
    const usageExample = `import React from 'react';
import { 
  Button, 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent, 
  CardFooter 
} from '@retreat-and-be/design-system';

export function ExampleUsage() {
  return (
    <div className="p-6 space-y-6">
      {/* Buttons */}
      <div className="space-x-4">
        <Button variant="default">Primary</Button>
        <Button variant="secondary">Secondary</Button>
        <Button variant="outline">Outline</Button>
        <Button variant="ghost">Ghost</Button>
        <Button variant="success">Success</Button>
        <Button variant="warning">Warning</Button>
        <Button variant="destructive">Destructive</Button>
      </div>

      {/* Card Example */}
      <Card className="w-96">
        <CardHeader>
          <CardTitle>Retreat Card</CardTitle>
        </CardHeader>
        <CardContent>
          <p>This is an example of using the Design System Card component.</p>
        </CardContent>
        <CardFooter>
          <Button variant="default">Book Now</Button>
        </CardFooter>
      </Card>

      {/* Loading Button */}
      <Button loading>Loading...</Button>

      {/* Button with Icons */}
      <Button 
        leftIcon={<span>🏠</span>}
        rightIcon={<span>→</span>}
      >
        Go Home
      </Button>
    </div>
  );
}`;

    fs.writeFileSync(path.join(examplesDir, 'usage-example.tsx'), usageExample);
    console.log('  ✅ Exemples d\'usage créés');
  }

  async createDocumentation() {
    console.log('\n📖 Création de la documentation...');

    const docsDir = path.join(this.projectRoot, 'design-system/docs');
    
    // Documentation README
    const readme = `# Retreat And Be Design System

## 🎨 Vue d'ensemble
Design System unifié pour tous les microservices de la plateforme Retreat And Be.

## 📦 Installation
\`\`\`bash
npm install @retreat-and-be/design-system
\`\`\`

## 🚀 Usage Rapide
\`\`\`typescript
import { Button, Card } from '@retreat-and-be/design-system';

function App() {
  return (
    <Card>
      <Button variant="default">Hello World</Button>
    </Card>
  );
}
\`\`\`

## 📚 Documentation
- [Guide de Migration](./migration-guide.md)
- [Exemples d'Usage](../examples/usage-example.tsx)

## 🎯 Objectifs
- Cohérence visuelle entre tous les services
- Réutilisabilité des composants
- Maintenabilité simplifiée
- Performance optimisée

## 🔧 Développement
\`\`\`bash
# Build
npm run build

# Development
npm run dev

# Type checking
npm run type-check
\`\`\`

## 📊 Statut d'Intégration
- ✅ Front-Audrey-V1-Main-main
- ✅ Agent IA
- ✅ Backend-NestJS
- ✅ Financial-Management
- ✅ Hotel-Booking
- ✅ Education
- ✅ Marketplace
- ✅ web3-nft-service
- ⚠️ Analyzer (conflits de dépendances)
- ❌ Social-Platform (service manquant)
`;

    fs.writeFileSync(path.join(this.projectRoot, 'design-system/README.md'), readme);
    console.log('  ✅ Documentation README créée');
  }

  displaySummary() {
    const totalComponents = Object.values(this.migrationResults)
      .reduce((sum, result) => sum + result.components.length, 0);

    console.log('\n📊 RÉSUMÉ DE LA PRÉPARATION MIGRATION');
    console.log('=====================================');
    console.log(`📦 Services analysés: ${this.integratedServices.length}`);
    console.log(`🎨 Composants UI identifiés: ${totalComponents}`);
    console.log(`📚 Guides créés: Migration + Documentation`);
    console.log(`💡 Exemples générés: Usage + Patterns`);
    console.log('');
    console.log('🎯 PROCHAINES ÉTAPES:');
    console.log('  1. Commencer la migration par les services prioritaires');
    console.log('  2. Utiliser les guides de migration créés');
    console.log('  3. Tester chaque composant migré');
    console.log('  4. Former les équipes sur le Design System');
    console.log('');
    console.log('📋 Documentation: design-system/docs/');
    console.log('💡 Exemples: design-system/examples/');
  }
}

// Exécution si appelé directement
if (require.main === module) {
  const migrator = new UIComponentMigrator();
  migrator.start().catch(console.error);
}

module.exports = UIComponentMigrator;
