# 🔐 HANUMAN UNIFIED - CONFIGURATION ENVIRONNEMENT
# Sprint 15 - Migration des secrets

# Core Configuration
NODE_ENV=development
PORT=3004
API_PREFIX=api/v1/hanuman

# AI Services
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/hanuman_dev
MONGODB_URI=mongodb://localhost:27017/hanuman
REDIS_URL=redis://localhost:6379

# Vector Database
WEAVIATE_URL=http://localhost:8080
PINECONE_API_KEY=your-pinecone-api-key-here

# Security
JWT_SECRET=your-hanuman-jwt-secret-here
ENCRYPTION_KEY=your-hanuman-encryption-key-here

# Microservices Communication
BACKEND_URL=http://localhost:3001
FRONTEND_URL=http://localhost:3000
AGENT_IA_URL=http://localhost:3002
SECURITY_URL=http://localhost:3003

# Monitoring
SENTRY_DSN=https://your-hanuman-sentry-dsn-here
LOG_LEVEL=debug

# Hanuman Specific
CORTEX_MODE=development
MEMORY_RETENTION=30
LEARNING_RATE=0.01
