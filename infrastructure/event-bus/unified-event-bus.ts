/**
 * Unified Event Bus - Sprint 17
 * Communication Inter-Services Centralisée
 * Date: 25 Juin 2025
 */

import { <PERSON><PERSON><PERSON>, Producer, Consumer, EachMessagePayload } from 'kafkajs';
import { EventEmitter } from 'events';
import { Logger } from 'winston';

// Types pour les événements
export interface ServiceEvent {
  id: string;
  source: string;
  target: string;
  type: EventType;
  payload: any;
  timestamp: Date;
  correlationId: string;
  version: string;
  metadata?: Record<string, any>;
}

export enum EventType {
  // Hanuman Events
  AGENT_STARTED = 'agent.started',
  AGENT_STOPPED = 'agent.stopped',
  AGENT_ERROR = 'agent.error',
  CORTEX_DECISION = 'cortex.decision',
  NEURAL_SYNC = 'neural.sync',
  
  // RB2 Business Events
  USER_REGISTERED = 'user.registered',
  RETREAT_BOOKED = 'retreat.booked',
  PAYMENT_PROCESSED = 'payment.processed',
  MESSAGE_SENT = 'message.sent',
  CONTENT_MODERATED = 'content.moderated',
  
  // System Events
  SERVICE_HEALTH = 'service.health',
  METRIC_COLLECTED = 'metric.collected',
  ALERT_TRIGGERED = 'alert.triggered',
  DEPLOYMENT_STARTED = 'deployment.started',
  DEPLOYMENT_COMPLETED = 'deployment.completed',
  
  // Integration Events
  API_CALL = 'api.call',
  DATA_SYNC = 'data.sync',
  CACHE_INVALIDATED = 'cache.invalidated'
}

export class UnifiedEventBus extends EventEmitter {
  private kafka: Kafka;
  private producer: Producer;
  private consumers: Map<string, Consumer> = new Map();
  private logger: Logger;
  private isConnected: boolean = false;

  constructor(
    private brokers: string[] = ['kafka:9092'],
    private clientId: string = 'unified-event-bus',
    logger?: Logger
  ) {
    super();
    
    this.kafka = new Kafka({
      clientId: this.clientId,
      brokers: this.brokers,
      retry: {
        initialRetryTime: 100,
        retries: 8
      }
    });

    this.producer = this.kafka.producer({
      maxInFlightRequests: 1,
      idempotent: true,
      transactionTimeout: 30000
    });

    this.logger = logger || console as any;
  }

  /**
   * Initialise la connexion au Event Bus
   */
  async connect(): Promise<void> {
    try {
      await this.producer.connect();
      this.isConnected = true;
      this.logger.info('Unified Event Bus connected successfully');
      this.emit('connected');
    } catch (error) {
      this.logger.error('Failed to connect to Event Bus:', error);
      throw error;
    }
  }

  /**
   * Ferme la connexion au Event Bus
   */
  async disconnect(): Promise<void> {
    try {
      // Disconnect all consumers
      for (const [groupId, consumer] of this.consumers) {
        await consumer.disconnect();
        this.logger.info(`Consumer ${groupId} disconnected`);
      }
      this.consumers.clear();

      // Disconnect producer
      await this.producer.disconnect();
      this.isConnected = false;
      this.logger.info('Unified Event Bus disconnected');
      this.emit('disconnected');
    } catch (error) {
      this.logger.error('Error disconnecting Event Bus:', error);
      throw error;
    }
  }

  /**
   * Publie un événement sur le bus
   */
  async publish(event: Omit<ServiceEvent, 'id' | 'timestamp'>): Promise<void> {
    if (!this.isConnected) {
      throw new Error('Event Bus not connected');
    }

    const fullEvent: ServiceEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: new Date()
    };

    const topic = this.getTopicForEvent(event.type);
    
    try {
      await this.producer.send({
        topic,
        messages: [{
          key: fullEvent.correlationId,
          value: JSON.stringify(fullEvent),
          headers: {
            source: fullEvent.source,
            target: fullEvent.target,
            type: fullEvent.type,
            version: fullEvent.version
          }
        }]
      });

      this.logger.debug(`Event published: ${fullEvent.type} from ${fullEvent.source}`);
      this.emit('event-published', fullEvent);
    } catch (error) {
      this.logger.error('Failed to publish event:', error);
      throw error;
    }
  }

  /**
   * S'abonne aux événements d'un type spécifique
   */
  async subscribe(
    eventTypes: EventType[],
    groupId: string,
    handler: (event: ServiceEvent) => Promise<void>
  ): Promise<void> {
    const consumer = this.kafka.consumer({ 
      groupId,
      sessionTimeout: 30000,
      heartbeatInterval: 3000
    });

    await consumer.connect();
    
    const topics = eventTypes.map(type => this.getTopicForEvent(type));
    await consumer.subscribe({ topics });

    await consumer.run({
      eachMessage: async ({ topic, partition, message }: EachMessagePayload) => {
        try {
          if (!message.value) return;

          const event: ServiceEvent = JSON.parse(message.value.toString());
          
          this.logger.debug(`Event received: ${event.type} for ${groupId}`);
          await handler(event);
          
          this.emit('event-processed', event, groupId);
        } catch (error) {
          this.logger.error(`Error processing event in ${groupId}:`, error);
          this.emit('event-error', error, groupId);
        }
      }
    });

    this.consumers.set(groupId, consumer);
    this.logger.info(`Subscribed to events: ${eventTypes.join(', ')} with group ${groupId}`);
  }

  /**
   * Publie un événement de santé de service
   */
  async publishHealthEvent(serviceName: string, status: 'healthy' | 'unhealthy', details?: any): Promise<void> {
    await this.publish({
      source: serviceName,
      target: 'monitoring',
      type: EventType.SERVICE_HEALTH,
      payload: { status, details },
      correlationId: this.generateCorrelationId(),
      version: '1.0'
    });
  }

  /**
   * Publie un événement de métrique
   */
  async publishMetric(serviceName: string, metricName: string, value: number, labels?: Record<string, string>): Promise<void> {
    await this.publish({
      source: serviceName,
      target: 'monitoring',
      type: EventType.METRIC_COLLECTED,
      payload: { metricName, value, labels },
      correlationId: this.generateCorrelationId(),
      version: '1.0'
    });
  }

  /**
   * Publie un événement d'alerte
   */
  async publishAlert(serviceName: string, alertName: string, severity: 'low' | 'medium' | 'high' | 'critical', message: string): Promise<void> {
    await this.publish({
      source: serviceName,
      target: 'alerting',
      type: EventType.ALERT_TRIGGERED,
      payload: { alertName, severity, message },
      correlationId: this.generateCorrelationId(),
      version: '1.0'
    });
  }

  /**
   * Détermine le topic Kafka pour un type d'événement
   */
  private getTopicForEvent(eventType: EventType): string {
    const topicMap: Record<string, string> = {
      // Hanuman topics
      'agent.': 'hanuman-agents',
      'cortex.': 'hanuman-cortex',
      'neural.': 'hanuman-neural',
      
      // RB2 business topics
      'user.': 'rb2-users',
      'retreat.': 'rb2-retreats',
      'payment.': 'rb2-payments',
      'message.': 'rb2-messaging',
      'content.': 'rb2-content',
      
      // System topics
      'service.': 'system-health',
      'metric.': 'system-metrics',
      'alert.': 'system-alerts',
      'deployment.': 'system-deployments',
      
      // Integration topics
      'api.': 'integration-api',
      'data.': 'integration-data',
      'cache.': 'integration-cache'
    };

    for (const [prefix, topic] of Object.entries(topicMap)) {
      if (eventType.startsWith(prefix)) {
        return topic;
      }
    }

    return 'unified-events'; // Topic par défaut
  }

  /**
   * Génère un ID unique pour l'événement
   */
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Génère un ID de corrélation
   */
  private generateCorrelationId(): string {
    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Obtient les statistiques du Event Bus
   */
  getStats(): {
    isConnected: boolean;
    activeConsumers: number;
    consumerGroups: string[];
  } {
    return {
      isConnected: this.isConnected,
      activeConsumers: this.consumers.size,
      consumerGroups: Array.from(this.consumers.keys())
    };
  }
}

// Instance singleton pour l'application
export const eventBus = new UnifiedEventBus();

// Helper functions pour les services
export const publishEvent = (event: Omit<ServiceEvent, 'id' | 'timestamp'>) => eventBus.publish(event);
export const subscribeToEvents = (eventTypes: EventType[], groupId: string, handler: (event: ServiceEvent) => Promise<void>) => 
  eventBus.subscribe(eventTypes, groupId, handler);

export default UnifiedEventBus;
