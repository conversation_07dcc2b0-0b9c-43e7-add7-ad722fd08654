/**
 * Unified Service Registry - Sprint 17
 * Découverte et Gestion des Services
 * Date: 25 Juin 2025
 */

import { EventEmitter } from 'events';
import Redis from 'ioredis';
import { Logger } from 'winston';

export interface ServiceInfo {
  id: string;
  name: string;
  type: ServiceType;
  version: string;
  host: string;
  port: number;
  protocol: 'http' | 'https' | 'grpc';
  healthEndpoint: string;
  status: ServiceStatus;
  metadata: Record<string, any>;
  registeredAt: Date;
  lastHeartbeat: Date;
  tags: string[];
}

export enum ServiceType {
  // Hanuman Services
  CORTEX_CENTRAL = 'cortex-central',
  AGENT_FRONTEND = 'agent-frontend',
  AGENT_BACKEND = 'agent-backend',
  AGENT_DEVOPS = 'agent-devops',
  AGENT_QA = 'agent-qa',
  AGENT_SECURITY = 'agent-security',
  
  // RB2 Services
  BACKEND_NESTJS = 'backend-nestjs',
  SOCIAL_PLATFORM = 'social-platform',
  MESSAGING_SERVICE = 'messaging-service',
  AGENT_IA = 'agent-ia',
  ANALYZER = 'analyzer',
  FINANCIAL_MANAGEMENT = 'financial-management',
  SECURITY_SERVICE = 'security-service',
  
  // Infrastructure Services
  API_GATEWAY = 'api-gateway',
  DATABASE = 'database',
  CACHE = 'cache',
  MESSAGE_BROKER = 'message-broker',
  MONITORING = 'monitoring'
}

export enum ServiceStatus {
  STARTING = 'starting',
  HEALTHY = 'healthy',
  UNHEALTHY = 'unhealthy',
  DEGRADED = 'degraded',
  STOPPING = 'stopping',
  STOPPED = 'stopped'
}

export interface HealthCheckResult {
  status: ServiceStatus;
  timestamp: Date;
  responseTime: number;
  details?: Record<string, any>;
}

export class UnifiedServiceRegistry extends EventEmitter {
  private redis: Redis;
  private services: Map<string, ServiceInfo> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private logger: Logger;
  private readonly REGISTRY_KEY = 'unified:service-registry';
  private readonly HEARTBEAT_INTERVAL = 30000; // 30 seconds
  private readonly SERVICE_TTL = 90; // 90 seconds

  constructor(
    redisUrl: string = 'redis://redis:6379',
    logger?: Logger
  ) {
    super();
    
    this.redis = new Redis(redisUrl, {
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });

    this.logger = logger || console as any;
    this.setupEventHandlers();
  }

  /**
   * Initialise le Service Registry
   */
  async initialize(): Promise<void> {
    try {
      await this.redis.connect();
      await this.loadServicesFromRedis();
      this.startHealthChecks();
      
      this.logger.info('Unified Service Registry initialized successfully');
      this.emit('initialized');
    } catch (error) {
      this.logger.error('Failed to initialize Service Registry:', error);
      throw error;
    }
  }

  /**
   * Enregistre un service
   */
  async registerService(serviceInfo: Omit<ServiceInfo, 'id' | 'registeredAt' | 'lastHeartbeat'>): Promise<string> {
    const serviceId = this.generateServiceId(serviceInfo.name, serviceInfo.type);
    
    const fullServiceInfo: ServiceInfo = {
      ...serviceInfo,
      id: serviceId,
      registeredAt: new Date(),
      lastHeartbeat: new Date()
    };

    // Store in memory
    this.services.set(serviceId, fullServiceInfo);

    // Store in Redis with TTL
    await this.redis.hset(
      this.REGISTRY_KEY,
      serviceId,
      JSON.stringify(fullServiceInfo)
    );
    await this.redis.expire(`${this.REGISTRY_KEY}:${serviceId}`, this.SERVICE_TTL);

    this.logger.info(`Service registered: ${serviceInfo.name} (${serviceId})`);
    this.emit('service-registered', fullServiceInfo);

    return serviceId;
  }

  /**
   * Désenregistre un service
   */
  async unregisterService(serviceId: string): Promise<void> {
    const service = this.services.get(serviceId);
    if (!service) {
      throw new Error(`Service not found: ${serviceId}`);
    }

    // Remove from memory
    this.services.delete(serviceId);

    // Remove from Redis
    await this.redis.hdel(this.REGISTRY_KEY, serviceId);
    await this.redis.del(`${this.REGISTRY_KEY}:${serviceId}`);

    this.logger.info(`Service unregistered: ${service.name} (${serviceId})`);
    this.emit('service-unregistered', service);
  }

  /**
   * Met à jour le heartbeat d'un service
   */
  async updateHeartbeat(serviceId: string): Promise<void> {
    const service = this.services.get(serviceId);
    if (!service) {
      throw new Error(`Service not found: ${serviceId}`);
    }

    service.lastHeartbeat = new Date();
    this.services.set(serviceId, service);

    // Update in Redis
    await this.redis.hset(
      this.REGISTRY_KEY,
      serviceId,
      JSON.stringify(service)
    );
    await this.redis.expire(`${this.REGISTRY_KEY}:${serviceId}`, this.SERVICE_TTL);

    this.emit('heartbeat-updated', service);
  }

  /**
   * Met à jour le statut d'un service
   */
  async updateServiceStatus(serviceId: string, status: ServiceStatus, details?: Record<string, any>): Promise<void> {
    const service = this.services.get(serviceId);
    if (!service) {
      throw new Error(`Service not found: ${serviceId}`);
    }

    const oldStatus = service.status;
    service.status = status;
    service.lastHeartbeat = new Date();
    
    if (details) {
      service.metadata = { ...service.metadata, ...details };
    }

    this.services.set(serviceId, service);

    // Update in Redis
    await this.redis.hset(
      this.REGISTRY_KEY,
      serviceId,
      JSON.stringify(service)
    );

    this.logger.info(`Service status updated: ${service.name} ${oldStatus} -> ${status}`);
    this.emit('service-status-changed', service, oldStatus);
  }

  /**
   * Découvre des services par type
   */
  discoverServices(type?: ServiceType, status?: ServiceStatus): ServiceInfo[] {
    let services = Array.from(this.services.values());

    if (type) {
      services = services.filter(s => s.type === type);
    }

    if (status) {
      services = services.filter(s => s.status === status);
    }

    return services;
  }

  /**
   * Trouve un service par nom
   */
  findServiceByName(name: string): ServiceInfo | undefined {
    return Array.from(this.services.values()).find(s => s.name === name);
  }

  /**
   * Obtient un service par ID
   */
  getService(serviceId: string): ServiceInfo | undefined {
    return this.services.get(serviceId);
  }

  /**
   * Obtient l'URL complète d'un service
   */
  getServiceUrl(serviceId: string): string | null {
    const service = this.services.get(serviceId);
    if (!service) return null;

    return `${service.protocol}://${service.host}:${service.port}`;
  }

  /**
   * Effectue un health check sur un service
   */
  async performHealthCheck(serviceId: string): Promise<HealthCheckResult> {
    const service = this.services.get(serviceId);
    if (!service) {
      throw new Error(`Service not found: ${serviceId}`);
    }

    const startTime = Date.now();
    
    try {
      const url = `${this.getServiceUrl(serviceId)}${service.healthEndpoint}`;
      const response = await fetch(url, { 
        method: 'GET',
        timeout: 5000 
      });

      const responseTime = Date.now() - startTime;
      const status = response.ok ? ServiceStatus.HEALTHY : ServiceStatus.UNHEALTHY;

      const result: HealthCheckResult = {
        status,
        timestamp: new Date(),
        responseTime,
        details: {
          httpStatus: response.status,
          url
        }
      };

      // Update service status
      await this.updateServiceStatus(serviceId, status);

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      const result: HealthCheckResult = {
        status: ServiceStatus.UNHEALTHY,
        timestamp: new Date(),
        responseTime,
        details: {
          error: error.message
        }
      };

      // Update service status
      await this.updateServiceStatus(serviceId, ServiceStatus.UNHEALTHY);

      return result;
    }
  }

  /**
   * Obtient les statistiques du registry
   */
  getStats(): {
    totalServices: number;
    servicesByType: Record<string, number>;
    servicesByStatus: Record<string, number>;
    healthyServices: number;
  } {
    const services = Array.from(this.services.values());
    
    const servicesByType: Record<string, number> = {};
    const servicesByStatus: Record<string, number> = {};

    services.forEach(service => {
      servicesByType[service.type] = (servicesByType[service.type] || 0) + 1;
      servicesByStatus[service.status] = (servicesByStatus[service.status] || 0) + 1;
    });

    return {
      totalServices: services.length,
      servicesByType,
      servicesByStatus,
      healthyServices: services.filter(s => s.status === ServiceStatus.HEALTHY).length
    };
  }

  /**
   * Charge les services depuis Redis
   */
  private async loadServicesFromRedis(): Promise<void> {
    try {
      const servicesData = await this.redis.hgetall(this.REGISTRY_KEY);
      
      for (const [serviceId, serviceJson] of Object.entries(servicesData)) {
        try {
          const service: ServiceInfo = JSON.parse(serviceJson);
          this.services.set(serviceId, service);
        } catch (error) {
          this.logger.warn(`Failed to parse service data for ${serviceId}:`, error);
        }
      }

      this.logger.info(`Loaded ${this.services.size} services from Redis`);
    } catch (error) {
      this.logger.error('Failed to load services from Redis:', error);
    }
  }

  /**
   * Démarre les health checks périodiques
   */
  private startHealthChecks(): void {
    this.healthCheckInterval = setInterval(async () => {
      const services = Array.from(this.services.values());
      
      for (const service of services) {
        try {
          await this.performHealthCheck(service.id);
        } catch (error) {
          this.logger.error(`Health check failed for ${service.name}:`, error);
        }
      }
    }, this.HEARTBEAT_INTERVAL);

    this.logger.info('Health checks started');
  }

  /**
   * Configure les gestionnaires d'événements
   */
  private setupEventHandlers(): void {
    this.redis.on('error', (error) => {
      this.logger.error('Redis error in Service Registry:', error);
      this.emit('error', error);
    });

    this.redis.on('connect', () => {
      this.logger.info('Service Registry connected to Redis');
    });
  }

  /**
   * Génère un ID unique pour un service
   */
  private generateServiceId(name: string, type: ServiceType): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 5);
    return `${type}-${name}-${timestamp}-${random}`;
  }

  /**
   * Ferme le Service Registry
   */
  async shutdown(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    await this.redis.disconnect();
    this.logger.info('Service Registry shutdown completed');
    this.emit('shutdown');
  }
}

// Instance singleton
export const serviceRegistry = new UnifiedServiceRegistry();

export default UnifiedServiceRegistry;
