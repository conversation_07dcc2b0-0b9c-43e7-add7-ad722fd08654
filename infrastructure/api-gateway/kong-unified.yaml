# Kong API Gateway - Configuration Unifiée Sprint 17
# Date: 25 Juin 2025
# Objectif: Unifier tous les microservices sous une API Gateway cohérente

_format_version: "3.0"
_transform: true

# ===== SERVICES BACKEND =====
services:
  # Backend NestJS Principal
  - name: backend-nestjs
    url: http://backend-nestjs:3000
    protocol: http
    host: backend-nestjs
    port: 3000
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    tags:
      - backend
      - nestjs
      - core
      - v1

  # Service Social Platform Video
  - name: social-platform-video
    url: http://social-platform-video:3002
    protocol: http
    host: social-platform-video
    port: 3002
    path: /
    connect_timeout: 45000
    write_timeout: 45000
    read_timeout: 45000
    retries: 3
    tags:
      - social
      - video
      - media
      - v1

  # Service de Messagerie
  - name: messaging-service
    url: http://messaging-service:5178
    protocol: http
    host: messaging-service
    port: 5178
    path: /
    connect_timeout: 30000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3
    tags:
      - messaging
      - communication
      - realtime
      - v1

  # Agent IA
  - name: agent-ia
    url: http://agent-ia:8000
    protocol: http
    host: agent-ia
    port: 8000
    path: /
    connect_timeout: 120000
    write_timeout: 120000
    read_timeout: 120000
    retries: 2
    tags:
      - ai
      - agent
      - ml
      - v1

  # Service Analyzer
  - name: analyzer-service
    url: http://analyzer:8080
    protocol: http
    host: analyzer
    port: 8080
    path: /
    connect_timeout: 90000
    write_timeout: 90000
    read_timeout: 90000
    retries: 3
    tags:
      - analyzer
      - analytics
      - ml
      - v1

  # Financial Management
  - name: financial-management
    url: http://financial-management:4000
    protocol: http
    host: financial-management
    port: 4000
    path: /
    connect_timeout: 45000
    write_timeout: 45000
    read_timeout: 45000
    retries: 3
    tags:
      - financial
      - payments
      - transactions
      - v1

  # Security Service
  - name: security-service
    url: http://security-service:5000
    protocol: http
    host: security-service
    port: 5000
    path: /
    connect_timeout: 30000
    write_timeout: 30000
    read_timeout: 30000
    retries: 5
    tags:
      - security
      - auth
      - compliance
      - v1

  # Hanuman Cortex Central
  - name: hanuman-cortex
    url: http://cortex-central:8081
    protocol: http
    host: cortex-central
    port: 8081
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3
    tags:
      - hanuman
      - cortex
      - orchestrator
      - v1

  # Hanuman Agent Frontend
  - name: hanuman-frontend
    url: http://agent-frontend:3001
    protocol: http
    host: agent-frontend
    port: 3001
    path: /
    connect_timeout: 30000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3
    tags:
      - hanuman
      - frontend
      - ui
      - v1

  # Hanuman Agent Backend
  - name: hanuman-backend
    url: http://agent-backend:3002
    protocol: http
    host: agent-backend
    port: 3002
    path: /
    connect_timeout: 45000
    write_timeout: 45000
    read_timeout: 45000
    retries: 3
    tags:
      - hanuman
      - backend
      - api
      - v1

# ===== ROUTES =====
routes:
  # Backend NestJS Routes
  - name: backend-api-v1
    service: backend-nestjs
    paths:
      - /api/v1
    methods:
      - GET
      - POST
      - PUT
      - DELETE
      - PATCH
    strip_path: false
    preserve_host: false
    tags:
      - api
      - v1
      - backend

  # Authentication Routes
  - name: auth-routes
    service: backend-nestjs
    paths:
      - /api/v1/auth
    methods:
      - GET
      - POST
    strip_path: false
    preserve_host: false
    tags:
      - auth
      - security

  # Social Platform Routes
  - name: social-routes
    service: social-platform-video
    paths:
      - /api/v1/social
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    strip_path: true
    preserve_host: false
    tags:
      - social
      - media

  # Messaging Routes
  - name: messaging-routes
    service: messaging-service
    paths:
      - /api/v1/messaging
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    strip_path: true
    preserve_host: false
    tags:
      - messaging
      - realtime

  # AI Agent Routes
  - name: ai-routes
    service: agent-ia
    paths:
      - /api/v1/ai
    methods:
      - GET
      - POST
    strip_path: true
    preserve_host: false
    tags:
      - ai
      - ml

  # Analytics Routes
  - name: analytics-routes
    service: analyzer-service
    paths:
      - /api/v1/analytics
    methods:
      - GET
      - POST
    strip_path: true
    preserve_host: false
    tags:
      - analytics
      - reporting

  # Financial Routes
  - name: financial-routes
    service: financial-management
    paths:
      - /api/v1/financial
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    strip_path: true
    preserve_host: false
    tags:
      - financial
      - payments

  # Security Routes
  - name: security-routes
    service: security-service
    paths:
      - /api/v1/security
    methods:
      - GET
      - POST
    strip_path: true
    preserve_host: false
    tags:
      - security
      - compliance

  # Hanuman Cortex Routes
  - name: hanuman-cortex-routes
    service: hanuman-cortex
    paths:
      - /api/v1/hanuman
    methods:
      - GET
      - POST
      - PUT
    strip_path: true
    preserve_host: false
    tags:
      - hanuman
      - orchestrator

  # Hanuman Frontend Routes
  - name: hanuman-frontend-routes
    service: hanuman-frontend
    paths:
      - /api/v1/hanuman/frontend
    methods:
      - GET
      - POST
    strip_path: true
    preserve_host: false
    tags:
      - hanuman
      - frontend

  # Hanuman Backend Routes
  - name: hanuman-backend-routes
    service: hanuman-backend
    paths:
      - /api/v1/hanuman/backend
    methods:
      - GET
      - POST
    strip_path: true
    preserve_host: false
    tags:
      - hanuman
      - backend

# ===== PLUGINS GLOBAUX =====
plugins:
  # CORS Global
  - name: cors
    config:
      origins:
        - "*"
      methods:
        - GET
        - POST
        - PUT
        - DELETE
        - PATCH
        - OPTIONS
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - Authorization
        - X-Auth-Token
      exposed_headers:
        - X-Auth-Token
      credentials: true
      max_age: 3600
    tags:
      - cors
      - global

  # Rate Limiting Global
  - name: rate-limiting
    config:
      minute: 1000
      hour: 10000
      day: 100000
      policy: local
      fault_tolerant: true
      hide_client_headers: false
    tags:
      - rate-limiting
      - global

  # Request Size Limiting
  - name: request-size-limiting
    config:
      allowed_payload_size: 10
    tags:
      - security
      - global

  # Prometheus Metrics
  - name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
    tags:
      - monitoring
      - prometheus
      - global

  # Request/Response Logging
  - name: http-log
    config:
      http_endpoint: http://logstash:5000/kong-logs
      method: POST
      timeout: 1000
      keepalive: 1000
    tags:
      - logging
      - global

# ===== CONSUMERS & AUTHENTICATION =====
consumers:
  # API Consumer pour Frontend
  - username: frontend-app
    custom_id: frontend-app-001
    tags:
      - frontend
      - web

  # API Consumer pour Mobile
  - username: mobile-app
    custom_id: mobile-app-001
    tags:
      - mobile
      - app

  # API Consumer pour Hanuman
  - username: hanuman-system
    custom_id: hanuman-system-001
    tags:
      - hanuman
      - internal

  # API Consumer pour Services Internes
  - username: internal-services
    custom_id: internal-services-001
    tags:
      - internal
      - microservices

# ===== API KEYS =====
key-auths:
  # Frontend API Key
  - consumer: frontend-app
    key: frontend-api-key-2025-secure
    tags:
      - frontend
      - auth

  # Mobile API Key
  - consumer: mobile-app
    key: mobile-api-key-2025-secure
    tags:
      - mobile
      - auth

  # Hanuman API Key
  - consumer: hanuman-system
    key: hanuman-api-key-2025-secure
    tags:
      - hanuman
      - auth

  # Internal Services API Key
  - consumer: internal-services
    key: internal-services-api-key-2025-secure
    tags:
      - internal
      - auth
