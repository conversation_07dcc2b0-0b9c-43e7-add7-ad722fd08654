# Prometheus Configuration - Sprint 17 Unified Monitoring
# Date: 25 Juin 2025
# Objectif: Monitoring centralisé de tous les microservices

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'unified-microservices'
    environment: 'production'

# Alerting configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 30s

  # Kong API Gateway
  - job_name: 'kong-gateway'
    static_configs:
      - targets: ['kong-gateway:8001']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # ===== INFRASTRUCTURE SERVICES =====
  
  # Kafka Monitoring
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9092']
    metrics_path: /metrics
    scrape_interval: 30s

  # Redis Monitoring
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: /metrics
    scrape_interval: 30s

  # PostgreSQL Monitoring
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: /metrics
    scrape_interval: 30s

  # MongoDB Monitoring
  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb:27017']
    metrics_path: /metrics
    scrape_interval: 30s

  # Weaviate Vector Database
  - job_name: 'weaviate'
    static_configs:
      - targets: ['weaviate:8080']
    metrics_path: /v1/meta
    scrape_interval: 30s

  # ===== HANUMAN AGENTS =====
  
  # Cortex Central - Brain
  - job_name: 'hanuman-cortex-central'
    static_configs:
      - targets: ['cortex-central:8080']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'cortex-central'
      - source_labels: [__address__]
        target_label: agent_type
        replacement: 'orchestrator'

  # Agent Frontend
  - job_name: 'hanuman-agent-frontend'
    static_configs:
      - targets: ['agent-frontend:3001']
    metrics_path: /metrics
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: agent_type
        replacement: 'frontend'

  # Agent Backend
  - job_name: 'hanuman-agent-backend'
    static_configs:
      - targets: ['agent-backend:3002']
    metrics_path: /metrics
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: agent_type
        replacement: 'backend'

  # Agent DevOps
  - job_name: 'hanuman-agent-devops'
    static_configs:
      - targets: ['agent-devops:3003']
    metrics_path: /metrics
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: agent_type
        replacement: 'devops'

  # Agent QA
  - job_name: 'hanuman-agent-qa'
    static_configs:
      - targets: ['agent-qa:3004']
    metrics_path: /metrics
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: agent_type
        replacement: 'qa'

  # ===== PROJET RB2 SERVICES =====
  
  # Backend NestJS Principal
  - job_name: 'rb2-backend-nestjs'
    static_configs:
      - targets: ['rb2-backend:3000']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s
    relabel_configs:
      - source_labels: [__address__]
        target_label: service_type
        replacement: 'backend-api'

  # Social Platform Video
  - job_name: 'rb2-social-platform'
    static_configs:
      - targets: ['social-platform-video:3002']
    metrics_path: /metrics
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: service_type
        replacement: 'social-media'

  # Messaging Service
  - job_name: 'rb2-messaging-service'
    static_configs:
      - targets: ['messaging-service:5178']
    metrics_path: /metrics
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: service_type
        replacement: 'messaging'

  # Agent IA
  - job_name: 'rb2-agent-ia'
    static_configs:
      - targets: ['agent-ia:8000']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: service_type
        replacement: 'ai-service'

  # Analyzer Service
  - job_name: 'rb2-analyzer'
    static_configs:
      - targets: ['analyzer:8080']
    metrics_path: /metrics
    scrape_interval: 30s
    relabel_configs:
      - source_labels: [__address__]
        target_label: service_type
        replacement: 'analytics'

  # Financial Management
  - job_name: 'rb2-financial-management'
    static_configs:
      - targets: ['financial-management:4000']
    metrics_path: /metrics
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: service_type
        replacement: 'financial'

  # Security Service
  - job_name: 'rb2-security-service'
    static_configs:
      - targets: ['security-service:5000']
    metrics_path: /metrics
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: service_type
        replacement: 'security'

  # ===== CUSTOM METRICS ENDPOINTS =====
  
  # Node Exporter (if deployed)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # cAdvisor (Container metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # Blackbox Exporter (Health checks)
  - job_name: 'blackbox'
    static_configs:
      - targets: ['blackbox-exporter:9115']
    scrape_interval: 60s

# Remote write configuration (for long-term storage)
remote_write:
  - url: "http://prometheus-remote-storage:9201/write"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Remote read configuration
remote_read:
  - url: "http://prometheus-remote-storage:9201/read"
    read_recent: true
