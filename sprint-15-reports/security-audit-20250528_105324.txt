[0;34m[2025-05-28 10:57:17][0m 🚀 Starting Sprint 10 Comprehensive Security Audit...
[0;34m[2025-05-28 10:57:17][0m Project Root: /Users/<USER>/Desktop/Agentic-Coding-Framework- RB2
[0;34m[2025-05-28 10:57:17][0m Audit Report: /Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/security-audit-reports/security_audit_20250528_105717.md
[0;34m[2025-05-28 10:57:17][0m 🔍 Starting Static Application Security Testing (SAST)...
[0;34m[2025-05-28 10:57:17][0m Running npm audit for dependency vulnerabilities...
# npm audit report

body-parser  <1.20.3
Severity: high
body-parser vulnerable to denial of service when url encoding is enabled - https://github.com/advisories/GHSA-qwcr-r2fm-qrc7
fix available via `npm audit fix --force`
Will install express@4.21.2, which is outside the stated dependency range
Projet-RB2/Security/node_modules/body-parser
  express  <=4.21.1 || 5.0.0-alpha.1 - 5.0.0
  Depends on vulnerable versions of body-parser
  Depends on vulnerable versions of cookie
  Depends on vulnerable versions of path-to-regexp
  Depends on vulnerable versions of send
  Depends on vulnerable versions of serve-static
  Projet-RB2/Security/node_modules/express

cookie  <0.7.0
cookie accepts cookie name, path, and domain with out of bounds characters - https://github.com/advisories/GHSA-pxg6-pf52-xh8x
fix available via `npm audit fix --force`
Will install express@4.21.2, which is outside the stated dependency range
Projet-RB2/Security/node_modules/express/node_modules/cookie

esbuild  <=0.24.2
Severity: moderate
esbuild enables any website to send any requests to the development server and read the response - https://github.com/advisories/GHSA-67mh-4wv8-2f99
fix available via `npm audit fix --force`
Will install vite@6.3.5, which is a breaking change
node_modules/vite-node/node_modules/esbuild
node_modules/vite/node_modules/esbuild
node_modules/vitest/node_modules/esbuild
Projet-RB2/Financial-Management/node_modules/esbuild
  vite  0.11.0 - 6.1.6
  Depends on vulnerable versions of esbuild
  node_modules/vite
  node_modules/vite-node/node_modules/vite
  node_modules/vitest/node_modules/vite
  Projet-RB2/Financial-Management/node_modules/vite
    vite-node  <=2.2.0-beta.2
    Depends on vulnerable versions of vite
    node_modules/vite-node
    Projet-RB2/Security/node_modules/vite-node
      vitest  0.0.1 - 0.0.12 || 0.0.29 - 0.0.122 || 0.3.3 - 2.2.0-beta.2
      Depends on vulnerable versions of vite
      Depends on vulnerable versions of vite-node
      node_modules/vitest
      Projet-RB2/Security/node_modules/vitest


micromatch  <4.0.8
Severity: moderate
Regular Expression Denial of Service (ReDoS) in micromatch - https://github.com/advisories/GHSA-952p-6rrq-rcjv
fix available via `npm audit fix`
node_modules/micromatch
  lint-staged  7.0.0 - 8.2.1 || 13.3.0 - 15.2.4
  Depends on vulnerable versions of micromatch
  hanuman-unified/node_modules/lint-staged

nth-check  <2.0.1
Severity: high
Inefficient Regular Expression Complexity in nth-check - https://github.com/advisories/GHSA-rp65-9cf3-cjxr
fix available via `npm audit fix --force`
Will install react-scripts@3.0.1, which is a breaking change
node_modules/svgo/node_modules/nth-check
  css-select  <=3.1.0
  Depends on vulnerable versions of nth-check
  node_modules/svgo/node_modules/css-select
    svgo  1.0.0 - 1.3.2
    Depends on vulnerable versions of css-select
    node_modules/svgo
      @svgr/plugin-svgo  <=5.5.0
      Depends on vulnerable versions of svgo
      node_modules/@svgr/plugin-svgo
        @svgr/webpack  4.0.0 - 5.5.0
        Depends on vulnerable versions of @svgr/plugin-svgo
        node_modules/@svgr/webpack
          react-scripts  >=2.1.4
          Depends on vulnerable versions of @svgr/webpack
          Depends on vulnerable versions of resolve-url-loader
          node_modules/react-scripts

path-to-regexp  <=0.1.11
Severity: high
Unpatched `path-to-regexp` ReDoS in 0.1.x - https://github.com/advisories/GHSA-rhx6-c78j-4q9w
path-to-regexp outputs backtracking regular expressions - https://github.com/advisories/GHSA-9wv6-86v2-598j
fix available via `npm audit fix --force`
Will install express@4.21.2, which is outside the stated dependency range
Projet-RB2/Security/node_modules/express/node_modules/path-to-regexp

postcss  <8.4.31
Severity: moderate
PostCSS line return parsing error - https://github.com/advisories/GHSA-7fh5-64p2-3v2j
fix available via `npm audit fix --force`
Will install react-scripts@3.0.1, which is a breaking change
node_modules/react-scripts/node_modules/resolve-url-loader/node_modules/postcss
  resolve-url-loader  0.0.1-experiment-postcss || 3.0.0-alpha.1 - 4.0.0
  Depends on vulnerable versions of postcss
  node_modules/react-scripts/node_modules/resolve-url-loader

send  <0.19.0
send vulnerable to template injection that can lead to XSS - https://github.com/advisories/GHSA-m6fv-jmcg-4jfg
fix available via `npm audit fix --force`
Will install express@4.21.2, which is outside the stated dependency range
Projet-RB2/Security/node_modules/send
  serve-static  <=1.16.0
  Depends on vulnerable versions of send
  Projet-RB2/Security/node_modules/serve-static


tar-fs  3.0.0 - 3.0.6
Severity: high
tar-fs Vulnerable to Link Following and Path Traversal via Extracting a Crafted tar File - https://github.com/advisories/GHSA-pq67-2wwv-3xjx
fix available via `npm audit fix`
node_modules/@puppeteer/browsers/node_modules/tar-fs
  @puppeteer/browsers  1.4.2 - 2.2.3
  Depends on vulnerable versions of tar-fs
  node_modules/@puppeteer/browsers
    puppeteer  18.2.0 - 22.13.0
    Depends on vulnerable versions of @puppeteer/browsers
    Depends on vulnerable versions of puppeteer-core
    node_modules/puppeteer
    puppeteer-core  11.0.0 - 22.13.0
    Depends on vulnerable versions of @puppeteer/browsers
    Depends on vulnerable versions of ws
    node_modules/puppeteer/node_modules/puppeteer-core


ws  8.0.0 - 8.17.0
Severity: high
ws affected by a DoS when handling a request with many HTTP headers - https://github.com/advisories/GHSA-3h5v-q93c-6h6q
fix available via `npm audit fix`
node_modules/puppeteer/node_modules/ws

25 vulnerabilities (3 low, 7 moderate, 14 high, 1 critical)

To address issues that do not require attention, run:
  npm audit fix

To address all issues (including breaking changes), run:
  npm audit fix --force
[1;33m[WARNING][0m NPM audit found vulnerabilities
[0;34m[2025-05-28 10:57:22][0m Running ESLint security analysis...
