version: '3.8'

# 🚀 CONFIGURATION DOCKER COMPOSE UNIFIÉE
# Hanuman-Unified + Projet-RB2 + Vimana
# Optimisation Ressources Matérielles

# Variables communes réutilisables
x-common-variables: &common-env
  KAFKA_BROKERS: kafka:9092
  REDIS_URL: redis://redis:6379
  WEAVIATE_URL: http://weaviate:8080
  POSTGRES_URL: ****************************************/unified_db
  MONGODB_URI: mongodb://mongodb:27017/unified_db
  NODE_ENV: production
  LOG_LEVEL: info

# Limites de ressources communes
x-resource-limits-small: &resource-limits-small
  deploy:
    resources:
      limits:
        memory: 512M
        cpus: '0.5'
      reservations:
        memory: 256M
        cpus: '0.25'

x-resource-limits-medium: &resource-limits-medium
  deploy:
    resources:
      limits:
        memory: 1G
        cpus: '1.0'
      reservations:
        memory: 512M
        cpus: '0.5'

x-resource-limits-large: &resource-limits-large
  deploy:
    resources:
      limits:
        memory: 2G
        cpus: '2.0'
      reservations:
        memory: 1G
        cpus: '1.0'

# Configuration de santé commune
x-healthcheck-web: &healthcheck-web
  test: ["CMD", "curl", "-f", "http://localhost:${PORT:-3000}/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s

services:
  # ===== API GATEWAY - SPRINT 17 =====

  # 🚪 Kong Gateway - Point d'Entrée Unifié
  kong-gateway:
    image: kong:3.4-alpine
    container_name: kong-gateway-unified
    ports:
      - "8000:8000"  # Proxy
      - "8001:8001"  # Admin API
    environment:
      KONG_DATABASE: 'off'
      KONG_DECLARATIVE_CONFIG: /kong/declarative/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: '0.0.0.0:8001'
      KONG_PROXY_LISTEN: '0.0.0.0:8000'
      KONG_NGINX_WORKER_PROCESSES: '2'
    volumes:
      - ./infrastructure/api-gateway/kong-unified.yaml:/kong/declarative/kong.yml:ro
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
    <<: *resource-limits-medium

  # ===== INFRASTRUCTURE PARTAGÉE =====

  # 📨 Kafka - Système Nerveux Principal
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka-unified
    ports:
      - "9092:9092"
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 6
      KAFKA_DEFAULT_REPLICATION_FACTOR: 1
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
    depends_on:
      - zookeeper
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - unified-network
    restart: unless-stopped
    <<: *resource-limits-medium

  # 🔗 Zookeeper - Coordinateur Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zookeeper-unified
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_INIT_LIMIT: 5
      ZOOKEEPER_SYNC_LIMIT: 2
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - unified-network
    restart: unless-stopped
    <<: *resource-limits-small

  # 🗄️ Redis - Mémoire de Travail Partagée
  redis:
    image: redis:7-alpine
    container_name: redis-unified
    ports:
      - "6379:6379"
    command: >
      redis-server
      --appendonly yes
      --maxmemory 4gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_data:/data
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    <<: *resource-limits-medium

  # 🧠 Weaviate - Mémoire Vectorielle Centrale
  weaviate:
    image: semitechnologies/weaviate:1.21.2
    container_name: weaviate-unified
    ports:
      - "8080:8080"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai'
      CLUSTER_HOSTNAME: 'node1'
      BACKUP_FILESYSTEM_PATH: '/var/lib/weaviate/backups'
    volumes:
      - weaviate_data:/var/lib/weaviate
      - weaviate_backups:/var/lib/weaviate/backups
    networks:
      - unified-network
    restart: unless-stopped
    <<: *resource-limits-large

  # 🐘 PostgreSQL - Base de Données Principale
  postgres:
    image: postgres:15-alpine
    container_name: postgres-unified
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: unified_db
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d unified_db"]
      interval: 30s
      timeout: 10s
      retries: 5
    <<: *resource-limits-medium

  # 🍃 MongoDB - Documents et Messages
  mongodb:
    image: mongo:6-jammy
    container_name: mongodb-unified
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: unified_db
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/unified_db --quiet
      interval: 30s
      timeout: 10s
      retries: 5
    <<: *resource-limits-medium

  # ===== HANUMAN BRAIN - ORGANISME IA =====

  # 🧠 Cortex Central - Orchestrateur Cognitif
  cortex-central:
    build:
      context: ./hanuman-unified/brain/cortex-central
      dockerfile: Dockerfile
    container_name: cortex-central
    ports:
      - "8081:8080"
    environment:
      <<: *common-env
      PORT: 8080
      NEURAL_NETWORK_MODE: active
      DECISION_ENGINE: enabled
      MEMORY_STORE: weaviate
      SYNAPTIC_COMMUNICATION: kafka
      AGENT_REGISTRY_ENABLED: true
    volumes:
      - cortex_memory:/app/memory
      - cortex_logs:/app/logs
      - shared_workspace:/app/workspace
    depends_on:
      - kafka
      - redis
      - weaviate
      - postgres
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      <<: *healthcheck-web
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
    <<: *resource-limits-large

  # 🎯 Agent Frontend - Spécialiste UI/UX
  agent-frontend:
    build:
      context: ./hanuman-unified/agents/frontend
      dockerfile: Dockerfile
    container_name: agent-frontend
    ports:
      - "3001:3001"
    environment:
      <<: *common-env
      PORT: 3001
      CORTEX_CENTRAL_URL: http://cortex-central:8080
      SPECIALIZATION: ui-ux
      AGENT_TYPE: frontend
      NEURAL_ID: agent-frontend-001
      MEMORY_STORE: pinecone-frontend
    volumes:
      - frontend_workspace:/app/workspace
      - shared_workspace:/app/shared
    depends_on:
      - cortex-central
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      <<: *healthcheck-web
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
    <<: *resource-limits-medium

  # ⚙️ Agent Backend - Architecte API
  agent-backend:
    build:
      context: ./hanuman-unified/agents/backend
      dockerfile: Dockerfile
    container_name: agent-backend
    ports:
      - "3002:3002"
    environment:
      <<: *common-env
      PORT: 3002
      CORTEX_CENTRAL_URL: http://cortex-central:8080
      SPECIALIZATION: api-architecture
      AGENT_TYPE: backend
      NEURAL_ID: agent-backend-001
      SECURITY_MODULE: enabled
    volumes:
      - backend_workspace:/app/workspace
      - shared_workspace:/app/shared
    depends_on:
      - cortex-central
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      <<: *healthcheck-web
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
    <<: *resource-limits-medium

  # 🔧 Agent DevOps - Automatiseur Infrastructure
  agent-devops:
    build:
      context: ./hanuman-unified/agents/devops
      dockerfile: Dockerfile
    container_name: agent-devops
    ports:
      - "3003:3003"
    environment:
      <<: *common-env
      PORT: 3003
      CORTEX_CENTRAL_URL: http://cortex-central:8080
      SPECIALIZATION: infrastructure
      AGENT_TYPE: devops
      NEURAL_ID: agent-devops-001
      INFRASTRUCTURE_MODE: kubernetes
    volumes:
      - devops_workspace:/app/workspace
      - shared_workspace:/app/shared
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - cortex-central
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      <<: *healthcheck-web
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
    <<: *resource-limits-medium

  # 🧪 Agent QA - Testeur Intelligent
  agent-qa:
    build:
      context: ./hanuman-unified/agents/qa
      dockerfile: Dockerfile
    container_name: agent-qa
    ports:
      - "3004:3004"
    environment:
      <<: *common-env
      PORT: 3004
      CORTEX_CENTRAL_URL: http://cortex-central:8080
      SPECIALIZATION: quality-assurance
      AGENT_TYPE: qa
      NEURAL_ID: agent-qa-001
      TESTING_FRAMEWORK: jest-cypress
    volumes:
      - qa_workspace:/app/workspace
      - shared_workspace:/app/shared
    depends_on:
      - cortex-central
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      <<: *healthcheck-web
      test: ["CMD", "curl", "-f", "http://localhost:3004/health"]
    <<: *resource-limits-medium

  # ===== MONITORING & OBSERVABILITÉ - SPRINT 17 =====

  # 📊 Prometheus - Collecte de Métriques
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: prometheus-unified
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
    <<: *resource-limits-medium

  # 📈 Grafana - Visualisation des Métriques
  grafana:
    image: grafana/grafana:10.0.0
    container_name: grafana-unified
    ports:
      - "3100:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin_password_2025
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    networks:
      - unified-network
    depends_on:
      - prometheus
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    <<: *resource-limits-medium

  # 🔍 Jaeger - Tracing Distribué
  jaeger:
    image: jaegertracing/all-in-one:1.47
    container_name: jaeger-unified
    ports:
      - "16686:16686"  # UI
      - "14268:14268"  # HTTP collector
      - "14250:14250"  # gRPC collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - SPAN_STORAGE_TYPE=memory
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:16686/"]
      interval: 30s
      timeout: 10s
      retries: 3
    <<: *resource-limits-medium

  # ===== PROJET RB2 - PLATEFORME MÉTIER =====

  # 🏠 Backend Principal RB2
  rb2-backend:
    build:
      context: ./Projet-RB2/Backend-NestJS
      dockerfile: Dockerfile
    container_name: rb2-backend
    ports:
      - "3000:3000"
    environment:
      <<: *common-env
      PORT: 3000
      DATABASE_URL: ****************************************/unified_db?schema=rb2
      JWT_SECRET: your-jwt-secret-key
      JWT_REFRESH_SECRET: your-refresh-secret-key
      HANUMAN_CORTEX_URL: http://cortex-central:8080
      AI_AGENT_URL: http://agent-ia:8000
    volumes:
      - rb2_uploads:/app/uploads
      - shared_workspace:/app/shared
    depends_on:
      - postgres
      - mongodb
      - redis
      - cortex-central
    networks:
      - unified-network
    restart: unless-stopped
    healthcheck:
      <<: *healthcheck-web
    <<: *resource-limits-large

volumes:
  # Infrastructure
  kafka_data:
  zookeeper_data:
  zookeeper_logs:
  redis_data:
  weaviate_data:
  weaviate_backups:
  postgres_data:
  mongodb_data:
  mongodb_config:

  # Monitoring - Sprint 17
  prometheus_data:
  grafana_data:

  # Hanuman
  cortex_memory:
  cortex_logs:
  frontend_workspace:
  backend_workspace:
  devops_workspace:
  qa_workspace:
  shared_workspace:

  # RB2
  rb2_uploads:

networks:
  unified-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: unified-br0
