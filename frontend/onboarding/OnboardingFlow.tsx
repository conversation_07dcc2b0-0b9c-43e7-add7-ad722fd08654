/**
 * User Onboarding Flow - Sprint 19
 * Optimized conversion funnel with AI-driven personalization
 * Date: 23 Juillet 2025
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/router';
import { useAuth } from '../hooks/useAuth';
import { useAnalytics } from '../hooks/useAnalytics';
import { OnboardingAPI } from '../services/onboardingAPI';

// Types
interface OnboardingStep {
  id: string;
  title: string;
  subtitle: string;
  component: React.ComponentType<any>;
  validation?: (data: any) => boolean;
  optional?: boolean;
}

interface UserProfile {
  role: 'organizer' | 'participant' | 'coach' | 'business';
  goals: string[];
  experience: 'beginner' | 'intermediate' | 'advanced';
  interests: string[];
  preferences: {
    notifications: boolean;
    newsletter: boolean;
    publicProfile: boolean;
  };
}

// Step Components
const WelcomeStep: React.FC<{ onNext: () => void }> = ({ onNext }) => {
  const { track } = useAnalytics();

  useEffect(() => {
    track('onboarding_step_view', { step: 'welcome' });
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="onboarding-step welcome-step"
    >
      <div className="step-content">
        <div className="welcome-icon">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            🌟
          </motion.div>
        </div>
        <h1>Bienvenue sur Retreat And Be!</h1>
        <p className="subtitle">
          Nous allons personnaliser votre expérience en quelques étapes simples.
          Cela ne prendra que 3 minutes.
        </p>
        
        <div className="welcome-benefits">
          <div className="benefit">
            <span className="benefit-icon">🎯</span>
            <span>Recommandations personnalisées par IA</span>
          </div>
          <div className="benefit">
            <span className="benefit-icon">⚡</span>
            <span>Configuration rapide et intuitive</span>
          </div>
          <div className="benefit">
            <span className="benefit-icon">🔒</span>
            <span>Vos données sont sécurisées</span>
          </div>
        </div>

        <button 
          className="cta-button primary large"
          onClick={() => {
            track('onboarding_start', { source: 'welcome_step' });
            onNext();
          }}
        >
          Commencer la personnalisation
        </button>
        
        <p className="step-note">
          Vous pourrez modifier ces paramètres à tout moment dans votre profil.
        </p>
      </div>
    </motion.div>
  );
};

const RoleSelectionStep: React.FC<{ 
  onNext: (data: any) => void;
  data: Partial<UserProfile>;
}> = ({ onNext, data }) => {
  const [selectedRole, setSelectedRole] = useState<string>(data.role || '');
  const { track } = useAnalytics();

  const roles = [
    {
      id: 'organizer',
      title: 'Organisateur de Retraites',
      description: 'Je veux organiser et gérer des retraites bien-être',
      icon: '🏛️',
      features: ['Gestion d\'événements', 'Outils marketing', 'Analytics avancées']
    },
    {
      id: 'participant',
      title: 'Participant',
      description: 'Je cherche à participer à des retraites transformatrices',
      icon: '🧘‍♀️',
      features: ['Recommandations IA', 'Communauté', 'Suivi personnel']
    },
    {
      id: 'coach',
      title: 'Coach/Facilitateur',
      description: 'Je propose mes services lors de retraites',
      icon: '🌟',
      features: ['Profil professionnel', 'Calendrier', 'Outils pédagogiques']
    },
    {
      id: 'business',
      title: 'Entreprise',
      description: 'Je veux organiser des retraites pour mes équipes',
      icon: '🏢',
      features: ['Gestion d\'équipes', 'Reporting RH', 'Packages entreprise']
    }
  ];

  const handleRoleSelect = (roleId: string) => {
    setSelectedRole(roleId);
    track('onboarding_role_select', { role: roleId });
  };

  const handleNext = () => {
    if (selectedRole) {
      track('onboarding_step_complete', { step: 'role_selection', role: selectedRole });
      onNext({ role: selectedRole });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="onboarding-step role-selection-step"
    >
      <div className="step-header">
        <h2>Quel est votre rôle principal ?</h2>
        <p>Cela nous aide à personnaliser votre expérience</p>
      </div>

      <div className="roles-grid">
        {roles.map((role) => (
          <motion.div
            key={role.id}
            className={`role-card ${selectedRole === role.id ? 'selected' : ''}`}
            onClick={() => handleRoleSelect(role.id)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="role-icon">{role.icon}</div>
            <h3>{role.title}</h3>
            <p>{role.description}</p>
            <ul className="role-features">
              {role.features.map((feature, index) => (
                <li key={index}>✓ {feature}</li>
              ))}
            </ul>
          </motion.div>
        ))}
      </div>

      <div className="step-actions">
        <button 
          className="cta-button primary"
          onClick={handleNext}
          disabled={!selectedRole}
        >
          Continuer
        </button>
      </div>
    </motion.div>
  );
};

const GoalsSelectionStep: React.FC<{
  onNext: (data: any) => void;
  data: Partial<UserProfile>;
}> = ({ onNext, data }) => {
  const [selectedGoals, setSelectedGoals] = useState<string[]>(data.goals || []);
  const { track } = useAnalytics();

  const goalsByRole = {
    organizer: [
      'Augmenter mes revenus',
      'Développer ma communauté',
      'Améliorer la qualité de mes retraites',
      'Automatiser ma gestion',
      'Étendre mon offre'
    ],
    participant: [
      'Réduire mon stress',
      'Améliorer mon bien-être',
      'Développer ma spiritualité',
      'Rencontrer des personnes inspirantes',
      'Apprendre de nouvelles pratiques'
    ],
    coach: [
      'Trouver plus de clients',
      'Développer mes compétences',
      'Créer des programmes innovants',
      'Construire ma réputation',
      'Collaborer avec d\'autres experts'
    ],
    business: [
      'Améliorer le bien-être des employés',
      'Renforcer la cohésion d\'équipe',
      'Réduire le turnover',
      'Augmenter la productivité',
      'Attirer les talents'
    ]
  };

  const availableGoals = goalsByRole[data.role as keyof typeof goalsByRole] || goalsByRole.participant;

  const toggleGoal = (goal: string) => {
    const newGoals = selectedGoals.includes(goal)
      ? selectedGoals.filter(g => g !== goal)
      : [...selectedGoals, goal];
    
    setSelectedGoals(newGoals);
    track('onboarding_goal_toggle', { goal, selected: !selectedGoals.includes(goal) });
  };

  const handleNext = () => {
    if (selectedGoals.length > 0) {
      track('onboarding_step_complete', { 
        step: 'goals_selection', 
        goals: selectedGoals,
        goals_count: selectedGoals.length 
      });
      onNext({ goals: selectedGoals });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="onboarding-step goals-selection-step"
    >
      <div className="step-header">
        <h2>Quels sont vos objectifs principaux ?</h2>
        <p>Sélectionnez tous ceux qui vous correspondent (minimum 1)</p>
      </div>

      <div className="goals-grid">
        {availableGoals.map((goal, index) => (
          <motion.div
            key={goal}
            className={`goal-card ${selectedGoals.includes(goal) ? 'selected' : ''}`}
            onClick={() => toggleGoal(goal)}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="goal-checkbox">
              {selectedGoals.includes(goal) && <span>✓</span>}
            </div>
            <span className="goal-text">{goal}</span>
          </motion.div>
        ))}
      </div>

      <div className="step-actions">
        <button 
          className="cta-button primary"
          onClick={handleNext}
          disabled={selectedGoals.length === 0}
        >
          Continuer ({selectedGoals.length} sélectionné{selectedGoals.length > 1 ? 's' : ''})
        </button>
      </div>
    </motion.div>
  );
};

const ExperienceStep: React.FC<{
  onNext: (data: any) => void;
  data: Partial<UserProfile>;
}> = ({ onNext, data }) => {
  const [experience, setExperience] = useState<string>(data.experience || '');
  const { track } = useAnalytics();

  const experienceLevels = [
    {
      id: 'beginner',
      title: 'Débutant',
      description: 'Je découvre le monde des retraites bien-être',
      icon: '🌱',
      details: 'Parfait pour commencer votre parcours'
    },
    {
      id: 'intermediate',
      title: 'Intermédiaire',
      description: 'J\'ai déjà participé à quelques retraites',
      icon: '🌿',
      details: 'Vous connaissez les bases et voulez approfondir'
    },
    {
      id: 'advanced',
      title: 'Avancé',
      description: 'Je suis expérimenté dans le domaine',
      icon: '🌳',
      details: 'Vous cherchez des expériences sophistiquées'
    }
  ];

  const handleNext = () => {
    if (experience) {
      track('onboarding_step_complete', { step: 'experience', level: experience });
      onNext({ experience });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="onboarding-step experience-step"
    >
      <div className="step-header">
        <h2>Quel est votre niveau d'expérience ?</h2>
        <p>Cela nous aide à vous proposer du contenu adapté</p>
      </div>

      <div className="experience-options">
        {experienceLevels.map((level) => (
          <motion.div
            key={level.id}
            className={`experience-card ${experience === level.id ? 'selected' : ''}`}
            onClick={() => {
              setExperience(level.id);
              track('onboarding_experience_select', { level: level.id });
            }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="experience-icon">{level.icon}</div>
            <h3>{level.title}</h3>
            <p>{level.description}</p>
            <span className="experience-details">{level.details}</span>
          </motion.div>
        ))}
      </div>

      <div className="step-actions">
        <button 
          className="cta-button primary"
          onClick={handleNext}
          disabled={!experience}
        >
          Continuer
        </button>
      </div>
    </motion.div>
  );
};

const CompletionStep: React.FC<{
  onComplete: () => void;
  data: UserProfile;
}> = ({ onComplete, data }) => {
  const { track } = useAnalytics();
  const [isCreatingProfile, setIsCreatingProfile] = useState(false);

  const handleComplete = async () => {
    setIsCreatingProfile(true);
    
    try {
      // Save user profile
      await OnboardingAPI.saveProfile(data);
      
      track('onboarding_complete', {
        role: data.role,
        goals_count: data.goals.length,
        experience: data.experience
      });
      
      // Trigger success animation
      setTimeout(() => {
        onComplete();
      }, 1500);
      
    } catch (error) {
      console.error('Error saving profile:', error);
      track('onboarding_error', { error: error.message });
      setIsCreatingProfile(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="onboarding-step completion-step"
    >
      <div className="completion-content">
        <motion.div
          className="success-icon"
          animate={{ rotate: 360 }}
          transition={{ duration: 1 }}
        >
          🎉
        </motion.div>
        
        <h2>Parfait ! Votre profil est prêt</h2>
        <p>
          Nous avons personnalisé votre expérience en fonction de vos préférences.
          Vous êtes maintenant prêt à découvrir Retreat And Be !
        </p>

        <div className="profile-summary">
          <h3>Votre profil :</h3>
          <div className="summary-item">
            <strong>Rôle :</strong> {data.role}
          </div>
          <div className="summary-item">
            <strong>Objectifs :</strong> {data.goals.join(', ')}
          </div>
          <div className="summary-item">
            <strong>Expérience :</strong> {data.experience}
          </div>
        </div>

        <button 
          className="cta-button primary large"
          onClick={handleComplete}
          disabled={isCreatingProfile}
        >
          {isCreatingProfile ? (
            <>
              <span className="spinner"></span>
              Création de votre profil...
            </>
          ) : (
            'Accéder à mon dashboard'
          )}
        </button>
      </div>
    </motion.div>
  );
};

// Main Onboarding Component
export const OnboardingFlow: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [userData, setUserData] = useState<Partial<UserProfile>>({});
  const router = useRouter();
  const { user } = useAuth();
  const { track } = useAnalytics();

  const steps: OnboardingStep[] = [
    { id: 'welcome', title: 'Bienvenue', subtitle: '', component: WelcomeStep },
    { id: 'role', title: 'Votre rôle', subtitle: '', component: RoleSelectionStep },
    { id: 'goals', title: 'Vos objectifs', subtitle: '', component: GoalsSelectionStep },
    { id: 'experience', title: 'Votre expérience', subtitle: '', component: ExperienceStep },
    { id: 'completion', title: 'Terminé', subtitle: '', component: CompletionStep }
  ];

  useEffect(() => {
    track('onboarding_flow_start', { user_id: user?.id });
  }, []);

  const handleNext = (stepData?: any) => {
    if (stepData) {
      setUserData(prev => ({ ...prev, ...stepData }));
    }
    setCurrentStep(prev => prev + 1);
  };

  const handleComplete = () => {
    track('onboarding_flow_complete', { 
      user_id: user?.id,
      total_steps: steps.length,
      completion_time: Date.now()
    });
    
    router.push('/dashboard?welcome=true');
  };

  const CurrentStepComponent = steps[currentStep]?.component;

  return (
    <div className="onboarding-container">
      <div className="onboarding-progress">
        <div className="progress-bar">
          <div 
            className="progress-fill"
            style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
          />
        </div>
        <span className="progress-text">
          Étape {currentStep + 1} sur {steps.length}
        </span>
      </div>

      <AnimatePresence mode="wait">
        {CurrentStepComponent && (
          <CurrentStepComponent
            key={currentStep}
            onNext={handleNext}
            onComplete={handleComplete}
            data={userData}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default OnboardingFlow;
