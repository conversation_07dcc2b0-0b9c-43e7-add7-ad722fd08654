# 📝 EXEMPLE DE CONFIGURATION - SPRINT 15
# Copiez ce fichier vers .env et remplissez avec vos vraies valeurs

# API Keys (remplacez par vos vraies clés)
API_KEY_OPENAI=sk-your-openai-key-here
API_KEY_STRIPE=sk_test_your-stripe-key-here
API_KEY_SENDGRID=SG.your-sendgrid-key-here

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/retreatandbe
MONGODB_URI=mongodb://localhost:27017/retreatandbe
REDIS_URL=redis://localhost:6379

# JWT & Auth (générez des secrets forts)
JWT_SECRET=your-super-secret-jwt-key-here
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here
SESSION_SECRET=your-super-secret-session-key-here

# External Services
SENTRY_DSN=https://your-sentry-dsn-here
CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name

# Encryption (générez des clés fortes)
ENCRYPTION_KEY=your-32-char-encryption-key-here
HASH_SALT=your-hash-salt-here

# Environment
NODE_ENV=development
PORT=3000
