name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd e2e-tests
        npm ci
    
    - name: Install Playwright browsers
      run: |
        cd e2e-tests
        npx playwright install --with-deps ${{ matrix.browser }}
    
    - name: Run E2E tests
      run: |
        cd e2e-tests
        npx playwright test --project=${{ matrix.browser }}
      env:
        BASE_URL: ${{ secrets.BASE_URL || 'http://localhost:3000' }}
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report-${{ matrix.browser }}
        path: e2e-tests/playwright-report/
        retention-days: 30
