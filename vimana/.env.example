# 🔐 VIMANA FRAMEWORK - CONFIGURATION ENVIRONNEMENT
# Sprint 15 - Migration des secrets

# Core Configuration
NODE_ENV=development
PORT=3005
API_PREFIX=api/v1/vimana

# AI Services
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/vimana_dev
REDIS_URL=redis://localhost:6379

# Security
JWT_SECRET=your-vimana-jwt-secret-here
ENCRYPTION_KEY=your-vimana-encryption-key-here

# Vimana Specific
SPIRITUAL_MODE=true
BRAHMA_CREATOR_ENABLED=true
VISHNU_PRESERVER_ENABLED=true
SHIVA_TRANSFORMER_ENABLED=true

# Integration
HANUMAN_URL=http://localhost:3004
IDE_INTEGRATION=true

# Monitoring
SENTRY_DSN=https://your-vimana-sentry-dsn-here
LOG_LEVEL=info
