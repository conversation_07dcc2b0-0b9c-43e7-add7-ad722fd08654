# npm audit report

@nestjs/common  <=10.4.15
Severity: high
nest allows a remote attacker to execute arbitrary code via the Content-Type header - https://github.com/advisories/GHSA-cj7v-w2c7-cp7c
Depends on vulnerable versions of axios
fix available via `npm audit fix --force`
Will install @nestjs/common@11.1.2, which is a breaking change
vimana/node_modules/@nestjs/common
  @nestjs/config  <=2.3.4
  Depends on vulnerable versions of @nestjs/common
  vimana/node_modules/@nestjs/config
  @nestjs/core  <=10.4.1
  Depends on vulnerable versions of @nestjs/common
  Depends on vulnerable versions of @nestjs/platform-express
  Depends on vulnerable versions of path-to-regexp
  vimana/node_modules/@nestjs/core
    @nestjs/platform-express  <=10.4.17 || 11.0.0-next.1 - 11.1.1
    Depends on vulnerable versions of @nestjs/common
    Depends on vulnerable versions of @nestjs/core
    Depends on vulnerable versions of body-parser
    Depends on vulnerable versions of express
    Depends on vulnerable versions of multer
    vimana/node_modules/@nestjs/platform-express
      @nestjs/testing  <=9.4.3
      Depends on vulnerable versions of @nestjs/common
      Depends on vulnerable versions of @nestjs/core
      Depends on vulnerable versions of @nestjs/platform-express
      vimana/node_modules/@nestjs/testing
    @nestjs/swagger  <=7.4.0
    Depends on vulnerable versions of @nestjs/common
    Depends on vulnerable versions of @nestjs/core
    Depends on vulnerable versions of @nestjs/mapped-types
    Depends on vulnerable versions of path-to-regexp
    vimana/node_modules/@nestjs/swagger
  @nestjs/jwt  <=10.0.3
  Depends on vulnerable versions of @nestjs/common
  Depends on vulnerable versions of jsonwebtoken
  vimana/node_modules/@nestjs/jwt
  @nestjs/mapped-types  <=1.2.2
  Depends on vulnerable versions of @nestjs/common
  Depends on vulnerable versions of class-validator
  vimana/node_modules/@nestjs/mapped-types
  @nestjs/passport  <=9.0.3
  Depends on vulnerable versions of @nestjs/common
  vimana/node_modules/@nestjs/passport


axios  <=0.29.0
Severity: high
Axios Cross-Site Request Forgery Vulnerability - https://github.com/advisories/GHSA-wf5p-g6vw-rhxx
axios Requests Vulnerable To Possible SSRF and Credential Leakage via Absolute URL - https://github.com/advisories/GHSA-jr5f-v2jv-69x6
fix available via `npm audit fix --force`
Will install @nestjs/common@11.1.2, which is a breaking change
vimana/node_modules/@nestjs/common/node_modules/axios

body-parser  <1.20.3
Severity: high
body-parser vulnerable to denial of service when url encoding is enabled - https://github.com/advisories/GHSA-qwcr-r2fm-qrc7
fix available via `npm audit fix --force`
Will install @nestjs/platform-express@11.1.2, which is a breaking change
vimana/node_modules/body-parser
  express  <=4.21.1 || 5.0.0-alpha.1 - 5.0.0
  Depends on vulnerable versions of body-parser
  Depends on vulnerable versions of cookie
  Depends on vulnerable versions of path-to-regexp
  Depends on vulnerable versions of send
  Depends on vulnerable versions of serve-static
  vimana/node_modules/express

class-validator  <0.14.0
Severity: critical
SQL Injection and Cross-site Scripting in class-validator - https://github.com/advisories/GHSA-fj58-h2fr-3pp2
fix available via `npm audit fix --force`
Will install class-validator@0.14.2, which is a breaking change
vimana/node_modules/class-validator

cookie  <0.7.0
cookie accepts cookie name, path, and domain with out of bounds characters - https://github.com/advisories/GHSA-pxg6-pf52-xh8x
fix available via `npm audit fix`
vimana/node_modules/cookie


jsonwebtoken  <=8.5.1
Severity: high
jsonwebtoken unrestricted key type could lead to legacy keys usage  - https://github.com/advisories/GHSA-8cf7-32gw-wr33
jsonwebtoken's insecure implementation of key retrieval function could lead to Forgeable Public/Private Tokens from RSA to HMAC - https://github.com/advisories/GHSA-hjrf-2m68-5959
jsonwebtoken vulnerable to signature validation bypass due to insecure default algorithm in jwt.verify() - https://github.com/advisories/GHSA-qwph-4952-7xr6
fix available via `npm audit fix --force`
Will install @nestjs/jwt@11.0.0, which is a breaking change
vimana/node_modules/jsonwebtoken

micromatch  <4.0.8
Severity: moderate
Regular Expression Denial of Service (ReDoS) in micromatch - https://github.com/advisories/GHSA-952p-6rrq-rcjv
fix available via `npm audit fix`
node_modules/micromatch

multer  >=1.4.4-lts.1 <2.0.0
Severity: high
Multer vulnerable to Denial of Service from maliciously crafted requests - https://github.com/advisories/GHSA-4pg4-qvpc-4q3h
fix available via `npm audit fix --force`
Will install @nestjs/platform-express@11.1.2, which is a breaking change
vimana/node_modules/multer

passport  <0.6.0
Severity: moderate
Passport vulnerable to session regeneration when a users logs in or out - https://github.com/advisories/GHSA-v923-w3x8-wh69
fix available via `npm audit fix --force`
Will install passport@0.7.0, which is a breaking change
vimana/node_modules/passport

path-to-regexp  <=0.1.11 || 2.0.0 - 3.2.0
Severity: high
Unpatched `path-to-regexp` ReDoS in 0.1.x - https://github.com/advisories/GHSA-rhx6-c78j-4q9w
path-to-regexp outputs backtracking regular expressions - https://github.com/advisories/GHSA-9wv6-86v2-598j
path-to-regexp outputs backtracking regular expressions - https://github.com/advisories/GHSA-9wv6-86v2-598j
fix available via `npm audit fix --force`
Will install @nestjs/swagger@11.2.0, which is a breaking change
vimana/node_modules/express/node_modules/path-to-regexp
vimana/node_modules/path-to-regexp

send  <0.19.0
send vulnerable to template injection that can lead to XSS - https://github.com/advisories/GHSA-m6fv-jmcg-4jfg
fix available via `npm audit fix`
vimana/node_modules/send
  serve-static  <=1.16.0
  Depends on vulnerable versions of send
  vimana/node_modules/serve-static


webpack  5.0.0-alpha.0 - 5.93.0
Severity: critical
Cross-realm object access in Webpack 5 - https://github.com/advisories/GHSA-hc6q-2mpp-qw7j
Webpack's AutoPublicPathRuntimeModule has a DOM Clobbering Gadget that leads to XSS - https://github.com/advisories/GHSA-4vvj-4cpr-p986
fix available via `npm audit fix --force`
Will install @nestjs/cli@11.0.7, which is a breaking change
vimana/node_modules/webpack
  @nestjs/cli  7.5.1-next.1 || 7.5.2-next.2 - 9.2.0
  Depends on vulnerable versions of webpack
  vimana/node_modules/@nestjs/cli

23 vulnerabilities (3 low, 6 moderate, 10 high, 4 critical)

To address issues that do not require attention, run:
  npm audit fix

To address all issues (including breaking changes), run:
  npm audit fix --force
