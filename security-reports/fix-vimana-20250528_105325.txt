npm warn using --force Recommended protections disabled.
npm warn audit Updating @nestjs/common to 11.1.2, which is a SemVer major change.
npm warn audit Updating @nestjs/core to 11.1.2, which is a SemVer major change.
npm warn audit Updating class-validator to 0.14.2, which is a SemVer major change.
npm warn audit Updating passport to 0.7.0, which is a SemVer major change.
npm warn audit Updating @nestjs/jwt to 11.0.0, which is a SemVer major change.
npm warn audit Updating @nestjs/passport to 11.0.5, which is a SemVer major change.
npm warn audit Updating @nestjs/config to 4.0.2, which is a SemVer major change.
npm warn audit Updating @nestjs/swagger to 11.2.0, which is a SemVer major change.
npm warn audit Updating @nestjs/platform-express to 11.1.2, which is a SemVer major change.
npm warn audit Updating @nestjs/testing to 11.1.2, which is a SemVer major change.
npm warn audit Updating @nestjs/cli to 11.0.7, which is a SemVer major change.

added 65 packages, removed 104 packages, changed 26 packages, and audited 3933 packages in 38s

562 packages are looking for funding
  run `npm fund` for details

# npm audit report

micromatch  <4.0.8
Severity: moderate
Regular Expression Denial of Service (ReDoS) in micromatch - https://github.com/advisories/GHSA-952p-6rrq-rcjv
fix available via `npm audit fix`
node_modules/micromatch

1 moderate severity vulnerability

To address all issues, run:
  npm audit fix
