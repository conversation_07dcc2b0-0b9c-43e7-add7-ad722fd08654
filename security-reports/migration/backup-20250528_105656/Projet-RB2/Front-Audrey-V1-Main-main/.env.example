# 🔐 VARIABLES D'ENVIRONNEMENT SÉCURISÉES
# Généré automatiquement par le script de migration des secrets
# Date: 2025-05-28T11:42:27.573Z

# ⚠️  IMPORTANT: Ne jamais commiter ce fichier !
# Utiliser .env.example comme template

# Backend Configuration
API_KEY=your-api-key-here
DATABASE_URL=postgresql://user:password@localhost:5432/database
JWT_SECRET=your-super-secret-jwt-key-here
NODE_ENV=development
PORT=3000
REDIS_URL=redis://localhost:6379

# Frontend Configuration - Phase 3 Optimisation
VITE_API_URL=http://localhost:3001
VITE_API_TIMEOUT=10000
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here
VITE_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here

# CDN Configuration - Phase 3 Sprint 3.1
VITE_CDN_PRIMARY=https://cdn.retreatandbe.com
VITE_CDN_SECONDARY=https://backup-cdn.retreatandbe.com
VITE_CDN_US=https://us-cdn.retreatandbe.com
VITE_CDN_EU=https://eu-cdn.retreatandbe.com
VITE_CDN_AP=https://ap-cdn.retreatandbe.com
VITE_USE_CDN=true

# Performance Configuration
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_IMAGE_OPTIMIZATION=true
VITE_IMAGE_QUALITY=85
VITE_ENABLE_WEBP=true
VITE_ENABLE_AVIF=true

# Build Configuration
VITE_BUILD_ANALYZE=false
VITE_BUILD_SOURCEMAP=false
VITE_ENABLE_COMPRESSION=true

# Monitoring Configuration
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_PERFORMANCE_SAMPLE_RATE=0.1

# Feature Flags
VITE_FEATURE_ADVANCED_CACHING=true
VITE_FEATURE_PRELOADING=true
VITE_FEATURE_SERVICE_WORKER=true

# AWS Configuration (pour CDN setup)
AWS_REGION=us-east-1
CDN_BUCKET_NAME=retreatandbe-assets-optimized
CDN_DOMAIN_NAME=cdn-optimized.retreatandbe.com

# Security Tokens (Legacy)
TOKEN=CHANGE_ME_TOKEN_0BFIW
TOKEN=CHANGE_ME_TOKEN_62F2SN
TOKEN=CHANGE_ME_TOKEN_88WYDQ
TOKEN=CHANGE_ME_TOKEN_GWKZ2P
TOKEN=CHANGE_ME_TOKEN_LGB9LG
TOKEN=CHANGE_ME_TOKEN_XOLXIN