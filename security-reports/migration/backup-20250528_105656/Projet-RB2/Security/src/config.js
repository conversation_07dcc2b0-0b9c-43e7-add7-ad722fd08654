export const config = {
  port: process.env.PORT || 7010,
  env: process.env.NODE_ENV || 'development',
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    scanCacheTTL: 3600 // 1 hour
  },
  smtp: {
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT, 10) || 587,
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  },
  adminEmail: process.env.ADMIN_EMAIL,
  paths: {
    uploads: '/app/uploads',
    quarantine: '/app/quarantine',
    logs: '/app/logs',
    clamav: {
      db: '/var/lib/clamav',
      socket: '/var/run/clamav/clamd.ctl'
    }
  },
  maxFileSize: 50 * 1024 * 1024, // 50MB
  allowedMimeTypes: [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/zip',
    'application/x-zip-compressed',
    'text/plain',
    'text/csv',
    'application/json'
  ]
};