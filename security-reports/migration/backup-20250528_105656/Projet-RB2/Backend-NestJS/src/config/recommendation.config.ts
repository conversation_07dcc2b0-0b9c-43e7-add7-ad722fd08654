import { registerAs } from '@nestjs/config';

/**
 * Configuration pour le système de recommandation
 */
export default registerAs('recommendation', () => ({
  /**
   * Configuration du cache
   */
  cache: {
    /**
     * Activer le cache pour les recommandations
     */
    enabled: process.env.RECOMMENDATION_CACHE_ENABLED !== 'false',
    
    /**
     * Durées de vie du cache (en secondes)
     */
    ttl: {
      /**
       * Durée de vie des recommandations personnalisées
       */
      recommendations: parseInt(process.env.RECOMMENDATION_CACHE_TTL_RECOMMENDATIONS || '1800', 10), // 30 minutes
      
      /**
       * Durée de vie des recommandations tendance
       */
      trending: parseInt(process.env.RECOMMENDATION_CACHE_TTL_TRENDING || '3600', 10), // 1 heure
      
      /**
       * Durée de vie des éléments similaires
       */
      similar: parseInt(process.env.RECOMMENDATION_CACHE_TTL_SIMILAR || '7200', 10), // 2 heures
      
      /**
       * Durée de vie des détails d'un élément
       */
      itemDetails: parseInt(process.env.RECOMMENDATION_CACHE_TTL_ITEM_DETAILS || '86400', 10), // 24 heures
    },
  },
  
  /**
   * Configuration de l'intégration avec Agent-RB
   */
  agentRb: {
    /**
     * URL du service Agent-RB
     */
    serviceUrl: process.env.AGENT_RB_SERVICE_URL || 'http://localhost:5000',
    
    /**
     * Timeout pour les requêtes à Agent-RB (en millisecondes)
     */
    timeoutMs: parseInt(process.env.AGENT_RB_TIMEOUT_MS || '30000', 10),
    
    /**
     * Nombre maximum de requêtes parallèles à Agent-RB
     */
    maxConcurrentRequests: parseInt(process.env.AGENT_RB_MAX_CONCURRENT_REQUESTS || '10', 10),
    
    /**
     * Activer le circuit breaker pour les requêtes à Agent-RB
     */
    circuitBreaker: {
      enabled: process.env.AGENT_RB_CIRCUIT_BREAKER_ENABLED !== 'false',
      failureThreshold: parseInt(process.env.AGENT_RB_CIRCUIT_BREAKER_FAILURE_THRESHOLD || '50', 10), // Pourcentage d'échecs
      resetTimeout: parseInt(process.env.AGENT_RB_CIRCUIT_BREAKER_RESET_TIMEOUT || '30000', 10), // 30 secondes
    },
  },
  
  /**
   * Configuration des performances
   */
  performance: {
    /**
     * Nombre maximum d'éléments à traiter en parallèle
     */
    maxConcurrentItems: parseInt(process.env.RECOMMENDATION_MAX_CONCURRENT_ITEMS || '20', 10),
    
    /**
     * Activer le préchargement des données fréquemment utilisées
     */
    enablePreloading: process.env.RECOMMENDATION_ENABLE_PRELOADING !== 'false',
    
    /**
     * Intervalle de préchargement (en secondes)
     */
    preloadingInterval: parseInt(process.env.RECOMMENDATION_PRELOADING_INTERVAL || '3600', 10), // 1 heure
  },
  
  /**
   * Configuration des stratégies de recommandation
   */
  strategies: {
    /**
     * Stratégie par défaut
     */
    default: process.env.RECOMMENDATION_DEFAULT_STRATEGY || 'HYBRID',
    
    /**
     * Méthode hybride par défaut
     */
    defaultHybridMethod: process.env.RECOMMENDATION_DEFAULT_HYBRID_METHOD || 'WEIGHTED',
    
    /**
     * Poids pour la méthode hybride pondérée
     */
    hybridWeights: {
      contentBased: parseFloat(process.env.RECOMMENDATION_HYBRID_WEIGHT_CONTENT_BASED || '0.6'),
      collaborative: parseFloat(process.env.RECOMMENDATION_HYBRID_WEIGHT_COLLABORATIVE || '0.4'),
    },
  },
}));
