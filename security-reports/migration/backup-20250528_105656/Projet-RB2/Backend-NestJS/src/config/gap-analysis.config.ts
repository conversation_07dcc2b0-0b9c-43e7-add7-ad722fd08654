import { registerAs } from '@nestjs/config';

export default registerAs('gapAnalysis', () => ({
  // Configuration JWT
  jwt: {
    secret: process.env.JWT_SECRET || 'change-me-in-production',
    accessExpiresIn: process.env.JWT_ACCESS_EXPIRES_IN || '15m',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'change-me-in-production-refresh',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    issuer: process.env.JWT_ISSUER || 'retreat-and-be',
    audience: process.env.JWT_AUDIENCE || 'retreat-and-be-clients',
  },

  // Configuration Redis
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    db: parseInt(process.env.REDIS_DB, 10) || 0,
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'retreat:',
    ttl: parseInt(process.env.REDIS_TTL, 10) || 3600,
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: true,
    lazyConnect: true,
  },

  // Configuration Circuit Breaker
  circuitBreaker: {
    failureThreshold: parseInt(process.env.CIRCUIT_BREAKER_FAILURE_THRESHOLD, 10) || 5,
    resetTimeout: parseInt(process.env.CIRCUIT_BREAKER_RESET_TIMEOUT, 10) || 30000,
    halfOpenSuccessThreshold: parseInt(process.env.CIRCUIT_BREAKER_HALF_OPEN_SUCCESS_THRESHOLD, 10) || 2,
    timeout: parseInt(process.env.CIRCUIT_BREAKER_TIMEOUT, 10) || 10000,
    monitorIntervalMs: parseInt(process.env.CIRCUIT_BREAKER_MONITOR_INTERVAL, 10) || 60000,
  },

  // Configuration Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000, // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100,
    skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL === 'true',
    skipFailedRequests: process.env.RATE_LIMIT_SKIP_FAILED === 'true',
  },

  // Configuration Retry
  retry: {
    maxAttempts: parseInt(process.env.RETRY_MAX_ATTEMPTS, 10) || 3,
    baseDelay: parseInt(process.env.RETRY_BASE_DELAY, 10) || 1000,
    maxDelay: parseInt(process.env.RETRY_MAX_DELAY, 10) || 30000,
    backoffMultiplier: parseFloat(process.env.RETRY_BACKOFF_MULTIPLIER) || 2,
    jitter: process.env.RETRY_JITTER !== 'false',
  },

  // Configuration Cache
  cache: {
    useRedis: process.env.CACHE_USE_REDIS === 'true',
    defaultTtl: parseInt(process.env.CACHE_DEFAULT_TTL, 10) || 3600,
    maxItems: parseInt(process.env.CACHE_MAX_ITEMS, 10) || 1000,
    updateAgeOnGet: process.env.CACHE_UPDATE_AGE_ON_GET === 'true',
  },

  // Configuration Health Checks
  healthCheck: {
    database: {
      enabled: process.env.HEALTH_CHECK_DATABASE_ENABLED !== 'false',
      timeout: parseInt(process.env.HEALTH_CHECK_DATABASE_TIMEOUT, 10) || 5000,
      interval: parseInt(process.env.HEALTH_CHECK_DATABASE_INTERVAL, 10) || 30000,
    },
    redis: {
      enabled: process.env.HEALTH_CHECK_REDIS_ENABLED !== 'false',
      timeout: parseInt(process.env.HEALTH_CHECK_REDIS_TIMEOUT, 10) || 5000,
      interval: parseInt(process.env.HEALTH_CHECK_REDIS_INTERVAL, 10) || 30000,
    },
    hanumanAgents: {
      enabled: process.env.HEALTH_CHECK_HANUMAN_ENABLED !== 'false',
      timeout: parseInt(process.env.HEALTH_CHECK_HANUMAN_TIMEOUT, 10) || 15000,
      interval: parseInt(process.env.HEALTH_CHECK_HANUMAN_INTERVAL, 10) || 60000,
    },
  },

  // Configuration Monitoring
  monitoring: {
    enabled: process.env.MONITORING_ENABLED !== 'false',
    metricsPath: process.env.MONITORING_METRICS_PATH || '/metrics',
    healthPath: process.env.MONITORING_HEALTH_PATH || '/health',
    logLevel: process.env.LOG_LEVEL || 'info',
    structuredLogging: process.env.STRUCTURED_LOGGING === 'true',
  },

  // Configuration Sécurité
  security: {
    enableCors: process.env.SECURITY_ENABLE_CORS !== 'false',
    corsOrigins: process.env.SECURITY_CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
    enableHelmet: process.env.SECURITY_ENABLE_HELMET !== 'false',
    enableCsrf: process.env.SECURITY_ENABLE_CSRF === 'true',
    sessionSecret: process.env.SESSION_SECRET || 'change-me-in-production',
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS, 10) || 12,
  },

  // Configuration Performance
  performance: {
    enableCompression: process.env.PERFORMANCE_ENABLE_COMPRESSION !== 'false',
    compressionLevel: parseInt(process.env.PERFORMANCE_COMPRESSION_LEVEL, 10) || 6,
    enableEtag: process.env.PERFORMANCE_ENABLE_ETAG !== 'false',
    maxRequestSize: process.env.PERFORMANCE_MAX_REQUEST_SIZE || '10mb',
    requestTimeout: parseInt(process.env.PERFORMANCE_REQUEST_TIMEOUT, 10) || 30000,
  },

  // Configuration Hanuman Integration
  hanuman: {
    enabled: process.env.HANUMAN_INTEGRATION_ENABLED !== 'false',
    cortexUrl: process.env.HANUMAN_CORTEX_URL || 'http://localhost:8000',
    agentTimeout: parseInt(process.env.HANUMAN_AGENT_TIMEOUT, 10) || 15000,
    maxConcurrentRequests: parseInt(process.env.HANUMAN_MAX_CONCURRENT_REQUESTS, 10) || 10,
    retryAttempts: parseInt(process.env.HANUMAN_RETRY_ATTEMPTS, 10) || 3,
    circuitBreakerEnabled: process.env.HANUMAN_CIRCUIT_BREAKER_ENABLED !== 'false',
  },

  // Configuration Environment
  environment: {
    nodeEnv: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT, 10) || 3000,
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production',
    isTest: process.env.NODE_ENV === 'test',
  },
}));
