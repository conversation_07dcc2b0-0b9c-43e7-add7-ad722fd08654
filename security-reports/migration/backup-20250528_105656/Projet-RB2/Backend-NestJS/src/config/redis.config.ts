import { registerAs } from '@nestjs/config';

export default registerAs('redis', () => ({
  url: process.env.REDIS_URL,
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT ?? '6379', 10),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB ?? '0', 10),
  keyPrefix: process.env.REDIS_KEY_PREFIX || 'retreat:',
  ttl: parseInt(process.env.REDIS_TTL ?? String(60 * 60), 10), // 1 heure en secondes
  maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES_PER_REQUEST ?? '3', 10),
  retryStrategy: (times: number) => Math.min(times * 50, 2000), // Stratégie de reconnexion
}));
