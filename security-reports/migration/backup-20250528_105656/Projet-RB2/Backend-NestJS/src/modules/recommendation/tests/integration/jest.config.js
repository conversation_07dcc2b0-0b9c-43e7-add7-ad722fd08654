/**
 * Configuration Jest pour les tests d'intégration du système de recommandation
 */
module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../../../..',
  testRegex: '.integration.spec.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/modules/recommendation/**/*.service.(t|j)s',
    'src/modules/recommendation/**/*.controller.(t|j)s',
    'src/modules/recommendation/**/*.client.(t|j)s',
    '!**/node_modules/**',
    '!**/dist/**',
  ],
  coverageDirectory: '../coverage/integration',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  setupFilesAfterEnv: ['<rootDir>/src/modules/recommendation/tests/integration/setup.ts'],
  testTimeout: 30000, // 30 secondes
  verbose: true,
};
