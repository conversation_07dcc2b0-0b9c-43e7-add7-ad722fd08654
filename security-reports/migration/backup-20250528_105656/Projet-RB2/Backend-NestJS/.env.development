# Database Configuration
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/retreatandbe?schema=public"

# JWT Configuration
JWT_SECRET="this_is_a_very_long_secret_key_for_jwt_tokens_that_is_at_least_32_characters"
JWT_REFRESH_SECRET="this_is_another_very_long_secret_key_for_jwt_refresh_tokens_that_is_at_least_32_characters"
JWT_EXPIRATION="1h"
JWT_REFRESH_EXPIRATION="7d"

# Application Configuration
PORT=3000
NODE_ENV=development
API_PREFIX="/api/v1"

# CORS Configuration
CORS_ORIGIN="http://localhost:5173"

# Security Configuration
ENCRYPTION_KEY="this_is_a_very_long_encryption_key_that_is_at_least_32_characters"
ENCRYPTION_IV="this_is_a_16_char_iv_for_encryption"

# Email Configuration
SMTP_HOST="smtp.example.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-smtp-password"
SMTP_FROM="<EMAIL>"

# Redis Configuration
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""

# Microservices Configuration
SECURITY_SERVICE_URL="http://localhost:3001/api"
AGENT_IA_SERVICE_URL="http://localhost:3002/api"
SOCIAL_SERVICE_URL="http://localhost:3003/api"
FINANCIAL_SERVICE_URL="http://localhost:3004/api"
EDUCATION_SERVICE_URL="http://localhost:3005/api"

# File Upload Configuration
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=5242880 # 5MB

# Logging Configuration
LOG_LEVEL="debug"
LOG_FORMAT="dev"

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# Swagger Configuration
SWAGGER_TITLE="Retreat And Be API"
SWAGGER_DESCRIPTION="API for Retreat And Be platform"
SWAGGER_VERSION="1.0"
SWAGGER_PATH="api/docs"

# i18n Configuration
I18N_DEFAULT_LANGUAGE="fr"
I18N_FALLBACK_LANGUAGE="en"
