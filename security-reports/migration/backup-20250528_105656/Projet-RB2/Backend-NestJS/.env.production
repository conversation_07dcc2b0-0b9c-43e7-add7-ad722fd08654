# Database Configuration
DATABASE_URL="********************************************/retreatandbe?schema=public"

# JWT Configuration
JWT_SECRET="${JWT_SECRET}"
JWT_REFRESH_SECRET="${JWT_REFRESH_SECRET}"
JWT_EXPIRATION="1h"
JWT_REFRESH_EXPIRATION="7d"

# Application Configuration
PORT=3000
NODE_ENV=production
API_PREFIX="/api/v1"

# CORS Configuration
CORS_ORIGIN="https://retreatandbe.com"

# Security Configuration
ENCRYPTION_KEY="${ENCRYPTION_KEY}"
ENCRYPTION_IV="${ENCRYPTION_IV}"

# Email Configuration
SMTP_HOST="${SMTP_HOST}"
SMTP_PORT="${SMTP_PORT}"
SMTP_USER="${SMTP_USER}"
SMTP_PASSWORD="${SMTP_PASSWORD}"
SMTP_FROM="<EMAIL>"

# Redis Configuration
REDIS_HOST="redis"
REDIS_PORT=6379
REDIS_PASSWORD="${REDIS_PASSWORD}"

# Microservices Configuration
SECURITY_SERVICE_URL="http://security-service:3001/api"
AGENT_IA_SERVICE_URL="http://agent-ia-service:3002/api"
SOCIAL_SERVICE_URL="http://social-service:3003/api"
FINANCIAL_SERVICE_URL="http://financial-service:3004/api"
EDUCATION_SERVICE_URL="http://education-service:3005/api"

# File Upload Configuration
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=5242880 # 5MB

# Logging Configuration
LOG_LEVEL="info"
LOG_FORMAT="combined"

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# Swagger Configuration
SWAGGER_TITLE="Retreat And Be API"
SWAGGER_DESCRIPTION="API for Retreat And Be platform"
SWAGGER_VERSION="1.0"
SWAGGER_PATH="api/docs"
