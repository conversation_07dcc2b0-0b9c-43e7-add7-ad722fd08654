import { z } from 'zod';

// N8N configuration schema;
export const n8nConfigSchema = z.object({
  // Database;
  dbType: z.enum(['sqlite', 'postgresdb']).default('postgresdb'),
  dbHost: z.string().default('localhost'),
  dbPort: z.number().default(5432),
  dbName: z.string().default('n8n'),
  dbUser: z.string(),
  dbPassword: z.string(),

  // Security;
  encryptionKey: z.string().min(32),
  jwtSecret: z.string().min(32),

  // Network;
  port: z.number().default(5678),
  protocol: z.enum(['http', 'https']).default('https'),
  sslKey: z.string().optional(),
  sslCert: z.string().optional(),

  // Authentication;
  authEnabled: z.boolean().default(true),
  basicAuth: z.boolean().default(false),
  basicAuthUser: z.string().optional(),
  basicAuthPassword: z.string().optional(),

  // Webhook;
  webhookUrl: z.string().url(),

  // Execution;
  executionMode: z.enum(['regular', 'queue']).default('regular'),
  maxExecutionTimeout: z.number().default(3600)
});

export type N8NConfig = z.infer<typeof n8nConfigSchema>;
