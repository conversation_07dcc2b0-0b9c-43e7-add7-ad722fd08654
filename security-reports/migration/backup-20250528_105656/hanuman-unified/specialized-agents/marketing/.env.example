# Agent Marketing - Configuration Environment

# Application
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Weaviate Configuration
WEAVIATE_SCHEME=http
WEAVIATE_HOST=localhost:8080
WEAVIATE_API_KEY=

# Kafka Configuration
KAFKA_BROKER=localhost:9092
KAFKA_CLIENT_ID=agent-marketing
KAFKA_GROUP_ID=agent-marketing-group

# Redis Configuration (pour le cache)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# API Keys pour intégrations externes
OPENAI_API_KEY=
GOOGLE_ADS_API_KEY=
FACEBOOK_API_KEY=
LINKEDIN_API_KEY=
TWITTER_API_KEY=

# Base de données (optionnel pour persistance additionnelle)
DATABASE_URL=postgresql://user:password@localhost:5432/marketing_agent

# Sécurité
JWT_SECRET=your-super-secret-jwt-key
API_RATE_LIMIT=100

# Monitoring
SENTRY_DSN=
NEW_RELIC_LICENSE_KEY=

# Features Flags
ENABLE_AB_TESTING=true
ENABLE_SOCIAL_MEDIA=true
ENABLE_ANALYTICS=true
ENABLE_OPTIMIZATION=true
