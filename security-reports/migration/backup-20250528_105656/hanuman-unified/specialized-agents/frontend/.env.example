# Agent Frontend Configuration

# Agent Identity
AGENT_ID=agent-frontend-001
AGENT_NAME="Agent Frontend Code Generator"
AGENT_VERSION=1.0.0

# Server Configuration
PORT=3006
NODE_ENV=development
LOG_LEVEL=info

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:3002

# Weaviate Configuration
WEAVIATE_URL=http://weaviate:8080
WEAVIATE_API_KEY=

# Kafka Configuration
KAFKA_BROKERS=kafka:9092
KAFKA_CLIENT_ID=agent-frontend-001
KAFKA_GROUP_ID=agent-frontend-group

# Redis Configuration (optionnel)
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=

# OpenAI Configuration (pour l'IA générative)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000

# Code Generation Configuration
DEFAULT_FRAMEWORK=react
OUTPUT_DIRECTORY=./generated
TEMPLATES_DIRECTORY=./templates
MAX_FILE_SIZE=10485760

# Performance Configuration
MAX_CONCURRENT_GENERATIONS=5
GENERATION_TIMEOUT=300000
VALIDATION_TIMEOUT=60000

# Security Configuration
JWT_SECRET=your_jwt_secret_here
API_RATE_LIMIT=100
ENABLE_CORS=true
ENABLE_HELMET=true

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30000

# Development Configuration
ENABLE_HOT_RELOAD=true
ENABLE_DEBUG_LOGS=false
MOCK_EXTERNAL_SERVICES=false

# Deployment Configuration
DEPLOYMENT_PLATFORMS=vercel,netlify,aws,docker
DEFAULT_DEPLOYMENT_PLATFORM=vercel

# Cache Configuration
CACHE_TTL=300000
CACHE_MAX_SIZE=1000
ENABLE_CACHE=true

# Template Configuration
TEMPLATE_CACHE_SIZE=100
TEMPLATE_UPDATE_INTERVAL=3600000

# Validation Configuration
ENABLE_ACCESSIBILITY_VALIDATION=true
ENABLE_PERFORMANCE_VALIDATION=true
ENABLE_SECURITY_VALIDATION=true
ENABLE_SEO_VALIDATION=true

# Optimization Configuration
ENABLE_CODE_OPTIMIZATION=true
ENABLE_BUNDLE_OPTIMIZATION=true
ENABLE_IMAGE_OPTIMIZATION=true
ENABLE_CSS_OPTIMIZATION=true

# Analytics Configuration
ENABLE_ANALYTICS=true
ANALYTICS_ENDPOINT=http://analytics:3000/events

# Backup Configuration
ENABLE_BACKUP=true
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=30
