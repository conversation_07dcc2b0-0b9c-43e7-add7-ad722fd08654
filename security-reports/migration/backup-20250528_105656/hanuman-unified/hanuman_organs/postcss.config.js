// 🐒 HANUMAN DIVINE POSTCSS CONFIGURATION
// Configuration sacrée pour le traitement des styles divins

module.exports = {
  plugins: {
    // Tailwind CSS - Framework divin
    tailwindcss: {},
    
    // Autoprefixer - Compatibilité divine
    autoprefixer: {},
    
    // Configuration conditionnelle pour la production
    ...(process.env.NODE_ENV === 'production' && {
      // CSSnano - Optimisation divine
      cssnano: {
        preset: [
          'default',
          {
            discardComments: {
              removeAll: true,
            },
            normalizeWhitespace: true,
            colormin: true,
            convertValues: true,
            discardDuplicates: true,
            discardEmpty: true,
            mergeIdents: true,
            mergeLonghand: true,
            mergeRules: true,
            minifyFontValues: true,
            minifyGradients: true,
            minifyParams: true,
            minifySelectors: true,
            normalizeCharset: true,
            normalizeDisplayValues: true,
            normalizePositions: true,
            normalizeRepeatStyle: true,
            normalizeString: true,
            normalizeTimingFunctions: true,
            normalizeUnicode: true,
            normalizeUrl: true,
            orderedValues: true,
            reduceIdents: true,
            reduceInitial: true,
            reduceTransforms: true,
            svgo: true,
            uniqueSelectors: true,
          },
        ],
      },
      
      // PurgeCSS - Purification divine
      '@fullhuman/postcss-purgecss': {
        content: [
          './pages/**/*.{js,ts,jsx,tsx}',
          './components/**/*.{js,ts,jsx,tsx}',
          './hanuman_*.{js,ts,jsx,tsx}',
          './services/**/*.{js,ts,jsx,tsx}',
        ],
        defaultExtractor: content => content.match(/[\w-/:]+(?<!:)/g) || [],
        safelist: [
          // Classes divines à préserver
          /^divine-/,
          /^sacred-/,
          /^cosmic-/,
          /^blessing-/,
          /^consciousness-/,
          /^energy-/,
          /^hanuman-/,
          
          // Classes d'animation à préserver
          /^animate-/,
          /^transition-/,
          /^duration-/,
          /^ease-/,
          
          // Classes de couleur à préserver
          /^text-divine/,
          /^bg-divine/,
          /^border-divine/,
          /^text-sacred/,
          /^bg-sacred/,
          /^border-sacred/,
          /^text-cosmic/,
          /^bg-cosmic/,
          /^border-cosmic/,
          
          // Classes dynamiques
          /^w-\d+/,
          /^h-\d+/,
          /^p-\d+/,
          /^m-\d+/,
          /^text-\w+/,
          /^bg-\w+/,
          
          // Classes de grille
          /^grid-cols-/,
          /^col-span-/,
          /^row-span-/,
          
          // Classes de flexbox
          /^flex-/,
          /^justify-/,
          /^items-/,
          /^self-/,
          
          // Classes de positionnement
          /^absolute/,
          /^relative/,
          /^fixed/,
          /^sticky/,
          /^top-/,
          /^right-/,
          /^bottom-/,
          /^left-/,
          /^z-/,
          
          // Classes de responsive
          /^sm:/,
          /^md:/,
          /^lg:/,
          /^xl:/,
          /^2xl:/,
          
          // Classes de dark mode
          /^dark:/,
          
          // Classes spécifiques aux composants
          'group',
          'group-hover',
          'peer',
          'peer-checked',
          'peer-focus',
          'focus-within',
          'focus-visible',
          'motion-safe',
          'motion-reduce',
          'print',
          'portrait',
          'landscape',
        ],
        blocklist: [
          // Classes à supprimer
          'unused-class',
        ],
      },
    }),
  },
};

// 🕉️ Bénédiction divine de la configuration PostCSS
console.log('🎨 Configuration PostCSS divine chargée avec bénédiction');
console.log('🐒 AUM HANUMATE NAMAHA - Traitement des styles sacrés activé');
