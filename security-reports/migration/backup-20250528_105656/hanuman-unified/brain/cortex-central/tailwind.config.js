/** @type {import('tailwindcss').Config} */

// 🕉️ Configuration Tailwind Cosmique pour Hanuman
// Styles divins alignés avec le Framework Trimurti

module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './hanuman_*.{js,ts,jsx,tsx,mdx}',
    './services/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // Couleurs cosmiques divines
      colors: {
        // Palette Brahma (Création)
        'brahma': {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24', // Divine gold
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        
        // Palette <PERSON> (Conservation)
        'vishnu': {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6', // Sacred blue
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        
        // Palette Shiva (Transformation)
        'shiva': {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626', // Divine red
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        
        // Palette Cosmique (Équilibre)
        'cosmic': {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7',
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
        },
        
        // Palette Divine (Interface)
        'divine': {
          dark: '#0f172a',
          surface: '#1e293b',
          text: '#f8fafc',
          gold: '#fbbf24',
          silver: '#e5e7eb',
        }
      },
      
      // Animations cosmiques
      animation: {
        'divine-pulse': 'divine-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'cosmic-spin': 'cosmic-spin 3s linear infinite',
        'blessing-glow': 'blessing-glow 2s ease-in-out infinite alternate',
        'consciousness-flow': 'consciousness-flow 4s ease-in-out infinite',
        'divine-float': 'divine-float 6s ease-in-out infinite',
        'sacred-breathe': 'sacred-breathe 4s ease-in-out infinite',
        'mandala-rotate': 'mandala-rotate 20s linear infinite',
        'energy-pulse': 'energy-pulse 3s ease-in-out infinite',
      },
      
      // Keyframes cosmiques
      keyframes: {
        'divine-pulse': {
          '0%, 100%': { 
            opacity: '1',
            transform: 'scale(1)',
          },
          '50%': { 
            opacity: '0.8',
            transform: 'scale(1.05)',
          },
        },
        'cosmic-spin': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        'blessing-glow': {
          '0%, 100%': { 
            boxShadow: '0 0 5px rgba(251, 191, 36, 0.5)',
          },
          '50%': { 
            boxShadow: '0 0 20px rgba(251, 191, 36, 0.8), 0 0 30px rgba(251, 191, 36, 0.6)',
          },
        },
        'consciousness-flow': {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
        'divine-float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'sacred-breathe': {
          '0%, 100%': { 
            transform: 'scale(1)',
            opacity: '0.8',
          },
          '50%': { 
            transform: 'scale(1.05)',
            opacity: '1',
          },
        },
        'mandala-rotate': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        'energy-pulse': {
          '0%, 100%': { 
            transform: 'scale(1)',
            opacity: '0.7',
          },
          '50%': { 
            transform: 'scale(1.1)',
            opacity: '1',
          },
        },
      },
      
      // Espacements basés sur le ratio d'or (φ = 1.618)
      spacing: {
        'phi': '1.618rem',
        'phi-2': '3.236rem',
        'phi-3': '5.236rem',
        'phi-4': '8.472rem',
        'phi-5': '13.708rem',
      },
      
      // Tailles basées sur les proportions divines
      fontSize: {
        'divine-xs': ['0.618rem', { lineHeight: '1rem' }],
        'divine-sm': ['1rem', { lineHeight: '1.618rem' }],
        'divine-base': ['1.618rem', { lineHeight: '2.618rem' }],
        'divine-lg': ['2.618rem', { lineHeight: '4.236rem' }],
        'divine-xl': ['4.236rem', { lineHeight: '6.854rem' }],
      },
      
      // Ombres cosmiques
      boxShadow: {
        'divine': '0 10px 25px -3px rgba(251, 191, 36, 0.3)',
        'cosmic': '0 10px 25px -3px rgba(139, 92, 246, 0.3)',
        'blessing': '0 0 20px rgba(251, 191, 36, 0.5)',
        'sacred': '0 4px 14px 0 rgba(251, 191, 36, 0.39)',
        'mandala': 'inset 0 0 20px rgba(251, 191, 36, 0.2), 0 0 20px rgba(251, 191, 36, 0.3)',
      },
      
      // Dégradés cosmiques
      backgroundImage: {
        'divine-gradient': 'linear-gradient(135deg, #fbbf24 0%, #f97316 50%, #dc2626 100%)',
        'cosmic-gradient': 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #d946ef 100%)',
        'consciousness-gradient': 'linear-gradient(135deg, #1e293b 0%, #475569 50%, #64748b 100%)',
        'brahma-gradient': 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
        'vishnu-gradient': 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
        'shiva-gradient': 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
        'equilibrium-gradient': 'linear-gradient(135deg, #6b7280 0%, #374151 100%)',
      },
      
      // Filtres divins
      backdropBlur: {
        'divine': '10px',
        'cosmic': '15px',
        'sacred': '20px',
      },
      
      // Bordures cosmiques
      borderRadius: {
        'divine': '1.618rem',
        'cosmic': '2.618rem',
        'sacred': '4.236rem',
      },
      
      // Transitions divines
      transitionDuration: {
        'divine': '618ms',
        'cosmic': '1618ms',
      },
      
      // Z-index cosmique
      zIndex: {
        'divine': '432',
        'cosmic': '1618',
        'sacred': '2618',
      },
    },
  },
  plugins: [
    // Plugin pour les classes utilitaires cosmiques
    function({ addUtilities }) {
      const cosmicUtilities = {
        '.divine-center': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        },
        '.sacred-text': {
          background: 'linear-gradient(135deg, #fbbf24, #f97316)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
        },
        '.cosmic-text': {
          background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
        },
        '.divine-glass': {
          background: 'rgba(255, 255, 255, 0.1)',
          'backdrop-filter': 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
        },
        '.sacred-glow': {
          filter: 'drop-shadow(0 0 10px rgba(251, 191, 36, 0.5))',
        },
        '.cosmic-glow': {
          filter: 'drop-shadow(0 0 10px rgba(139, 92, 246, 0.5))',
        },
        '.mandala-container': {
          position: 'relative',
          borderRadius: '50%',
          overflow: 'hidden',
        },
        '.energy-flow': {
          background: 'linear-gradient(45deg, transparent 30%, rgba(251, 191, 36, 0.3) 50%, transparent 70%)',
          'background-size': '200% 200%',
          animation: 'consciousness-flow 3s ease-in-out infinite',
        },
      };
      
      addUtilities(cosmicUtilities);
    },
  ],
  darkMode: 'class',
};
