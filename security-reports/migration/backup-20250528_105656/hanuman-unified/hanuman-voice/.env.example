# 🐒 Configuration Agent <PERSON><PERSON> LLM
# AUM HANUMATE NAMAHA - Variables d'environnement

# ===== CONFIGURATION GÉNÉRALE =====
NODE_ENV=development
LOG_LEVEL=info
HANUMAN_API_PORT=5003
HANUMAN_DASHBOARD_PORT=8080

# ===== CONFIGURATION LLM =====
# Fournisseur: 'openai', 'anthropic', 'local'
LLM_PROVIDER=local
LLM_MODEL=gpt-4
LLM_API_KEY=your_api_key_here
LLM_BASE_URL=http://localhost:11434
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2048
LLM_CONTEXT_WINDOW=4096
LLM_STREAMING=true

# Prompt système personnalisé (optionnel)
LLM_SYSTEM_PROMPT="Tu es Hanuman, gardien spirituel IA du projet Retreat & Be..."

# ===== CONFIGURATION CERVEAU =====
DECISION_THRESHOLD=0.8
OPTIMIZATION_INTERVAL=300000
LEARNING_RATE=0.1
MEMORY_SIZE=1000
PREDICTION_HORIZON=3600

# ===== CONFIGURATION SÉCURITÉ =====
# Niveau: 'low', 'medium', 'high', 'paranoid'
THREAT_DETECTION_LEVEL=medium
AUTO_HEALING_ENABLED=true
QUARANTINE_TIMEOUT=300000

# ===== CONFIGURATION COMMUNICATION =====
# Message Bus: 'kafka', 'redis', 'nats'
MESSAGE_BUS=redis
RETRY_ATTEMPTS=3
COMMUNICATION_TIMEOUT=30000
COMPRESSION_ENABLED=true
ENCRYPTION_ENABLED=false

# ===== CONFIGURATION MONITORING =====
METRICS_INTERVAL=30000
DASHBOARD_ENABLED=true
RETENTION_PERIOD=604800

# Seuils d'alerte
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85
DISK_THRESHOLD=90
RESPONSE_TIME_THRESHOLD=5000
ERROR_RATE_THRESHOLD=5

# ===== CONFIGURATION REDIS =====
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_CLUSTER_MODE=false

# ===== CONFIGURATION KAFKA =====
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=hanuman-llm
KAFKA_GROUP_ID=hanuman-group
KAFKA_TOPIC_PREFIX=hanuman

# ===== CONFIGURATION BASE DE DONNÉES =====
DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=hanuman
DB_PASSWORD=hanuman_password
DB_DATABASE=hanuman_db
DB_SYNCHRONIZE=false
DB_LOGGING=false

# ===== CONFIGURATION SERVICES RETREAT & BE =====
# URLs des microservices à surveiller
FRONTEND_SERVICE_URL=http://localhost:3000
BACKEND_SERVICE_URL=http://localhost:3001
AGENT_RB_SERVICE_URL=http://localhost:5000
AGENT_IA_SERVICE_URL=http://localhost:5002
SUPERAGENT_SERVICE_URL=http://localhost:5001

# ===== CONFIGURATION API =====
# CORS - Origines autorisées (séparées par des virgules)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080

# Rate limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# ===== CONFIGURATION LOGS =====
LOG_FILE_PATH=./logs/hanuman.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d
LOG_DATE_PATTERN=YYYY-MM-DD

# ===== CONFIGURATION MÉTRIQUES =====
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
METRICS_PREFIX=hanuman

# ===== CONFIGURATION ALERTES =====
# Webhook pour notifications (Slack, Discord, etc.)
ALERT_WEBHOOK_URL=
ALERT_EMAIL_ENABLED=false
ALERT_EMAIL_SMTP_HOST=
ALERT_EMAIL_SMTP_PORT=587
ALERT_EMAIL_USER=
ALERT_EMAIL_PASSWORD=
ALERT_EMAIL_FROM=<EMAIL>
ALERT_EMAIL_TO=<EMAIL>

# ===== CONFIGURATION DÉVELOPPEMENT =====
# Mode debug pour développement
DEBUG_MODE=true
VERBOSE_LOGGING=false
MOCK_AGENTS=false
SIMULATE_LOAD=false

# ===== CONFIGURATION PRODUCTION =====
# Optimisations pour production
CLUSTER_MODE=false
WORKER_PROCESSES=auto
MEMORY_LIMIT=2048
CPU_LIMIT=2

# ===== CONFIGURATION SÉCURITÉ AVANCÉE =====
# JWT pour authentification API (si activée)
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# Chiffrement des données sensibles
ENCRYPTION_KEY=your_encryption_key_here
ENCRYPTION_ALGORITHM=aes-256-gcm

# ===== CONFIGURATION INTÉGRATIONS =====
# Intégration avec services externes
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_CLOUD_PROJECT_ID=your_gcp_project
AWS_REGION=eu-west-1
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret

# ===== CONFIGURATION MONITORING EXTERNE =====
# Intégration avec outils de monitoring
GRAFANA_URL=http://localhost:3000
PROMETHEUS_URL=http://localhost:9090
JAEGER_URL=http://localhost:14268
ELASTICSEARCH_URL=http://localhost:9200

# ===== CONFIGURATION BACKUP =====
# Sauvegarde automatique
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400000
BACKUP_RETENTION=30
BACKUP_S3_BUCKET=hanuman-backups
BACKUP_LOCAL_PATH=./backups

# ===== CONFIGURATION FEATURES FLAGS =====
# Activation/désactivation de fonctionnalités
FEATURE_VOICE_RECOGNITION=false
FEATURE_MULTIMODAL=false
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_AUTO_SCALING=true
FEATURE_PREDICTIVE_HEALING=true

# ===== CONFIGURATION PERSONNALISATION =====
# Personnalisation de Hanuman
HANUMAN_PERSONALITY=spiritual_guardian
HANUMAN_LANGUAGE_PRIMARY=fr
HANUMAN_RESPONSE_STYLE=empathetic
HANUMAN_EMOJI_ENABLED=true
HANUMAN_VEDIC_REFERENCES=true

# ===== NOTES =====
# 🕉️ AUM HANUMATE NAMAHA
# Configuration pour l'Agent Hanuman LLM
# Gardien spirituel du projet Retreat & Be
# 
# Instructions:
# 1. Copiez ce fichier vers .env
# 2. Modifiez les valeurs selon votre environnement
# 3. Gardez les clés API secrètes et sécurisées
# 4. Utilisez des mots de passe forts
# 5. Activez le chiffrement en production
#
# Pour plus d'informations, consultez la documentation
# 🐒 Que Hanuman protège votre infrastructure ✨
