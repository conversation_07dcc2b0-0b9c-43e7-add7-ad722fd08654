# Agent UI/UX Configuration
AGENT_ID=agent-uiux-001
PORT=3005
NODE_ENV=development
LOG_LEVEL=info

# Weaviate Configuration
WEAVIATE_URL=http://weaviate:8080
WEAVIATE_API_KEY=

# Kafka Configuration
KAFKA_BROKERS=kafka:9092
KAFKA_GROUP_ID=agent-uiux-group
KAFKA_CLIENT_ID=agent-uiux-client

# Redis Configuration
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=

# OpenAI Configuration (for AI-powered features)
OPENAI_API_KEY=
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# External APIs
FIGMA_API_TOKEN=
GOOGLE_ANALYTICS_API_KEY=
HOTJAR_API_KEY=

# Security
JWT_SECRET=your-jwt-secret-here
API_RATE_LIMIT=100

# Performance
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30000

# Features Flags
ENABLE_AI_GENERATION=true
ENABLE_REAL_USER_RESEARCH=false
ENABLE_EXTERNAL_APIS=false
ENABLE_CACHING=true

# Monitoring
SENTRY_DSN=
PROMETHEUS_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# Development
DEBUG=agent-uiux:*
MOCK_EXTERNAL_SERVICES=true
