import dotenv from 'dotenv';

// Chargement des variables d'environnement
dotenv.config();

export const config = {
  // Configuration du serveur
  server: {
    port: parseInt(process.env.PORT || '8080'),
    host: process.env.HOST || '0.0.0.0',
    environment: process.env.NODE_ENV || 'development'
  },

  // Configuration Kafka
  kafka: {
    brokers: (process.env.KAFKA_BROKERS || 'kafka:9092').split(','),
    clientId: 'cortex-central',
    groupId: 'cortex-central-group'
  },

  // Configuration Redis
  redis: {
    url: process.env.REDIS_URL || 'redis://redis:6379',
    maxRetries: 10,
    retryDelayOnFailure: 100
  },

  // Configuration Weaviate
  weaviate: {
    url: process.env.WEAVIATE_URL || 'http://weaviate:8080',
    scheme: 'http'
  },

  // Configuration du réseau neuronal
  neuralNetwork: {
    mode: process.env.NEURAL_NETWORK_MODE || 'active',
    synapticCommunication: process.env.SYNAPTIC_COMMUNICATION || 'kafka',
    plasticityEnabled: process.env.PLASTICITY_ENABLED !== 'false',
    adaptiveForgetting: process.env.ADAPTIVE_FORGETTING !== 'false'
  },

  // Configuration du moteur de décision
  decisionEngine: {
    enabled: process.env.DECISION_ENGINE !== 'disabled',
    learningRate: parseFloat(process.env.LEARNING_RATE || '0.1'),
    selfImprovementInterval: parseInt(process.env.SELF_IMPROVEMENT_INTERVAL || '3600000'), // 1 heure
    patternCacheSize: parseInt(process.env.PATTERN_CACHE_SIZE || '1000')
  },

  // Configuration de la mémoire
  memory: {
    store: process.env.MEMORY_STORE || 'weaviate',
    cacheEnabled: process.env.MEMORY_CACHE_ENABLED !== 'false',
    cacheTTL: parseInt(process.env.MEMORY_CACHE_TTL || '3600'), // 1 heure
    forgettingThreshold: parseFloat(process.env.FORGETTING_THRESHOLD || '0.3'),
    forgettingInterval: parseInt(process.env.FORGETTING_INTERVAL || '3600000') // 1 heure
  },

  // Configuration du logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    file: process.env.LOG_FILE || '/app/logs/cortex-central.log',
    maxSize: process.env.LOG_MAX_SIZE || '10m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES || '5')
  },

  // Configuration du monitoring
  monitoring: {
    enabled: process.env.MONITORING_ENABLED !== 'false',
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000'), // 30 secondes
    metricsInterval: parseInt(process.env.METRICS_INTERVAL || '60000'), // 1 minute
    alertingEnabled: process.env.ALERTING_ENABLED === 'true'
  },

  // Configuration de sécurité
  security: {
    jwtSecret: process.env.JWT_SECRET || 'cortex-central-secret-key',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
    corsOrigin: process.env.CORS_ORIGIN || '*',
    rateLimitEnabled: process.env.RATE_LIMIT_ENABLED !== 'false',
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100'),
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '900000') // 15 minutes
  },

  // Configuration des agents
  agents: {
    maxConcurrentTasks: parseInt(process.env.MAX_CONCURRENT_TASKS || '10'),
    taskTimeout: parseInt(process.env.TASK_TIMEOUT || '1800000'), // 30 minutes
    heartbeatInterval: parseInt(process.env.AGENT_HEARTBEAT_INTERVAL || '30000'), // 30 secondes
    reconnectAttempts: parseInt(process.env.AGENT_RECONNECT_ATTEMPTS || '5'),
    reconnectDelay: parseInt(process.env.AGENT_RECONNECT_DELAY || '5000') // 5 secondes
  },

  // Configuration de l'IDE intelligent
  ide: {
    enabled: process.env.IDE_ENABLED !== 'false',
    collaborationEnabled: process.env.COLLABORATION_ENABLED !== 'false',
    websocketPort: parseInt(process.env.WEBSOCKET_PORT || '3000'),
    maxConnections: parseInt(process.env.IDE_MAX_CONNECTIONS || '100'),
    sessionTimeout: parseInt(process.env.IDE_SESSION_TIMEOUT || '3600000') // 1 heure
  },

  // Configuration des performances
  performance: {
    maxMemoryUsage: process.env.MAX_MEMORY_USAGE || '2gb',
    gcInterval: parseInt(process.env.GC_INTERVAL || '300000'), // 5 minutes
    optimizationEnabled: process.env.OPTIMIZATION_ENABLED !== 'false',
    cachingStrategy: process.env.CACHING_STRATEGY || 'lru'
  }
};

// Validation de la configuration
export function validateConfig(): void {
  const requiredEnvVars = [
    'KAFKA_BROKERS',
    'REDIS_URL',
    'WEAVIATE_URL'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new Error(`Variables d'environnement manquantes: ${missingVars.join(', ')}`);
  }

  // Validation des ports
  if (config.server.port < 1 || config.server.port > 65535) {
    throw new Error('Port du serveur invalide');
  }

  // Validation des URLs
  try {
    new URL(config.redis.url);
    new URL(`http://${config.weaviate.url}`);
  } catch (error) {
    throw new Error('URLs de configuration invalides');
  }

  console.log('✅ Configuration validée avec succès');
}

// Export des constantes utiles
export const NEURAL_TOPICS = {
  SIGNALS: 'neural-signals',
  HEARTBEAT: 'agent-heartbeat',
  TASK_COORDINATION: 'task-coordination',
  MEMORY_SYNC: 'memory-sync',
  EMERGENCY: 'emergency-signals'
} as const;

export const AGENT_TYPES = {
  FRONTEND: 'frontend',
  BACKEND: 'backend',
  DEVOPS: 'devops',
  QA: 'qa',
  SECURITY: 'security'
} as const;

export const COMPLEXITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const;

export const PRIORITY_LEVELS = {
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const;
