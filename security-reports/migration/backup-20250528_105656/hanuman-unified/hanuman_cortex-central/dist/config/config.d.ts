export declare const config: {
    server: {
        port: number;
        host: string;
        environment: string;
    };
    kafka: {
        brokers: string[];
        clientId: string;
        groupId: string;
    };
    redis: {
        url: string;
        maxRetries: number;
        retryDelayOnFailure: number;
    };
    weaviate: {
        url: string;
        scheme: string;
    };
    neuralNetwork: {
        mode: string;
        synapticCommunication: string;
        plasticityEnabled: boolean;
        adaptiveForgetting: boolean;
    };
    decisionEngine: {
        enabled: boolean;
        learningRate: number;
        selfImprovementInterval: number;
        patternCacheSize: number;
    };
    memory: {
        store: string;
        cacheEnabled: boolean;
        cacheTTL: number;
        forgettingThreshold: number;
        forgettingInterval: number;
    };
    logging: {
        level: string;
        format: string;
        file: string;
        maxSize: string;
        maxFiles: number;
    };
    monitoring: {
        enabled: boolean;
        healthCheckInterval: number;
        metricsInterval: number;
        alertingEnabled: boolean;
    };
    security: {
        jwtSecret: string;
        jwtExpiresIn: string;
        corsOrigin: string;
        rateLimitEnabled: boolean;
        rateLimitMax: number;
        rateLimitWindow: number;
    };
    agents: {
        maxConcurrentTasks: number;
        taskTimeout: number;
        heartbeatInterval: number;
        reconnectAttempts: number;
        reconnectDelay: number;
    };
    ide: {
        enabled: boolean;
        collaborationEnabled: boolean;
        websocketPort: number;
        maxConnections: number;
        sessionTimeout: number;
    };
    performance: {
        maxMemoryUsage: string;
        gcInterval: number;
        optimizationEnabled: boolean;
        cachingStrategy: string;
    };
};
export declare function validateConfig(): void;
export declare const NEURAL_TOPICS: {
    readonly SIGNALS: "neural-signals";
    readonly HEARTBEAT: "agent-heartbeat";
    readonly TASK_COORDINATION: "task-coordination";
    readonly MEMORY_SYNC: "memory-sync";
    readonly EMERGENCY: "emergency-signals";
};
export declare const AGENT_TYPES: {
    readonly FRONTEND: "frontend";
    readonly BACKEND: "backend";
    readonly DEVOPS: "devops";
    readonly QA: "qa";
    readonly SECURITY: "security";
};
export declare const COMPLEXITY_LEVELS: {
    readonly LOW: "low";
    readonly MEDIUM: "medium";
    readonly HIGH: "high";
    readonly CRITICAL: "critical";
};
export declare const PRIORITY_LEVELS: {
    readonly LOW: "low";
    readonly NORMAL: "normal";
    readonly HIGH: "high";
    readonly CRITICAL: "critical";
};
//# sourceMappingURL=config.d.ts.map