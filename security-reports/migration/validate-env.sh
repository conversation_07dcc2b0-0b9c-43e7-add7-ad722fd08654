#!/bin/bash

# Script de validation des variables d'environnement
# Vérifie que tous les services ont leurs variables configurées

validate_service() {
    local service_name="$1"
    local env_file="$2"
    
    echo "🔍 Validation: $service_name"
    
    if [[ -f "$env_file" ]]; then
        echo "  ✅ Fichier .env trouvé"
        
        # Vérifier les variables critiques
        local critical_vars=("DATABASE_URL" "JWT_SECRET" "NODE_ENV")
        
        for var in "${critical_vars[@]}"; do
            if grep -q "^$var=" "$env_file"; then
                echo "  ✅ $var configuré"
            else
                echo "  ❌ $var manquant"
            fi
        done
    else
        echo "  ❌ Fichier .env manquant: $env_file"
    fi
    
    echo ""
}

# Valider tous les services
validate_service "Backend NestJS" "Projet-RB2/Backend-NestJS/.env"
validate_service "Frontend" "Projet-RB2/Front-Audrey-V1-Main-main/.env"
validate_service "Agent IA" "Projet-RB2/Agent IA/.env"
validate_service "Security" "Projet-RB2/Security/.env"
validate_service "Hanuman" "hanuman-unified/.env"
validate_service "Vimana" "vimana/.env"
