npm warn using --force Recommended protections disabled.

up to date, audited 3972 packages in 26s

572 packages are looking for funding
  run `npm fund` for details

# npm audit report

micromatch  <4.0.8
Severity: moderate
Regular Expression Denial of Service (ReDoS) in micromatch - https://github.com/advisories/GHSA-952p-6rrq-rcjv
fix available via `npm audit fix`
node_modules/micromatch
  lint-staged  7.0.0 - 8.2.1 || 13.3.0 - 15.2.4
  Depends on vulnerable versions of micromatch
  hanuman-unified/node_modules/lint-staged

tar-fs  3.0.0 - 3.0.6
Severity: high
tar-fs Vulnerable to Link Following and Path Traversal via Extracting a Crafted tar File - https://github.com/advisories/GHSA-pq67-2wwv-3xjx
fix available via `npm audit fix`
node_modules/@puppeteer/browsers/node_modules/tar-fs
  @puppeteer/browsers  1.4.2 - 2.2.3
  Depends on vulnerable versions of tar-fs
  node_modules/@puppeteer/browsers
    puppeteer  18.2.0 - 22.13.0
    Depends on vulnerable versions of @puppeteer/browsers
    Depends on vulnerable versions of puppeteer-core
    node_modules/puppeteer
    puppeteer-core  11.0.0 - 22.13.0
    Depends on vulnerable versions of @puppeteer/browsers
    Depends on vulnerable versions of ws
    node_modules/puppeteer/node_modules/puppeteer-core

ws  8.0.0 - 8.17.0
Severity: high
ws affected by a DoS when handling a request with many HTTP headers - https://github.com/advisories/GHSA-3h5v-q93c-6h6q
fix available via `npm audit fix`
node_modules/puppeteer/node_modules/ws

7 vulnerabilities (2 moderate, 5 high)

To address all issues, run:
  npm audit fix
