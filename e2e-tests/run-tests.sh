#!/bin/bash

# Script d'exécution des tests E2E - Sprint 16

set -e

# Configuration
E2E_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

cd "$E2E_DIR"

case "${1:-all}" in
    "chrome"|"chromium")
        log "🧪 Exécution tests Chrome"
        npx playwright test --project=chromium
        ;;
    "firefox")
        log "🧪 Exécution tests Firefox"
        npx playwright test --project=firefox
        ;;
    "safari"|"webkit")
        log "🧪 Exécution tests Safari"
        npx playwright test --project=webkit
        ;;
    "mobile")
        log "🧪 Exécution tests Mobile"
        npx playwright test --project=mobile
        ;;
    "headed")
        log "🧪 Exécution tests avec interface"
        npx playwright test --headed
        ;;
    "debug")
        log "🧪 Exécution tests en mode debug"
        npx playwright test --debug
        ;;
    "ui")
        log "🧪 Ouverture interface UI"
        npx playwright test --ui
        ;;
    "report")
        log "📊 Ouverture rapport"
        npx playwright show-report
        ;;
    "all"|*)
        log "🧪 Exécution tous les tests"
        npx playwright test
        ;;
esac

success "✅ Tests terminés"
