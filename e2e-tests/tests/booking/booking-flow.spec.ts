import { test, expect } from '@playwright/test';

test.describe('Processus de réservation', () => {
  test.beforeEach(async ({ page }) => {
    // Se connecter avant chaque test
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
  });

  test('Réservation complète d\'une retraite', async ({ page }) => {
    // 1. Rechercher une retraite
    await page.goto('/retreats');
    await page.fill('[data-testid="search-input"]', 'Yoga');
    await page.click('[data-testid="search-button"]');
    
    // 2. Sélectionner une retraite
    await page.click('[data-testid="retreat-card"]:first-child');
    await expect(page.locator('[data-testid="retreat-details"]')).toBeVisible();
    
    // 3. <PERSON>sir les dates
    await page.click('[data-testid="book-now-button"]');
    await page.click('[data-testid="date-picker"]');
    await page.click('[data-testid="available-date"]:first-child');
    
    // 4. Sélectionner les options
    await page.click('[data-testid="room-type-select"]');
    await page.click('[data-testid="room-option"]:first-child');
    
    // 5. Procéder au paiement
    await page.click('[data-testid="proceed-payment"]');
    
    // 6. Remplir les informations de paiement (mode test)
    await page.fill('[data-testid="card-number"]', '****************');
    await page.fill('[data-testid="card-expiry"]', '12/25');
    await page.fill('[data-testid="card-cvc"]', '123');
    
    // 7. Confirmer la réservation
    await page.click('[data-testid="confirm-booking"]');
    
    // 8. Vérifier la confirmation
    await expect(page.locator('[data-testid="booking-confirmation"]')).toBeVisible();
    await expect(page.locator('[data-testid="booking-reference"]')).toBeVisible();
  });

  test('Annulation de réservation', async ({ page }) => {
    // Aller aux réservations
    await page.goto('/my-bookings');
    
    // Sélectionner une réservation
    await page.click('[data-testid="booking-item"]:first-child');
    
    // Annuler la réservation
    await page.click('[data-testid="cancel-booking"]');
    await page.click('[data-testid="confirm-cancellation"]');
    
    // Vérifier l'annulation
    await expect(page.locator('[data-testid="cancellation-confirmation"]')).toBeVisible();
  });
});
