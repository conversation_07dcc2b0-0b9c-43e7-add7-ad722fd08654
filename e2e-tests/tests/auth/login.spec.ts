import { test, expect } from '@playwright/test';

test.describe('Authentification', () => {
  test('Login utilisateur valide', async ({ page }) => {
    await page.goto('/login');
    
    // Remplir le formulaire de connexion
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    
    // Cliquer sur le bouton de connexion
    await page.click('[data-testid="login-button"]');
    
    // Vérifier la redirection vers le dashboard
    await expect(page).toHaveURL('/dashboard');
    
    // Vérifier la présence du menu utilisateur
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });

  test('Login avec identifiants invalides', async ({ page }) => {
    await page.goto('/login');
    
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');
    
    // Vérifier le message d'erreur
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Identifiants invalides');
  });

  test('Logout utilisateur', async ({ page }) => {
    // Se connecter d'abord
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    
    // Se déconnecter
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-button"]');
    
    // Vérifier la redirection vers la page d'accueil
    await expect(page).toHaveURL('/');
  });
});
