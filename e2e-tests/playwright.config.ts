import { defineConfig, devices } from '@playwright/test';
import * as dotenv from 'dotenv';

// Charger les variables d'environnement
dotenv.config();

/**
 * Configuration Playwright unifiée - Sprint 16
 * Tests E2E multi-browser pour Retreat And Be
 */
export default defineConfig({
  // Répertoire des tests
  testDir: './tests',
  
  // Timeout global
  timeout: 30000,
  
  // Timeout pour les assertions
  expect: {
    timeout: 5000,
  },
  
  // Nombre de tentatives en cas d'échec
  retries: process.env.CI ? 2 : 1,
  
  // Nombre de workers parallèles
  workers: process.env.CI ? 1 : undefined,
  
  // Reporter pour les résultats
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['json', { outputFile: 'test-results.json' }],
    ['junit', { outputFile: 'test-results.xml' }],
    ['list']
  ],
  
  // Configuration globale
  use: {
    // URL de base
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    
    // Timeout pour les actions
    actionTimeout: 10000,
    
    // Timeout pour la navigation
    navigationTimeout: 30000,
    
    // Capture d'écran en cas d'échec
    screenshot: 'only-on-failure',
    
    // Enregistrement vidéo
    video: 'retain-on-failure',
    
    // Trace en cas d'échec
    trace: 'retain-on-failure',
    
    // Headers par défaut
    extraHTTPHeaders: {
      'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
    },
  },

  // Projets de test (différents navigateurs)
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 }
      },
    },
    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        viewport: { width: 1920, height: 1080 }
      },
    },
    {
      name: 'webkit',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1920, height: 1080 }
      },
    },
    {
      name: 'mobile',
      use: { 
        ...devices['iPhone 13'],
      },
    },
    {
      name: 'tablet',
      use: { 
        ...devices['iPad Pro'],
      },
    },
  ],

  // Serveur de développement
  webServer: process.env.CI ? undefined : {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    cwd: '../Projet-RB2/Front-Audrey-V1-Main-main',
  },
});
